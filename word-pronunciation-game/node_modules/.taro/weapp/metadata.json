{"bundleHash": "071904ce", "taroRuntimeBundlePath": "node_modules/.taro/weapp/prebundle/chunk-4JFQ53LR.js", "mfHash": "64693ed5", "runtimeRequirements": ["__webpack_require__.O", "__webpack_require__", "webpackChunk", "__webpack_require__.d", "__webpack_require__.o", "__webpack_exports__", "__webpack_require__.e", "__webpack_require__.*", "module", "__webpack_require__.r", "__webpack_require__.g", "__webpack_require__.m (add only)", "__webpack_require__.f", "__webpack_require__.p", "__webpack_require__.l", "__webpack_require__.u"], "remoteAssets": [{"name": "prebundle/vendors-node_modules_taro_weapp_prebundle_react-dom_js.js"}, {"name": "prebundle/vendors-node_modules_taro_weapp_prebundle_chunk-4JFQ53LR_js.js"}, {"name": "prebundle/vendors-node_modules_taro_weapp_prebundle_chunk-XDFXK7K5_js.js"}, {"name": "prebundle/vendors-node_modules_taro_weapp_prebundle_chunk-7MJDXN2B_js.js"}, {"name": "prebundle/vendors-node_modules_taro_weapp_prebundle_tarojs_plugin-framework-react_dist_runtime_js.js"}, {"name": "prebundle/vendors-node_modules_taro_weapp_prebundle_tarojs_plugin-platform-weapp_dist_runtime_js.js"}, {"name": "prebundle/node_modules_taro_weapp_prebundle_tarojs_runtime_js.js"}, {"name": "prebundle/node_modules_taro_weapp_prebundle_tarojs_taro_js.js"}, {"name": "prebundle/remoteEntry.js"}, {"name": "prebundle/node_modules_taro_weapp_prebundle_react_jsx-runtime_js.js"}, {"name": "prebundle/node_modules_taro_weapp_prebundle_react_js.js"}]}