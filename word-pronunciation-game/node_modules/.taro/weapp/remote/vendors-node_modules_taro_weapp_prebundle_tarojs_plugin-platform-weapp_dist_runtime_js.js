"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["vendors-node_modules_taro_weapp_prebundle_tarojs_plugin-platform-weapp_dist_runtime_js"],{

/***/ "./node_modules/.taro/weapp/prebundle/@tarojs_plugin-platform-weapp_dist_runtime.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/@tarojs_plugin-platform-weapp_dist_runtime.js ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-7MJDXN2B.js */ "./node_modules/.taro/weapp/prebundle/chunk-7MJDXN2B.js");
/* harmony import */ var _chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-QRPWKJ4C.js */ "./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js");


// node_modules/@tarojs/plugin-platform-weapp/dist/runtime.js
(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_0__.init_dist)();
var needPromiseApis = /* @__PURE__ */ new Set([
    "addFileToFavorites",
    "addVideoToFavorites",
    "authPrivateMessage",
    "checkIsAddedToMyMiniProgram",
    "chooseContact",
    "cropImage",
    "disableAlertBeforeUnload",
    "editImage",
    "enableAlertBeforeUnload",
    "getBackgroundFetchData",
    "getChannelsLiveInfo",
    "getChannelsLiveNoticeInfo",
    "getFuzzyLocation",
    "getGroupEnterInfo",
    "getLocalIPAddress",
    "getShareInfo",
    "getUserProfile",
    "getWeRunData",
    "join1v1Chat",
    "openChannelsActivity",
    "openChannelsEvent",
    "openChannelsLive",
    "openChannelsUserProfile",
    "openCustomerServiceChat",
    "openVideoEditor",
    "saveFileToDisk",
    "scanItem",
    "setEnable1v1Chat",
    "setWindowSize",
    "sendBizRedPacket",
    "startFacialRecognitionVerify"
]);
function initNativeApi(taro) {
    (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_0__.processApis)(taro, wx, {
        needPromiseApis,
        modifyApis (apis) {
            apis.delete("lanDebug");
        },
        transformMeta (api, options) {
            var _a;
            if (api === "showShareMenu") {
                options.menus = (_a = options.showShareItems) === null || _a === void 0 ? void 0 : _a.map((item)=>item === "wechatFriends" ? "shareAppMessage" : item === "wechatMoment" ? "shareTimeline" : item);
            }
            return {
                key: api,
                options
            };
        }
    });
    taro.cloud = wx.cloud;
    taro.getTabBar = function(pageCtx) {
        var _a;
        if (typeof (pageCtx === null || pageCtx === void 0 ? void 0 : pageCtx.getTabBar) === "function") {
            return (_a = pageCtx.getTabBar()) === null || _a === void 0 ? void 0 : _a.$taroInstances;
        }
    };
    taro.getRenderer = function() {
        var _a, _b, _c;
        return (_c = (_b = (_a = taro.getCurrentInstance()) === null || _a === void 0 ? void 0 : _a.page) === null || _b === void 0 ? void 0 : _b.renderer) !== null && _c !== void 0 ? _c : "webview";
    };
}
var _true = "true";
var _false = "false";
var _empty = "";
var _zero = "0";
var _object = "{}";
var components = {
    // ======== 调整属性 ========
    Progress: {
        "border-radius": _zero,
        "font-size": "16",
        duration: "30",
        bindActiveEnd: _empty
    },
    RichText: {
        space: _empty,
        "user-select": _false,
        mode: "'default'"
    },
    Text: {
        "user-select": _false,
        overflow: "visible",
        "max-lines": ""
    },
    Map: {
        polygons: "[]",
        subkey: _empty,
        rotate: _zero,
        skew: _zero,
        "max-scale": "20",
        "min-scale": "3",
        "enable-3D": _false,
        "show-compass": _false,
        "show-scale": _false,
        "enable-overlooking": _false,
        "enable-auto-max-overlooking": _false,
        "enable-zoom": _true,
        "enable-scroll": _true,
        "enable-rotate": _false,
        "enable-satellite": _false,
        "enable-traffic": _false,
        "enable-poi": _true,
        "enable-building": _true,
        setting: _object,
        bindLabelTap: _empty,
        bindRegionChange: _empty,
        bindPoiTap: _empty,
        bindPolylineTap: _empty,
        bindAbilitySuccess: _empty,
        bindAbilityFailed: _empty,
        bindAuthSuccess: _empty,
        bindInterpolatePoint: _empty,
        bindError: _empty,
        bindAnchorPointTap: _empty
    },
    Button: {
        lang: "en",
        "session-from": _empty,
        "send-message-title": _empty,
        "send-message-path": _empty,
        "send-message-img": _empty,
        "app-parameter": _empty,
        "show-message-card": _false,
        "business-id": _empty,
        bindGetUserInfo: _empty,
        bindContact: _empty,
        bindGetPhoneNumber: _empty,
        bindGetRealTimePhoneNumber: _empty,
        bindChooseAvatar: _empty,
        bindError: _empty,
        bindOpenSetting: _empty,
        bindLaunchApp: _empty,
        bindAgreePrivacyAuthorization: _empty
    },
    Form: {
        "report-submit-timeout": _zero
    },
    Input: {
        "always-embed": _false,
        "adjust-position": _true,
        "hold-keyboard": _false,
        "safe-password-cert-path": "",
        "safe-password-length": "",
        "safe-password-time-stamp": "",
        "safe-password-nonce": "",
        "safe-password-salt": "",
        "safe-password-custom-hash": "",
        "auto-fill": _empty,
        "cursor-color": "",
        bindKeyboardHeightChange: _empty,
        bindNicknameReview: _empty,
        bindSelectionChange: _empty,
        bindKeyboardCompositionStart: _empty,
        bindKeyboardCompositionUpdate: _empty,
        bindKeyboardCompositionEnd: _empty
    },
    Picker: {
        "header-text": _empty,
        level: "region"
    },
    PickerView: {
        "immediate-change": _false,
        bindPickStart: _empty,
        bindPickEnd: _empty
    },
    Slider: {
        color: "'#e9e9e9'",
        "selected-color": "'#1aad19'"
    },
    Textarea: {
        "show-confirm-bar": _true,
        "adjust-position": _true,
        "hold-keyboard": _false,
        "disable-default-padding": _false,
        "confirm-type": "'return'",
        "confirm-hold": _false,
        "adjust-keyboard-to": "'cursor'",
        bindKeyboardHeightChange: _empty,
        bindSelectionChange: _empty,
        bindKeyboardCompositionStart: _empty,
        bindKeyboardCompositionUpdate: _empty,
        bindKeyboardCompositionEnd: _empty
    },
    ScrollView: {
        "enable-flex": _false,
        "scroll-anchoring": _false,
        enhanced: _false,
        "using-sticky": _false,
        "paging-enabled": _false,
        "enable-passive": _false,
        "refresher-enabled": _false,
        "refresher-threshold": "45",
        "refresher-default-style": "'black'",
        "refresher-background": "'#FFF'",
        "refresher-triggered": _false,
        bounces: _true,
        "show-scrollbar": _true,
        "fast-deceleration": _false,
        type: "'list'",
        "associative-container": "''",
        reverse: _false,
        clip: _true,
        "enable-back-to-top": _false,
        "cache-extent": _empty,
        "min-drag-distance": "18",
        "scroll-into-view-within-extent": _false,
        "scroll-into-view-alignment": "'start'",
        padding: "[0,0,0,0]",
        "refresher-two-level-enabled": _false,
        "refresher-two-level-triggered": _false,
        "refresher-two-level-threshold": "150",
        "refresher-two-level-close-threshold": "80",
        "refresher-two-level-scroll-enabled": _false,
        "refresher-ballistic-refresh-enabled": _false,
        "refresher-two-level-pinned": _false,
        bindDragStart: _empty,
        bindDragging: _empty,
        bindDragEnd: _empty,
        bindRefresherPulling: _empty,
        bindRefresherRefresh: _empty,
        bindRefresherRestore: _empty,
        bindRefresherAbort: _empty,
        bindScrollStart: _empty,
        bindScrollEnd: _empty,
        bindRefresherWillRefresh: _empty,
        bindRefresherStatusChange: _empty
    },
    StickySection: {
        "push-pinned-header": _true,
        padding: "[0, 0, 0, 0]"
    },
    GridView: {
        type: "'aligned'",
        "cross-axis-count": "2",
        "max-cross-axis-extent": _zero,
        "main-axis-gap": _zero,
        "cross-axis-gap": _zero,
        padding: "[0, 0, 0, 0]"
    },
    GridBuilder: {
        type: "'aligned'",
        list: "[]",
        "cross-axis-count": "2",
        "max-cross-axis-extent": _zero,
        "main-axis-gap": _zero,
        "cross-axis-gap": _zero,
        padding: "[0, 0, 0, 0]",
        bindItemBuild: _empty,
        bindItemDispose: _empty
    },
    ListView: {
        padding: "[0, 0, 0, 0]"
    },
    ListBuilder: {
        list: "[]",
        type: "static",
        padding: "[0, 0, 0, 0]",
        "child-count": _empty,
        "child-height": _empty,
        bindItemBuild: _empty,
        bindItemDispose: _empty
    },
    StickyHeader: {
        "offset-top": "0",
        padding: "[0, 0, 0, 0]"
    },
    Swiper: {
        "snap-to-edge": _false,
        "easing-function": "'default'",
        "layout-type": "'normal'",
        "transformer-type": "'scaleAndFade'",
        "indicator-type": "'normal'",
        "indicator-margin": "10",
        "indicator-spacing": "4",
        "indicator-radius": "4",
        "indicator-width": "8",
        "indicator-height": "8",
        "indicator-alignment": "'auto'",
        "indicator-offset": "[0, 0]",
        "scroll-with-animation": _true,
        "cache-extent": "0"
    },
    SwiperItem: {
        "skip-hidden-item-layout": _false
    },
    Navigator: {
        target: "'self'",
        "app-id": _empty,
        path: _empty,
        "extra-data": _empty,
        version: "'version'"
    },
    Camera: {
        mode: "'normal'",
        resolution: "'medium'",
        "frame-size": "'medium'",
        bindInitDone: _empty,
        bindScanCode: _empty
    },
    Image: {
        webp: _false,
        "show-menu-by-longpress": _false,
        "fade-in": _false
    },
    LivePlayer: {
        mode: "'live'",
        "sound-mode": "'speaker'",
        "auto-pause-if-navigate": _true,
        "auto-pause-if-open-native": _true,
        "picture-in-picture-mode": "[]",
        "enable-auto-rotation": _false,
        "referrer-policy": "'no-referrer'",
        "enable-casting": _false,
        bindstatechange: _empty,
        bindfullscreenchange: _empty,
        bindnetstatus: _empty,
        bindAudioVolumeNotify: _empty,
        bindEnterPictureInPicture: _empty,
        bindLeavePictureInPicture: _empty,
        bindCastingUserSelect: _empty,
        bindCastingStateChange: _empty,
        bindCastingInterrupt: _empty
    },
    Video: {
        title: _empty,
        "play-btn-position": "'bottom'",
        "enable-play-gesture": _false,
        "auto-pause-if-navigate": _true,
        "auto-pause-if-open-native": _true,
        "vslide-gesture": _false,
        "vslide-gesture-in-fullscreen": _true,
        "show-bottom-progress": _true,
        "ad-unit-id": _empty,
        "poster-for-crawler": _empty,
        "show-casting-button": _false,
        "picture-in-picture-mode": "[]",
        // picture-in-picture-show-progress 属性先注释掉的原因如下：
        // 该属性超过了 wxml 属性的长度限制，实际无法使用且导致编译报错。可等微信官方修复后再放开。
        // 参考1：https://developers.weixin.qq.com/community/develop/doc/000a429beb87f0eac07acc0fc5b400
        // 参考2: https://developers.weixin.qq.com/community/develop/doc/0006883619c48054286a4308258c00?_at=vyxqpllafi
        // 'picture-in-picture-show-progress': 'false',
        "enable-auto-rotation": _false,
        "show-screen-lock-button": _false,
        "show-snapshot-button": _false,
        "show-background-playback-button": _false,
        "background-poster": _empty,
        "referrer-policy": "'no-referrer'",
        "is-drm": _false,
        "is-live": _false,
        "provision-url": _empty,
        "certificate-url": _empty,
        "license-url": _empty,
        "preferred-peak-bit-rate": _empty,
        bindProgress: _empty,
        bindLoadedMetadata: _empty,
        bindControlsToggle: _empty,
        bindEnterPictureInPicture: _empty,
        bindLeavePictureInPicture: _empty,
        bindSeekComplete: _empty,
        bindCastingUserSelect: _empty,
        bindCastingStateChange: _empty,
        bindCastingInterrupt: _empty,
        bindAdLoad: _empty,
        bindAdError: _empty,
        bindAdClose: _empty,
        bindAdPlay: _empty
    },
    Canvas: {
        type: _empty
    },
    Ad: {
        "ad-type": "'banner'",
        "ad-theme": "'white'"
    },
    CoverView: {
        "marker-id": _empty,
        slot: _empty
    },
    // ======== 额外组件 ========
    Editor: {
        "read-only": _false,
        placeholder: _empty,
        "show-img-size": _false,
        "show-img-toolbar": _false,
        "show-img-resize": _false,
        focus: _false,
        bindReady: _empty,
        bindFocus: _empty,
        bindBlur: _empty,
        bindInput: _empty,
        bindStatusChange: _empty,
        name: _empty
    },
    MatchMedia: {
        "min-width": _empty,
        "max-width": _empty,
        width: _empty,
        "min-height": _empty,
        "max-height": _empty,
        height: _empty,
        orientation: _empty
    },
    FunctionalPageNavigator: {
        version: "'release'",
        name: _empty,
        args: _empty,
        bindSuccess: _empty,
        bindFail: _empty,
        bindCancel: _empty
    },
    LivePusher: {
        url: _empty,
        mode: "'RTC'",
        autopush: _false,
        muted: _false,
        "enable-camera": _true,
        "auto-focus": _true,
        orientation: "'vertical'",
        beauty: _zero,
        whiteness: _zero,
        aspect: "'9:16'",
        "min-bitrate": "200",
        "max-bitrate": "1000",
        "audio-quality": "'high'",
        "waiting-image": _empty,
        "waiting-image-hash": _empty,
        zoom: _false,
        "device-position": "'front'",
        "background-mute": _false,
        mirror: _false,
        "remote-mirror": _false,
        "local-mirror": _false,
        "audio-reverb-type": _zero,
        "enable-mic": _true,
        "enable-agc": _false,
        "enable-ans": _false,
        "audio-volume-type": "'voicecall'",
        "video-width": "360",
        "video-height": "640",
        "beauty-style": "'smooth'",
        filter: "'standard'",
        "picture-in-picture-mode": "[]",
        animation: _empty,
        bindStateChange: _empty,
        bindNetStatus: _empty,
        bindBgmStart: _empty,
        bindBgmProgress: _empty,
        bindBgmComplete: _empty,
        bindAudioVolumeNotify: _empty
    },
    OfficialAccount: {
        bindLoad: _empty,
        bindError: _empty
    },
    OpenData: {
        type: _empty,
        "open-gid": _empty,
        lang: "'en'",
        "default-text": _empty,
        "default-avatar": _empty,
        bindError: _empty
    },
    NavigationBar: {
        title: _empty,
        loading: _false,
        "front-color": "'#000000'",
        "background-color": _empty,
        "color-animation-duration": _zero,
        "color-animation-timing-func": "'linear'"
    },
    PageMeta: {
        "background-text-style": _empty,
        "background-color": _empty,
        "background-color-top": _empty,
        "background-color-bottom": _empty,
        "root-background-color": _empty,
        "scroll-top": "''",
        "scroll-duration": "300",
        "page-style": "''",
        "root-font-size": "''",
        "page-orientation": "''",
        bindResize: _empty,
        bindScroll: _empty,
        bindScrollDone: _empty
    },
    VoipRoom: {
        openid: _empty,
        mode: "'camera'",
        "device-position": "'front'",
        bindError: _empty
    },
    AdCustom: {
        "unit-id": _empty,
        "ad-intervals": _empty,
        bindLoad: _empty,
        bindError: _empty
    },
    PageContainer: {
        show: _false,
        duration: "300",
        "z-index": "100",
        overlay: _true,
        position: "'bottom'",
        round: _false,
        "close-on-slide-down": _false,
        "overlay-style": _empty,
        "custom-style": _empty,
        bindBeforeEnter: _empty,
        bindEnter: _empty,
        bindAfterEnter: _empty,
        bindBeforeLeave: _empty,
        bindLeave: _empty,
        bindAfterLeave: _empty,
        bindClickOverlay: _empty
    },
    ShareElement: {
        mapkey: _empty,
        transform: _false,
        duration: "300",
        "easing-function": "'ease-out'",
        "transition-on-gesture": _false,
        "shuttle-on-push": "'to'",
        "shuttle-on-pop": "'to'",
        "rect-tween-type": "'materialRectArc'"
    },
    KeyboardAccessory: {},
    RootPortal: {
        enable: _true
    },
    ChannelLive: {
        "feed-id": _empty,
        "finder-user-name": _empty
    },
    ChannelVideo: {
        "feed-id": _empty,
        "finder-user-name": _empty,
        "feed-token": _empty,
        autoplay: _false,
        loop: _false,
        muted: _false,
        "object-fit": "'contain'",
        bindError: _empty
    },
    Snapshot: {
        mode: "'view'"
    },
    Span: {},
    OpenContainer: {
        transitionType: "'fade'",
        transitionDuration: "300",
        closedColor: "'white'",
        closedElevation: _zero,
        closeBorderRadius: _zero,
        middleColor: _empty,
        openColor: "'white'",
        openElevation: _zero,
        openBorderRadius: _zero
    },
    DraggableSheet: {
        initialChildSize: "0.5",
        minChildSize: "0.25",
        maxChildSize: "1.0",
        snap: _false,
        snapSizes: "[]"
    },
    NestedScrollHeader: {},
    NestedScrollBody: {},
    // skyline手势组件
    DoubleTapGestureHandler: {},
    ForcePressGestureHandler: {},
    HorizontalDragGestureHandler: {},
    LongPressGestureHandler: {},
    PanGestureHandler: {},
    ScaleGestureHandler: {},
    TapGestureHandler: {},
    VerticalDragGestureHandler: {}
};
var hostConfig = {
    initNativeApi,
    getMiniLifecycle (config) {
        const methods = config.page[5];
        if (methods.indexOf("onSaveExitState") === -1) {
            methods.push("onSaveExitState");
        }
        return config;
    },
    transferHydrateData (data, element, componentsAlias) {
        var _a;
        if (element.isTransferElement) {
            const pages = getCurrentPages();
            const page = pages[pages.length - 1];
            data["nn"] = element.dataName;
            page.setData({
                [(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_0__.toCamelCase)(data.nn)]: data
            });
            return {
                sid: element.sid,
                ["v"]: "",
                ["nn"]: ((_a = componentsAlias["#text"]) === null || _a === void 0 ? void 0 : _a._num) || "8"
            };
        }
    }
};
(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_0__.mergeReconciler)(hostConfig);
(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_0__.mergeInternalComponents)(components);


/***/ })

}]);