{"name": "to-buffer", "version": "1.2.1", "description": "Pass in a string, array, Buffer, Data View, or Uint8Array, and get a Buffer back.", "main": "index.js", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*'", "test": "npm run tests-only", "posttest": "npx npm@\">= 10.2\" audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "https://github.com/browserify/to-buffer.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/browserify/to-buffer/issues"}, "homepage": "https://github.com/browserify/to-buffer", "dependencies": {"isarray": "^2.0.5", "safe-buffer": "^5.2.1", "typed-array-buffer": "^1.0.3"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.5", "npmignore": "^0.3.1", "nyc": "^10.3.2", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "publishConfig": {"ignore": [".github/workflows", "test"]}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}}