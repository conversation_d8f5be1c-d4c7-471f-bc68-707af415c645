# text-extensions

> List of text file extensions

The list is just a [JSON file](text-extensions.json) and can be used anywhere.

## Install

```
$ npm install text-extensions
```

## Usage

```js
const textExtensions = require('text-extensions');

console.log(textExtensions);
//=> ['asp', 'aspx', …]
```

## Related

- [is-text-path](https://github.com/sindresorhus/is-text-path) - Check if a file path is a text file
- [binary-extensions](https://github.com/sindresorhus/binary-extensions) - List of binary file extensions

---

<div align="center">
	<b>
		<a href="https://tidelift.com/subscription/pkg/npm-text-extensions?utm_source=npm-text-extensions&utm_medium=referral&utm_campaign=readme">Get professional support for this package with a Tidelift subscription</a>
	</b>
	<br>
	<sub>
		Tidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.
	</sub>
</div>
