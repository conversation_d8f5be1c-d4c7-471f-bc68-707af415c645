{"name": "thunky", "version": "1.1.0", "description": "delay the evaluation of a paramless async function and cache the result", "main": "index.js", "devDependencies": {"standard": "^12.0.1", "tape": "^4.9.1"}, "repository": {"type": "git", "url": "git://github.com/mafintosh/thunky.git"}, "scripts": {"test": "standard && tape test.js"}, "keywords": ["memo", "thunk", "async", "lazy", "control", "flow", "cache"], "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/mafintosh/thunky/issues"}, "homepage": "https://github.com/mafintosh/thunky#readme", "license": "MIT"}