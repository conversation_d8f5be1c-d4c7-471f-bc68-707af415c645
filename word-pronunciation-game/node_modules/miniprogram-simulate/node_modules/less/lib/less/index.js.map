{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/less/index.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAA0B;AAC1B,gDAA0B;AAC1B,0EAAoD;AACpD,8FAAsE;AACtE,gGAAwE;AACxE,wDAAkC;AAClC,2DAAqC;AACrC,0DAAoC;AACpC,wDAAkC;AAClC,0EAAkD;AAClD,4EAAoD;AACpD,4DAAqC;AACrC,oEAA6C;AAC7C,oDAA8B;AAC9B,kDAA4B;AAC5B,4DAAqC;AACrC,oEAA6C;AAC7C,6CAAiC;AACjC,oEAA6C;AAC7C,oDAA8B;AAE9B,mBAAe,UAAC,WAAW,EAAE,YAAY;IACrC;;;;;OAKG;IACH,WAAW,GAAG,IAAI,qBAAW,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IAEzD,IAAM,eAAe,GAAG,2BAAe,CAAC,WAAW,CAAC,CAAC;IACrD,IAAM,gBAAgB,GAAG,4BAAgB,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;IACxE,IAAM,SAAS,GAAG,oBAAS,CAAC,gBAAgB,CAAC,CAAC;IAC9C,IAAM,aAAa,GAAG,wBAAa,CAAC,WAAW,CAAC,CAAC;IACjD,IAAM,MAAM,GAAG,gBAAM,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAC7D,IAAM,KAAK,GAAG,eAAK,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAC3D,IAAM,SAAS,GAAG,mBAAS,CAAC,WAAW,CAAC,CAAC;IAEzC;;;;OAIG;IACH,IAAM,OAAO,GAAG;QACZ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACnB,IAAI,gBAAA;QACJ,IAAI,gBAAA;QACJ,WAAW,uBAAA;QACX,mBAAmB,iCAAA;QACnB,oBAAoB,kCAAA;QACpB,WAAW,aAAA;QACX,QAAQ,oBAAA;QACR,MAAM,kBAAA;QACN,SAAS,WAAA;QACT,QAAQ,oBAAA;QACR,eAAe,iBAAA;QACf,gBAAgB,kBAAA;QAChB,SAAS,WAAA;QACT,aAAa,eAAA;QACb,MAAM,QAAA;QACN,KAAK,OAAA;QACL,SAAS,sBAAA;QACT,aAAa,0BAAA;QACb,KAAK,OAAA;QACL,aAAa,0BAAA;QACb,MAAM,kBAAA;KACT,CAAC;IAEF,sBAAsB;IACtB,IAAM,IAAI,GAAG,UAAA,CAAC,IAAI,OAAA;QAAU,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QAC/B,YAAW,CAAC,YAAD,CAAC,2BAAI,IAAI,MAAE;IAC1B,CAAC,EAFiB,CAEjB,CAAC;IAEF,IAAI,CAAC,CAAC;IACN,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnC,KAAK,IAAM,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE;QAC1B,4BAA4B;QAC5B,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE;YACzB,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;SAClC;aACI;YACD,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC7B,KAAK,IAAM,CAAC,IAAI,CAAC,EAAE;gBACf,4BAA4B;gBAC5B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACxC;SACJ;KACJ;IAED;;;;;OAKG;IACH,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1C,OAAO,GAAG,CAAC;AACf,CAAC,EAAC", "sourcesContent": ["import data from './data';\nimport tree from './tree';\nimport Environment from './environment/environment';\nimport AbstractFileManager from './environment/abstract-file-manager';\nimport AbstractPluginLoader from './environment/abstract-plugin-loader';\nimport visitors from './visitors';\nimport Parser from './parser/parser';\nimport Functions from './functions';\nimport contexts from './contexts';\nimport sourceMapOutput from './source-map-output';\nimport sourceMapBuilder from './source-map-builder';\nimport parseTree from './parse-tree';\nimport importManager from './import-manager';\nimport Render from './render';\nimport Parse from './parse';\nimport LessError from './less-error';\nimport transformTree from './transform-tree';\nimport * as utils from './utils';\nimport PluginManager from './plugin-manager';\nimport logger from './logger';\n\nexport default (environment, fileManagers) => {\n    /**\n     * @todo\n     * This original code could be improved quite a bit.\n     * Many classes / modules currently add side-effects / mutations to passed in objects,\n     * which makes it hard to refactor and reason about. \n     */\n    environment = new Environment(environment, fileManagers);\n\n    const SourceMapOutput = sourceMapOutput(environment);\n    const SourceMapBuilder = sourceMapBuilder(SourceMapOutput, environment);\n    const ParseTree = parseTree(SourceMapBuilder);\n    const ImportManager = importManager(environment);\n    const render = Render(environment, ParseTree, ImportManager);\n    const parse = Parse(environment, ParseTree, ImportManager);\n    const functions = Functions(environment);\n\n    /**\n     * @todo\n     * This root properties / methods need to be organized.\n     * It's not clear what should / must be public and why.\n     */\n    const initial = {\n        version: [3, 13, 1],\n        data,\n        tree,\n        Environment,\n        AbstractFileManager,\n        AbstractPluginLoader,\n        environment,\n        visitors,\n        Parser,\n        functions,\n        contexts,\n        SourceMapOutput,\n        SourceMapBuilder,\n        ParseTree,\n        ImportManager,\n        render,\n        parse,\n        LessError,\n        transformTree,\n        utils,\n        PluginManager,\n        logger\n    };\n\n    // Create a public API\n    const ctor = t => function (...args) {\n        return new t(...args);\n    };\n\n    let t;\n    const api = Object.create(initial);\n    for (const n in initial.tree) {\n        /* eslint guard-for-in: 0 */\n        t = initial.tree[n];\n        if (typeof t === 'function') {\n            api[n.toLowerCase()] = ctor(t);\n        }\n        else {\n            api[n] = Object.create(null);\n            for (const o in t) {\n                /* eslint guard-for-in: 0 */\n                api[n][o.toLowerCase()] = ctor(t[o]);\n            }\n        }\n    }\n\n    /**\n     * Some of the functions assume a `this` context of the API object,\n     * which causes it to fail when wrapped for ES6 imports.\n     * \n     * An assumed `this` should be removed in the future.\n     */\n    initial.parse = initial.parse.bind(api);\n    initial.render = initial.render.bind(api);\n\n    return api;\n};\n"]}