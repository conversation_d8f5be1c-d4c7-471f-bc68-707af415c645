{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../../src/less/parse.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAkC;AAClC,2DAAqC;AACrC,oEAA6C;AAC7C,4DAAqC;AACrC,6CAAiC;AAEjC,mBAAe,UAAC,WAAW,EAAE,SAAS,EAAE,aAAa;IACjD,IAAM,KAAK,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE,QAAQ;QAE5C,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YAC/B,QAAQ,GAAG,OAAO,CAAC;YACnB,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACjD;aACI;YACD,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,QAAQ,EAAE;YACX,IAAM,MAAI,GAAG,IAAI,CAAC;YAClB,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gBAC/B,KAAK,CAAC,IAAI,CAAC,MAAI,EAAE,KAAK,EAAE,OAAO,EAAE,UAAC,GAAG,EAAE,MAAM;oBACzC,IAAI,GAAG,EAAE;wBACL,MAAM,CAAC,GAAG,CAAC,CAAC;qBACf;yBAAM;wBACH,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,SAAO,CAAC;YACZ,IAAI,YAAY,SAAA,CAAC;YACjB,IAAM,eAAa,GAAG,IAAI,wBAAa,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAE3E,OAAO,CAAC,aAAa,GAAG,eAAa,CAAC;YAEtC,SAAO,GAAG,IAAI,kBAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEtC,IAAI,OAAO,CAAC,YAAY,EAAE;gBACtB,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;aACvC;iBAAM;gBACH,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC;gBAC7C,IAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBACpD,YAAY,GAAG;oBACX,QAAQ,UAAA;oBACR,WAAW,EAAE,SAAO,CAAC,WAAW;oBAChC,QAAQ,EAAE,SAAO,CAAC,QAAQ,IAAI,EAAE;oBAChC,gBAAgB,EAAE,SAAS;oBAC3B,SAAS,WAAA;oBACT,YAAY,EAAE,QAAQ;iBACzB,CAAC;gBACF,kCAAkC;gBAClC,IAAI,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAClE,YAAY,CAAC,QAAQ,IAAI,GAAG,CAAC;iBAChC;aACJ;YAED,IAAM,SAAO,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,SAAO,EAAE,YAAY,CAAC,CAAC;YAC/D,IAAI,CAAC,aAAa,GAAG,SAAO,CAAC;YAE7B,8DAA8D;YAC9D,sCAAsC;YAEtC,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;oBAC1B,IAAI,UAAU,CAAC;oBACf,IAAI,QAAQ,CAAC;oBACb,IAAI,MAAM,CAAC,WAAW,EAAE;wBACpB,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;wBACrD,UAAU,GAAG,eAAa,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAO,EAAE,SAAO,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAC1G,IAAI,UAAU,YAAY,oBAAS,EAAE;4BACjC,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC;yBAC/B;qBACJ;yBACI;wBACD,eAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;qBACnC;gBACL,CAAC,CAAC,CAAC;aACN;YAED,IAAI,gBAAM,CAAC,SAAO,EAAE,SAAO,EAAE,YAAY,CAAC;iBACrC,KAAK,CAAC,KAAK,EAAE,UAAC,CAAC,EAAE,IAAI;gBAClB,IAAI,CAAC,EAAE;oBAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;iBAAE;gBAC9B,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,SAAO,EAAE,OAAO,CAAC,CAAC;YAC3C,CAAC,EAAE,OAAO,CAAC,CAAC;SACnB;IACL,CAAC,CAAC;IACF,OAAO,KAAK,CAAC;AACjB,CAAC,EAAC", "sourcesContent": ["import contexts from './contexts';\nimport Parser from './parser/parser';\nimport PluginManager from './plugin-manager';\nimport LessError from './less-error';\nimport * as utils from './utils';\n\nexport default (environment, ParseTree, ImportManager) => {\n    const parse = function (input, options, callback) {\n\n        if (typeof options === 'function') {\n            callback = options;\n            options = utils.copyOptions(this.options, {});\n        }\n        else {\n            options = utils.copyOptions(this.options, options || {});\n        }\n\n        if (!callback) {\n            const self = this;\n            return new Promise((resolve, reject) => {\n                parse.call(self, input, options, (err, output) => {\n                    if (err) {\n                        reject(err);\n                    } else {\n                        resolve(output);\n                    }\n                });\n            });\n        } else {\n            let context;\n            let rootFileInfo;\n            const pluginManager = new PluginManager(this, !options.reUsePluginManager);\n\n            options.pluginManager = pluginManager;\n\n            context = new contexts.Parse(options);\n\n            if (options.rootFileInfo) {\n                rootFileInfo = options.rootFileInfo;\n            } else {\n                const filename = options.filename || 'input';\n                const entryPath = filename.replace(/[^\\/\\\\]*$/, '');\n                rootFileInfo = {\n                    filename,\n                    rewriteUrls: context.rewriteUrls,\n                    rootpath: context.rootpath || '',\n                    currentDirectory: entryPath,\n                    entryPath,\n                    rootFilename: filename\n                };\n                // add in a missing trailing slash\n                if (rootFileInfo.rootpath && rootFileInfo.rootpath.slice(-1) !== '/') {\n                    rootFileInfo.rootpath += '/';\n                }\n            }\n\n            const imports = new ImportManager(this, context, rootFileInfo);\n            this.importManager = imports;\n\n            // TODO: allow the plugins to be just a list of paths or names\n            // Do an async plugin queue like lessc\n\n            if (options.plugins) {\n                options.plugins.forEach(plugin => {\n                    let evalResult;\n                    let contents;\n                    if (plugin.fileContent) {\n                        contents = plugin.fileContent.replace(/^\\uFEFF/, '');\n                        evalResult = pluginManager.Loader.evalPlugin(contents, context, imports, plugin.options, plugin.filename);\n                        if (evalResult instanceof LessError) {\n                            return callback(evalResult);\n                        }\n                    }\n                    else {\n                        pluginManager.addPlugin(plugin);\n                    }\n                });\n            }\n\n            new Parser(context, imports, rootFileInfo)\n                .parse(input, (e, root) => {\n                    if (e) { return callback(e); }\n                    callback(null, root, imports, options);\n                }, options);\n        }\n    };\n    return parse;\n};\n"]}