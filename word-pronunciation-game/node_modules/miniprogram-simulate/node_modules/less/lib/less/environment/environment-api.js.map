{"version": 3, "file": "environment-api.js", "sourceRoot": "", "sources": ["../../../src/less/environment/environment-api.js"], "names": [], "mappings": ";;AAAA,kBAAe;IACX;;;OAGG;IACH,YAAY,EAAE,UAAS,GAAG;IAC1B,CAAC;IACD;;;OAGG;IACH,UAAU,EAAE,UAAU,QAAQ;IAC9B,CAAC;IACD;;;OAGG;IACH,aAAa,EAAE,UAAU,IAAI;IAC7B,CAAC;IACD;;OAEG;IACH,qBAAqB,EAAE,SAAS,qBAAqB;IACrD,CAAC;CACJ,CAAC", "sourcesContent": ["export default {\n    /**\n     * Converts a string to a base 64 string\n     * @param str\n     */\n    encodeBase64: function(str) {\n    },\n    /**\n     * Lookup the mime-type of a filename\n     * @param filename\n     */\n    mimeLookup: function (filename) {\n    },\n    /**\n     * Look up the charset of a mime type\n     * @param mime\n     */\n    charsetLookup: function (mime) {\n    },\n    /**\n     * Gets a source map generator\n     */\n    getSourceMapGenerator: function getSourceMapGenerator() {\n    }\n};\n"]}