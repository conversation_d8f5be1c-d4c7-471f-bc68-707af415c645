{"version": 3, "file": "file-manager-api.js", "sourceRoot": "", "sources": ["../../../src/less/environment/file-manager-api.js"], "names": [], "mappings": ";;AAAA,kBAAe;IACX;;;;;OAKG;IACH,OAAO,EAAE,UAAS,QAAQ;IAC1B,CAAC;IACD;;;;;OAKG;IACH,sBAAsB,EAAE,UAAS,QAAQ;IACzC,CAAC;IACD;;;;;OAKG;IACH,uBAAuB,EAAE;IACzB,CAAC;IACD;;;;;OAKG;IACH,cAAc,EAAE,UAAS,IAAI;IAC7B,CAAC;IACD;;;;;OAKG;IACH,IAAI,EAAE,UAAS,QAAQ,EAAE,SAAS;IAClC,CAAC;IACD;;;;;;;;OAQG;IACH,QAAQ,EAAE,UAAS,GAAG,EAAE,OAAO;IAC/B,CAAC;IACD;;;;;;;;;OASG;IACH,YAAY,EAAE,UAAS,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;IACvE,CAAC;IACD;;;;;;;OAOG;IACH,QAAQ,EAAE,UAAS,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;IACnE,CAAC;IACD;;;;;;;;;;;OAWG;IACH,QAAQ,EAAE,UAAS,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;IACnE,CAAC;IACD;;;;;;;;;;;OAWG;IACH,YAAY,EAAE,UAAS,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;IACvE,CAAC;CACJ,CAAC", "sourcesContent": ["export default {\n    /**\n     * Given the full path to a file, return the path component\n     * Provided by AbstractFileManager\n     * @param {string} filename\n     * @returns {string}\n     */\n    getPath: function(filename) {\n    },\n    /**\n     * Append a .less extension if appropriate. Only called if less thinks one could be added.\n     * Provided by AbstractFileManager\n     * @param filename\n     * @returns {string}\n     */\n    tryAppendLessExtension: function(filename) {\n    },\n    /**\n     * Whether the rootpath should be converted to be absolute.\n     * The browser ovverides this to return true because urls must be absolute.\n     * Provided by AbstractFileManager (returns false)\n     * @returns {bool}\n     */\n    alwaysMakePathsAbsolute: function() {\n    },\n    /**\n     * Returns whether a path is absolute\n     * Provided by AbstractFileManager\n     * @param {string} path\n     * @returns {bool}\n     */\n    isPathAbsolute: function(path) {\n    },\n    /**\n     * joins together 2 paths\n     * Provided by AbstractFileManager\n     * @param {string} basePath\n     * @param {string} laterPath\n     */\n    join: function(basePath, laterPath) {\n    },\n    /**\n     * Returns the difference between 2 paths\n     * E.g. url = a/ baseUrl = a/b/ returns ../\n     * url = a/b/ baseUrl = a/ returns b/\n     * Provided by AbstractFileManager\n     * @param {string} url\n     * @param {string} baseUrl\n     * @returns {string}\n     */\n    pathDiff: function(url, baseUrl) {\n    },\n    /**\n     * Returns whether this file manager supports this file for syncronous file retrieval\n     * If true is returned, loadFileSync will then be called with the file.\n     * Provided by AbstractFileManager (returns false)\n     * @param {string} filename\n     * @param {string} currentDirectory\n     * @param {object} options\n     * @param {less.environment.environment} environment\n     * @returns {bool}\n     */\n    supportsSync: function(filename, currentDirectory, options, environment) {\n    },\n    /**\n     *\n     * @param {string} filename\n     * @param {string} currentDirectory\n     * @param {object} options\n     * @param {less.environment.environment} environment\n     * @returns {bool}\n     */\n    supports: function(filename, currentDirectory, options, environment) {\n    },\n    /**\n     * Loads a file asynchronously. Expects a promise that either rejects with an error or fulfills with an\n     * object containing\n     *  { filename: - full resolved path to file\n     *    contents: - the contents of the file, as a string }\n     *\n     * @param {string} filename\n     * @param {string} currentDirectory\n     * @param {object} options\n     * @param {less.environment.environment} environment\n     * @returns {Promise}\n     */\n    loadFile: function(filename, currentDirectory, options, environment) {\n    },\n    /**\n     * Loads a file synchronously. Expects an immediate return with an object containing\n     *  { error: - error object if an error occurs\n     *    filename: - full resolved path to file\n     *    contents: - the contents of the file, as a string }\n     *\n     * @param {string} filename\n     * @param {string} currentDirectory\n     * @param {object} options\n     * @param {less.environment.environment} environment\n     * @returns {object} should be an object containing error or contents and filename\n     */\n    loadFileSync: function(filename, currentDirectory, options, environment) {\n    }\n};\n"]}