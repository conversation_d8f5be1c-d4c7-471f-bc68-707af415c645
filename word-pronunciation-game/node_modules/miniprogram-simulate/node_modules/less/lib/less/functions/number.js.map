{"version": 3, "file": "number.js", "sourceRoot": "", "sources": ["../../../src/less/functions/number.js"], "names": [], "mappings": ";;;;;AAAA,gEAA0C;AAC1C,gEAA0C;AAC1C,oEAA0C;AAE1C,IAAM,MAAM,GAAG,UAAU,KAAK,EAAE,IAAI;IAChC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,QAAQ,IAAI,CAAC,MAAM,EAAE;QACjB,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;KACjF;IACD,IAAI,CAAC,CAAC,CAAC,2DAA2D;IAClE,IAAI,CAAC,CAAC;IACN,IAAI,OAAO,CAAC;IACZ,IAAI,cAAc,CAAC;IACnB,IAAI,gBAAgB,CAAC;IACrB,IAAI,IAAI,CAAC;IACT,IAAI,UAAU,CAAC;IACf,IAAI,SAAS,CAAC;IAEd,IAAM,gDAAgD;IAClD,KAAK,GAAI,EAAE,CAAC;IAEhB,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,2CAA2C;IAC3C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9B,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,IAAI,CAAC,CAAC,OAAO,YAAY,mBAAS,CAAC,EAAE;YACjC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC9B,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/E;YACD,SAAS;SACZ;QACD,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,mBAAS,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAC/I,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvH,UAAU,GAAG,IAAI,KAAK,EAAE,IAAI,UAAU,KAAK,SAAS,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC;QACnI,SAAS,GAAG,IAAI,KAAK,EAAE,IAAI,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACzF,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,KAAK,SAAS,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC/F,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,IAAI,UAAU,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,EAAE;gBACjD,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;aAC7D;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;YAC5B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpB,SAAS;SACZ;QACD,gBAAgB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,mBAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QACpJ,IAAK,KAAK,IAAI,cAAc,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK;YACvD,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,EAAE;YACzD,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;SACtB;KACJ;IACD,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;QACnB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;KACnB;IACD,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC1G,OAAO,IAAI,mBAAS,CAAC,CAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,UAAI,IAAI,MAAG,CAAC,CAAC;AAC9D,CAAC,CAAC;AAEF,kBAAe;IACX,GAAG,EAAE;QAAS,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACjB,OAAO,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IACD,GAAG,EAAE;QAAS,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACjB,OAAO,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IACD,OAAO,EAAE,UAAU,GAAG,EAAE,IAAI;QACxB,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IACD,EAAE,EAAE;QACA,OAAO,IAAI,mBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IACD,GAAG,EAAE,UAAS,CAAC,EAAE,CAAC;QACd,OAAO,IAAI,mBAAS,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IACD,GAAG,EAAE,UAAS,CAAC,EAAE,CAAC;QACd,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAChD,CAAC,GAAG,IAAI,mBAAS,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,GAAG,IAAI,mBAAS,CAAC,CAAC,CAAC,CAAC;SACxB;aAAM,IAAI,CAAC,CAAC,CAAC,YAAY,mBAAS,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,mBAAS,CAAC,EAAE;YAC/D,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;SACpE;QAED,OAAO,IAAI,mBAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,UAAU,EAAE,UAAU,CAAC;QACnB,IAAM,MAAM,GAAG,wBAAU,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,GAAG,GAAG,EAAT,CAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEpD,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ,CAAC", "sourcesContent": ["import Dimension from '../tree/dimension';\nimport Anonymous from '../tree/anonymous';\nimport mathHelper from './math-helper.js';\n\nconst minMax = function (isMin, args) {\n    args = Array.prototype.slice.call(args);\n    switch (args.length) {\n        case 0: throw { type: 'Argument', message: 'one or more arguments required' };\n    }\n    let i; // key is the unit.toString() for unified Dimension values,\n    let j;\n    let current;\n    let currentUnified;\n    let referenceUnified;\n    let unit;\n    let unitStatic;\n    let unitClone;\n\n    const // elems only contains original argument values.\n        order  = [];\n\n    const values = {};\n    // value is the index into the order array.\n    for (i = 0; i < args.length; i++) {\n        current = args[i];\n        if (!(current instanceof Dimension)) {\n            if (Array.isArray(args[i].value)) {\n                Array.prototype.push.apply(args, Array.prototype.slice.call(args[i].value));\n            }\n            continue;\n        }\n        currentUnified = current.unit.toString() === '' && unitClone !== undefined ? new Dimension(current.value, unitClone).unify() : current.unify();\n        unit = currentUnified.unit.toString() === '' && unitStatic !== undefined ? unitStatic : currentUnified.unit.toString();\n        unitStatic = unit !== '' && unitStatic === undefined || unit !== '' && order[0].unify().unit.toString() === '' ? unit : unitStatic;\n        unitClone = unit !== '' && unitClone === undefined ? current.unit.toString() : unitClone;\n        j = values[''] !== undefined && unit !== '' && unit === unitStatic ? values[''] : values[unit];\n        if (j === undefined) {\n            if (unitStatic !== undefined && unit !== unitStatic) {\n                throw { type: 'Argument', message: 'incompatible types' };\n            }\n            values[unit] = order.length;\n            order.push(current);\n            continue;\n        }\n        referenceUnified = order[j].unit.toString() === '' && unitClone !== undefined ? new Dimension(order[j].value, unitClone).unify() : order[j].unify();\n        if ( isMin && currentUnified.value < referenceUnified.value ||\n            !isMin && currentUnified.value > referenceUnified.value) {\n            order[j] = current;\n        }\n    }\n    if (order.length == 1) {\n        return order[0];\n    }\n    args = order.map(function (a) { return a.toCSS(this.context); }).join(this.context.compress ? ',' : ', ');\n    return new Anonymous(`${isMin ? 'min' : 'max'}(${args})`);\n};\n\nexport default {\n    min: function(...args) {\n        return minMax(true, args);\n    },\n    max: function(...args) {\n        return minMax(false, args);\n    },\n    convert: function (val, unit) {\n        return val.convertTo(unit.value);\n    },\n    pi: function () {\n        return new Dimension(Math.PI);\n    },\n    mod: function(a, b) {\n        return new Dimension(a.value % b.value, a.unit);\n    },\n    pow: function(x, y) {\n        if (typeof x === 'number' && typeof y === 'number') {\n            x = new Dimension(x);\n            y = new Dimension(y);\n        } else if (!(x instanceof Dimension) || !(y instanceof Dimension)) {\n            throw { type: 'Argument', message: 'arguments must be numbers' };\n        }\n\n        return new Dimension(Math.pow(x.value, y.value), x.unit);\n    },\n    percentage: function (n) {\n        const result = mathHelper(num => num * 100, '%', n);\n\n        return result;\n    }\n};\n"]}