{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/less/functions/index.js"], "names": [], "mappings": ";;;;;AAAA,0EAAmD;AACnD,sEAA+C;AAE/C,sDAAgC;AAChC,sDAAoC;AACpC,kDAA4B;AAC5B,oEAA6C;AAC7C,wDAAiC;AACjC,gDAA0B;AAC1B,gDAA0B;AAC1B,oDAA8B;AAC9B,oDAA8B;AAC9B,8CAAwB;AACxB,kDAA4B;AAE5B,mBAAe,UAAA,WAAW;IACtB,IAAM,SAAS,GAAG,EAAE,gBAAgB,6BAAA,EAAE,cAAc,2BAAA,EAAE,CAAC;IAEvD,qBAAqB;IACrB,2BAAgB,CAAC,WAAW,CAAC,iBAAO,CAAC,CAAC;IACtC,2BAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAW,CAAC,CAAC,CAAC;IACpE,2BAAgB,CAAC,WAAW,CAAC,eAAK,CAAC,CAAC;IACpC,2BAAgB,CAAC,WAAW,CAAC,wBAAa,CAAC,CAAC;IAC5C,2BAAgB,CAAC,WAAW,CAAC,kBAAO,CAAC,WAAW,CAAC,CAAC,CAAC;IACnD,2BAAgB,CAAC,WAAW,CAAC,cAAI,CAAC,CAAC;IACnC,2BAAgB,CAAC,WAAW,CAAC,cAAI,CAAC,CAAC;IACnC,2BAAgB,CAAC,WAAW,CAAC,gBAAM,CAAC,CAAC;IACrC,2BAAgB,CAAC,WAAW,CAAC,gBAAM,CAAC,CAAC;IACrC,2BAAgB,CAAC,WAAW,CAAC,aAAG,CAAC,WAAW,CAAC,CAAC,CAAC;IAC/C,2BAAgB,CAAC,WAAW,CAAC,eAAK,CAAC,CAAC;IAEpC,OAAO,SAAS,CAAC;AACrB,CAAC,EAAC", "sourcesContent": ["import functionRegistry from './function-registry';\nimport functionCaller from './function-caller';\n\nimport boolean from './boolean';\nimport defaultFunc from './default';\nimport color from './color';\nimport colorBlending from './color-blending';\nimport dataUri from './data-uri';\nimport list from './list';\nimport math from './math';\nimport number from './number';\nimport string from './string';\nimport svg from './svg';\nimport types from './types';\n\nexport default environment => {\n    const functions = { functionRegistry, functionCaller };\n\n    // register functions\n    functionRegistry.addMultiple(boolean);\n    functionRegistry.add('default', defaultFunc.eval.bind(defaultFunc));\n    functionRegistry.addMultiple(color);\n    functionRegistry.addMultiple(colorBlending);\n    functionRegistry.addMultiple(dataUri(environment));\n    functionRegistry.addMultiple(list);\n    functionRegistry.addMultiple(math);\n    functionRegistry.addMultiple(number);\n    functionRegistry.addMultiple(string);\n    functionRegistry.addMultiple(svg(environment));\n    functionRegistry.addMultiple(types);\n\n    return functions;\n};\n"]}