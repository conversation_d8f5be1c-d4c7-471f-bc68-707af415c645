{"version": 3, "file": "function-caller.js", "sourceRoot": "", "sources": ["../../../src/less/functions/function-caller.js"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,kEAA4C;AAE5C;IACI,wBAAY,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe;QAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QAEvC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAED,gCAAO,GAAP;QACI,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,6BAAI,GAAJ,UAAK,IAAI;QAAT,iBAqCC;QApCG,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpC,IAAI,QAAQ,KAAK,KAAK,EAAE;YACpB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,EAApB,CAAoB,CAAC,CAAC;SAC9C;QACD,oEAAoE;QACpE,8CAA8C;QAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACrB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,IAAI;gBACnB,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;oBACzB,OAAO,KAAK,CAAC;iBAChB;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC;iBACG,GAAG,CAAC,UAAA,IAAI;gBACL,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;oBAC5B,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI;wBACnC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;4BACzB,OAAO,KAAK,CAAC;yBAChB;wBACD,OAAO,IAAI,CAAC;oBAChB,CAAC,CAAC,CAAC;oBACH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;wBACvB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;qBACtB;yBAAM;wBACH,OAAO,IAAI,oBAAU,CAAC,QAAQ,CAAC,CAAC;qBACnC;iBACJ;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC,CAAC;SACV;QAED,IAAI,QAAQ,KAAK,KAAK,EAAE;YACpB,OAAO,IAAI,CAAC,IAAI,OAAT,IAAI,kBAAM,IAAI,CAAC,OAAO,GAAK,IAAI,GAAE;SAC3C;QAED,OAAO,IAAI,CAAC,IAAI,OAAT,IAAI,EAAS,IAAI,EAAE;IAC9B,CAAC;IACL,qBAAC;AAAD,CAAC,AApDD,IAoDC;AAED,kBAAe,cAAc,CAAC", "sourcesContent": ["import Expression from '../tree/expression';\n\nclass functionCaller {\n    constructor(name, context, index, currentFileInfo) {\n        this.name = name.toLowerCase();\n        this.index = index;\n        this.context = context;\n        this.currentFileInfo = currentFileInfo;\n\n        this.func = context.frames[0].functionRegistry.get(this.name);\n    }\n\n    isValid() {\n        return Boolean(this.func);\n    }\n\n    call(args) {\n        const evalArgs = this.func.evalArgs;\n        if (evalArgs !== false) {\n            args = args.map(a => a.eval(this.context));\n        }\n        // This code is terrible and should be replaced as per this issue...\n        // https://github.com/less/less.js/issues/2477\n        if (Array.isArray(args)) {\n            args = args.filter(item => {\n                if (item.type === 'Comment') {\n                    return false;\n                }\n                return true;\n            })\n                .map(item => {\n                    if (item.type === 'Expression') {\n                        const subNodes = item.value.filter(item => {\n                            if (item.type === 'Comment') {\n                                return false;\n                            }\n                            return true;\n                        });\n                        if (subNodes.length === 1) {\n                            return subNodes[0];\n                        } else {\n                            return new Expression(subNodes);\n                        }\n                    }\n                    return item;\n                });\n        }\n\n        if (evalArgs === false) {\n            return this.func(this.context, ...args);\n        }\n\n        return this.func(...args);\n    }\n}\n\nexport default functionCaller;\n"]}