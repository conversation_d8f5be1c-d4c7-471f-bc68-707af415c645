{"version": 3, "file": "function-registry.js", "sourceRoot": "", "sources": ["../../../src/less/functions/function-registry.js"], "names": [], "mappings": ";;AAAA,SAAS,YAAY,CAAE,IAAI;IACvB,OAAO;QACH,KAAK,EAAE,EAAE;QACT,GAAG,EAAE,UAAS,IAAI,EAAE,IAAI;YACpB,sDAAsD;YACtD,2DAA2D;YAC3D,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAE1B,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBACjC,YAAY;aACf;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC5B,CAAC;QACD,WAAW,EAAE,UAAS,SAAS;YAAlB,iBAKZ;YAJG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAC1B,UAAA,IAAI;gBACA,KAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACX,CAAC;QACD,GAAG,EAAE,UAAS,IAAI;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE,IAAI,IAAI,IAAI,CAAC,GAAG,CAAE,IAAI,CAAE,CAAC,CAAC;QAC3D,CAAC;QACD,iBAAiB,EAAE;YACf,OAAO,IAAI,CAAC,KAAK,CAAC;QACtB,CAAC;QACD,OAAO,EAAE;YACL,OAAO,YAAY,CAAE,IAAI,CAAE,CAAC;QAChC,CAAC;QACD,MAAM,EAAE,UAAS,IAAI;YACjB,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;KACJ,CAAC;AACN,CAAC;AAED,kBAAe,YAAY,CAAE,IAAI,CAAE,CAAC", "sourcesContent": ["function makeRegistry( base ) {\n    return {\n        _data: {},\n        add: function(name, func) {\n            // precautionary case conversion, as later querying of\n            // the registry by function-caller uses lower case as well.\n            name = name.toLowerCase();\n\n            if (this._data.hasOwnProperty(name)) {\n                // TODO warn\n            }\n            this._data[name] = func;\n        },\n        addMultiple: function(functions) {\n            Object.keys(functions).forEach(\n                name => {\n                    this.add(name, functions[name]);\n                });\n        },\n        get: function(name) {\n            return this._data[name] || ( base && base.get( name ));\n        },\n        getLocalFunctions: function() {\n            return this._data;\n        },\n        inherit: function() {\n            return makeRegistry( this );\n        },\n        create: function(base) {\n            return makeRegistry(base);\n        }\n    };\n}\n\nexport default makeRegistry( null );"]}