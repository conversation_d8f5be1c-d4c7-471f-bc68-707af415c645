{"version": 3, "file": "color-blending.js", "sourceRoot": "", "sources": ["../../../src/less/functions/color-blending.js"], "names": [], "mappings": ";;;;;AAAA,wDAAkC;AAElC,iBAAiB;AACjB,0CAA0C;AAE1C,SAAS,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM;IACpC,IAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAQ,SAAS;IAEzC,IAAI,WAAW;IACX,EAAE,CAAC;IAEP,IAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;IAExB,IAAI,SAAS;IACT,EAAE,CAAC;IAEP,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,IAAM,CAAC,GAAG,EAAE,CAAC;IAEb,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QACzB,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QACzB,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,IAAI,EAAE,EAAE;YACJ,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;gBAClB,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;SACpC;QACD,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;KACnB;IAED,OAAO,IAAI,eAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC5B,CAAC;AAED,IAAM,uBAAuB,GAAG;IAC5B,QAAQ,EAAE,UAAS,EAAE,EAAE,EAAE;QACrB,OAAO,EAAE,GAAG,EAAE,CAAC;IACnB,CAAC;IACD,MAAM,EAAE,UAAS,EAAE,EAAE,EAAE;QACnB,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IAC7B,CAAC;IACD,OAAO,EAAE,UAAS,EAAE,EAAE,EAAE;QACpB,EAAE,IAAI,CAAC,CAAC;QACR,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YACd,uBAAuB,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1C,uBAAuB,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACnD,CAAC;IACD,SAAS,EAAE,UAAS,EAAE,EAAE,EAAE;QACtB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,EAAE,GAAG,GAAG,EAAE;YACV,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;SACxC;QACD,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC5C,CAAC;IACD,SAAS,EAAE,UAAS,EAAE,EAAE,EAAE;QACtB,OAAO,uBAAuB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACnD,CAAC;IACD,UAAU,EAAE,UAAS,EAAE,EAAE,EAAE;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7B,CAAC;IACD,SAAS,EAAE,UAAS,EAAE,EAAE,EAAE;QACtB,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IACjC,CAAC;IAED,qBAAqB;IACrB,OAAO,EAAE,UAAS,EAAE,EAAE,EAAE;QACpB,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IACD,QAAQ,EAAE,UAAS,EAAE,EAAE,EAAE;QACrB,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;CACJ,CAAC;AAEF,KAAK,IAAM,CAAC,IAAI,uBAAuB,EAAE;IACrC,IAAI,uBAAuB,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;QAC3C,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;KACrE;CACJ;AAED,kBAAe,UAAU,CAAC", "sourcesContent": ["import Color from '../tree/color';\n\n// Color Blending\n// ref: http://www.w3.org/TR/compositing-1\n\nfunction colorBlend(mode, color1, color2) {\n    const ab = color1.alpha;        // result\n\n    let // backdrop\n        cb;\n\n    const as = color2.alpha;\n\n    let // source\n        cs;\n\n    let ar;\n    let cr;\n    const r = [];\n\n    ar = as + ab * (1 - as);\n    for (let i = 0; i < 3; i++) {\n        cb = color1.rgb[i] / 255;\n        cs = color2.rgb[i] / 255;\n        cr = mode(cb, cs);\n        if (ar) {\n            cr = (as * cs + ab * (cb -\n                  as * (cb + cs - cr))) / ar;\n        }\n        r[i] = cr * 255;\n    }\n\n    return new Color(r, ar);\n}\n\nconst colorBlendModeFunctions = {\n    multiply: function(cb, cs) {\n        return cb * cs;\n    },\n    screen: function(cb, cs) {\n        return cb + cs - cb * cs;\n    },\n    overlay: function(cb, cs) {\n        cb *= 2;\n        return (cb <= 1) ?\n            colorBlendModeFunctions.multiply(cb, cs) :\n            colorBlendModeFunctions.screen(cb - 1, cs);\n    },\n    softlight: function(cb, cs) {\n        let d = 1;\n        let e = cb;\n        if (cs > 0.5) {\n            e = 1;\n            d = (cb > 0.25) ? Math.sqrt(cb)\n                : ((16 * cb - 12) * cb + 4) * cb;\n        }\n        return cb - (1 - 2 * cs) * e * (d - cb);\n    },\n    hardlight: function(cb, cs) {\n        return colorBlendModeFunctions.overlay(cs, cb);\n    },\n    difference: function(cb, cs) {\n        return Math.abs(cb - cs);\n    },\n    exclusion: function(cb, cs) {\n        return cb + cs - 2 * cb * cs;\n    },\n\n    // non-w3c functions:\n    average: function(cb, cs) {\n        return (cb + cs) / 2;\n    },\n    negation: function(cb, cs) {\n        return 1 - Math.abs(cb + cs - 1);\n    }\n};\n\nfor (const f in colorBlendModeFunctions) {\n    if (colorBlendModeFunctions.hasOwnProperty(f)) {\n        colorBlend[f] = colorBlend.bind(null, colorBlendModeFunctions[f]);\n    }\n}\n\nexport default colorBlend;\n"]}