{"version": 3, "file": "paren.js", "sourceRoot": "", "sources": ["../../../src/less/tree/paren.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAE1B,IAAM,KAAK,GAAG,UAAS,IAAI;IACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAE7B,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAC7C,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACnC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,CAAC,CAAA;AAED,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACnC,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC;AAC/B,kBAAe,KAAK,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Paren = function(node) {\n    this.value = node;\n};\n\nParen.prototype = new Node();\n\nParen.prototype.genCSS = function(context, output) {\n    output.add('(');\n    this.value.genCSS(context, output);\n    output.add(')');\n}\n\nParen.prototype.eval = function(context) {\n    return new Paren(this.value.eval(context));\n};\n\nParen.prototype.type = 'Paren';\nexport default Paren;\n"]}