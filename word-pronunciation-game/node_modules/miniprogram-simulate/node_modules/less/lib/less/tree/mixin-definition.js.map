{"version": 3, "file": "mixin-definition.js", "sourceRoot": "", "sources": ["../../../src/less/tree/mixin-definition.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAkC;AAClC,sDAAgC;AAChC,sDAAgC;AAChC,8DAAwC;AACxC,wEAAiD;AACjD,4DAAsC;AACtC,yDAAmC;AACnC,8CAAkC;AAElC,IAAM,UAAU,GAAG,UAAS,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc;IACxF,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,iBAAiB,CAAC;IACtC,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,CAAC,IAAI,iBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/F,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACnB,IAAM,kBAAkB,GAAG,EAAE,CAAC;IAC9B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACjC,OAAO,KAAK,GAAG,CAAC,CAAC;SACpB;aACI;YACD,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO,KAAK,CAAC;SAChB;IACL,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC7C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,GAAG,IAAI,iBAAO,EAAE,CAAC;AAErC,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IAC1C,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;QACnC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACjD;IACD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAClD;AACL,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,UAAS,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc;IAC9E,sBAAsB;IACtB,IAAM,KAAK,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAEtC,IAAI,OAAO,CAAC;IACZ,IAAI,GAAG,CAAC;IACR,IAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,IAAI,GAAG,CAAC;IACR,IAAI,IAAI,CAAC;IACT,IAAI,YAAY,CAAC;IACjB,IAAI,QAAQ,CAAC;IACb,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE;QAC9E,KAAK,CAAC,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;KAC1E;IACD,QAAQ,GAAG,IAAI,kBAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAExE,IAAI,IAAI,EAAE;QACN,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC7B,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YAC7B,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACd,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC1B,YAAY,GAAG,KAAK,CAAC;gBACrB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAChC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;wBAC/C,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBAC5C,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBAClE,YAAY,GAAG,IAAI,CAAC;wBACpB,MAAM;qBACT;iBACJ;gBACD,IAAI,YAAY,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClB,CAAC,EAAE,CAAC;oBACJ,SAAS;iBACZ;qBAAM;oBACH,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,wBAAsB,IAAI,CAAC,IAAI,SAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,eAAY,EAAE,CAAC;iBACnG;aACJ;SACJ;KACJ;IACD,QAAQ,GAAG,CAAC,CAAC;IACb,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAChC,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE;YAAE,SAAS;SAAE;QAEpC,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7B,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACvB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;gBACpB,OAAO,GAAG,EAAE,CAAC;gBACb,KAAK,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;oBACpC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC7C;gBACD,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,IAAI,EAAE,IAAI,oBAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACnF;iBAAM;gBACH,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC;gBACvB,IAAI,GAAG,EAAE;oBACL,yEAAyE;oBACzE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;wBACpB,GAAG,GAAG,IAAI,0BAAe,CAAC,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;qBACnD;yBACI;wBACD,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC3B;iBACJ;qBAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;oBACxB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACrC,KAAK,CAAC,UAAU,EAAE,CAAC;iBACtB;qBAAM;oBACH,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,mCAAiC,IAAI,CAAC,IAAI,UAAK,UAAU,aAAQ,IAAI,CAAC,KAAK,MAAG,EAAE,CAAC;iBACtH;gBAED,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC9C,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;aAC3B;SACJ;QAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC5B,KAAK,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACpC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACnD;SACJ;QACD,QAAQ,EAAE,CAAC;KACd;IAED,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG;IACjC,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC;QACrD,IAAI,CAAC,CAAC,aAAa,EAAE;YACjB,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAChC;aAAM;YACH,OAAO,CAAC,CAAC;SACZ;IACL,CAAC,CAAC,CAAC;IACH,IAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACzG,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACxC,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7I,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAS,OAAO,EAAE,IAAI,EAAE,SAAS;IAC7D,IAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IACtF,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAClG,IAAI,KAAK,CAAC;IACV,IAAI,OAAO,CAAC;IAEZ,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,YAAY,EAAE,IAAI,oBAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE3F,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEpC,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACnC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;IAC/B,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACtF,IAAI,SAAS,EAAE;QACX,OAAO,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;KACrC;IACD,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,cAAc,GAAG,UAAS,IAAI,EAAE,OAAO;IACxD,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CACtC,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EACrB,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,6BAA6B,CACnD,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;SACxG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,oCAAoC;SAC9D,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,iCAAiC;QACtE,OAAO,KAAK,CAAC;KAChB;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,SAAS,GAAG,UAAS,IAAI,EAAE,OAAO;IACnD,IAAM,UAAU,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI,GAAG,CAAC;IACR,IAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;IACnD,IAAM,eAAe,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,CAAC;QACrD,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACxC,OAAO,KAAK,GAAG,CAAC,CAAC;SACpB;aAAM;YACH,OAAO,KAAK,CAAC;SAChB;IACL,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAChB,IAAI,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;KACJ;SAAM;QACH,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE;YACvC,OAAO,KAAK,CAAC;SAChB;KACJ;IAED,iBAAiB;IACjB,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;YAClD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACnF,OAAO,KAAK,CAAC;aAChB;SACJ;KACJ;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC9C,UAAU,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;AACtC,kBAAe,UAAU,CAAC", "sourcesContent": ["import Selector from './selector';\nimport Element from './element';\nimport Ruleset from './ruleset';\nimport Declaration from './declaration';\nimport DetachedRuleset from './detached-ruleset';\nimport Expression from './expression';\nimport contexts from '../contexts';\nimport * as utils from '../utils';\n\nconst Definition = function(name, params, rules, condition, variadic, frames, visibilityInfo) {\n    this.name = name || 'anonymous mixin';\n    this.selectors = [new Selector([new Element(null, name, false, this._index, this._fileInfo)])];\n    this.params = params;\n    this.condition = condition;\n    this.variadic = variadic;\n    this.arity = params.length;\n    this.rules = rules;\n    this._lookups = {};\n    const optionalParameters = [];\n    this.required = params.reduce((count, p) => {\n        if (!p.name || (p.name && !p.value)) {\n            return count + 1;\n        }\n        else {\n            optionalParameters.push(p.name);\n            return count;\n        }\n    }, 0);\n    this.optionalParameters = optionalParameters;\n    this.frames = frames;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n};\n\nDefinition.prototype = new Ruleset();\n\nDefinition.prototype.accept = function(visitor) {\n    if (this.params && this.params.length) {\n        this.params = visitor.visitArray(this.params);\n    }\n    this.rules = visitor.visitArray(this.rules);\n    if (this.condition) {\n        this.condition = visitor.visit(this.condition);\n    }\n};\n\nDefinition.prototype.evalParams = function(context, mixinEnv, args, evaldArguments) {\n    /* jshint boss:true */\n    const frame = new Ruleset(null, null);\n\n    let varargs;\n    let arg;\n    const params = utils.copyArray(this.params);\n    let i;\n    let j;\n    let val;\n    let name;\n    let isNamedFound;\n    let argIndex;\n    let argsLength = 0;\n\n    if (mixinEnv.frames && mixinEnv.frames[0] && mixinEnv.frames[0].functionRegistry) {\n        frame.functionRegistry = mixinEnv.frames[0].functionRegistry.inherit();\n    }\n    mixinEnv = new contexts.Eval(mixinEnv, [frame].concat(mixinEnv.frames));\n\n    if (args) {\n        args = utils.copyArray(args);\n        argsLength = args.length;\n\n        for (i = 0; i < argsLength; i++) {\n            arg = args[i];\n            if (name = (arg && arg.name)) {\n                isNamedFound = false;\n                for (j = 0; j < params.length; j++) {\n                    if (!evaldArguments[j] && name === params[j].name) {\n                        evaldArguments[j] = arg.value.eval(context);\n                        frame.prependRule(new Declaration(name, arg.value.eval(context)));\n                        isNamedFound = true;\n                        break;\n                    }\n                }\n                if (isNamedFound) {\n                    args.splice(i, 1);\n                    i--;\n                    continue;\n                } else {\n                    throw { type: 'Runtime', message: `Named argument for ${this.name} ${args[i].name} not found` };\n                }\n            }\n        }\n    }\n    argIndex = 0;\n    for (i = 0; i < params.length; i++) {\n        if (evaldArguments[i]) { continue; }\n\n        arg = args && args[argIndex];\n\n        if (name = params[i].name) {\n            if (params[i].variadic) {\n                varargs = [];\n                for (j = argIndex; j < argsLength; j++) {\n                    varargs.push(args[j].value.eval(context));\n                }\n                frame.prependRule(new Declaration(name, new Expression(varargs).eval(context)));\n            } else {\n                val = arg && arg.value;\n                if (val) {\n                    // This was a mixin call, pass in a detached ruleset of it's eval'd rules\n                    if (Array.isArray(val)) {\n                        val = new DetachedRuleset(new Ruleset('', val));\n                    }\n                    else {\n                        val = val.eval(context);\n                    }\n                } else if (params[i].value) {\n                    val = params[i].value.eval(mixinEnv);\n                    frame.resetCache();\n                } else {\n                    throw { type: 'Runtime', message: `wrong number of arguments for ${this.name} (${argsLength} for ${this.arity})` };\n                }\n\n                frame.prependRule(new Declaration(name, val));\n                evaldArguments[i] = val;\n            }\n        }\n\n        if (params[i].variadic && args) {\n            for (j = argIndex; j < argsLength; j++) {\n                evaldArguments[j] = args[j].value.eval(context);\n            }\n        }\n        argIndex++;\n    }\n\n    return frame;\n};\n\nDefinition.prototype.makeImportant = function() {\n    const rules = !this.rules ? this.rules : this.rules.map(r => {\n        if (r.makeImportant) {\n            return r.makeImportant(true);\n        } else {\n            return r;\n        }\n    });\n    const result = new Definition(this.name, this.params, rules, this.condition, this.variadic, this.frames);\n    return result;\n};\n\nDefinition.prototype.eval = function(context) {\n    return new Definition(this.name, this.params, this.rules, this.condition, this.variadic, this.frames || utils.copyArray(context.frames));\n};\n\nDefinition.prototype.evalCall = function(context, args, important) {\n    const _arguments = [];\n    const mixinFrames = this.frames ? this.frames.concat(context.frames) : context.frames;\n    const frame = this.evalParams(context, new contexts.Eval(context, mixinFrames), args, _arguments);\n    let rules;\n    let ruleset;\n\n    frame.prependRule(new Declaration('@arguments', new Expression(_arguments).eval(context)));\n\n    rules = utils.copyArray(this.rules);\n\n    ruleset = new Ruleset(null, rules);\n    ruleset.originalRuleset = this;\n    ruleset = ruleset.eval(new contexts.Eval(context, [this, frame].concat(mixinFrames)));\n    if (important) {\n        ruleset = ruleset.makeImportant();\n    }\n    return ruleset;\n};\n\nDefinition.prototype.matchCondition = function(args, context) {\n    if (this.condition && !this.condition.eval(\n        new contexts.Eval(context,\n            [this.evalParams(context, /* the parameter variables */\n                new contexts.Eval(context, this.frames ? this.frames.concat(context.frames) : context.frames), args, [])]\n                .concat(this.frames || []) // the parent namespace/mixin frames\n                .concat(context.frames)))) { // the current environment frames\n        return false;\n    }\n    return true;\n};\n\nDefinition.prototype.matchArgs = function(args, context) {\n    const allArgsCnt = (args && args.length) || 0;\n    let len;\n    const optionalParameters = this.optionalParameters;\n    const requiredArgsCnt = !args ? 0 : args.reduce((count, p) => {\n        if (optionalParameters.indexOf(p.name) < 0) {\n            return count + 1;\n        } else {\n            return count;\n        }\n    }, 0);\n\n    if (!this.variadic) {\n        if (requiredArgsCnt < this.required) {\n            return false;\n        }\n        if (allArgsCnt > this.params.length) {\n            return false;\n        }\n    } else {\n        if (requiredArgsCnt < (this.required - 1)) {\n            return false;\n        }\n    }\n\n    // check patterns\n    len = Math.min(requiredArgsCnt, this.arity);\n\n    for (let i = 0; i < len; i++) {\n        if (!this.params[i].name && !this.params[i].variadic) {\n            if (args[i].value.eval(context).toCSS() != this.params[i].value.eval(context).toCSS()) {\n                return false;\n            }\n        }\n    }\n    return true;\n};\n\nDefinition.prototype.type = 'MixinDefinition';\nDefinition.prototype.evalFirst = true;\nexport default Definition;\n"]}