{"version": 3, "file": "quoted.js", "sourceRoot": "", "sources": ["../../../src/less/tree/quoted.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,wDAAkC;AAClC,wDAAkC;AAElC,IAAM,MAAM,GAAG,UAAS,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe;IACjE,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;IAClD,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,EAAE,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC;IACtC,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC;IACnC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;AAC7B,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAE9B,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAC9C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACf,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC5D;IACD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACf,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC1B;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,iBAAiB,GAAG;IACjC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACpC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,IAAM,mBAAmB,GAAG,UAAC,CAAC,EAAE,IAAI;QAChC,IAAM,CAAC,GAAG,IAAI,kBAAQ,CAAC,MAAI,IAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzF,OAAO,CAAC,CAAC,YAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IACvD,CAAC,CAAC;IACF,IAAM,mBAAmB,GAAG,UAAC,CAAC,EAAE,IAAI;QAChC,IAAM,CAAC,GAAG,IAAI,kBAAQ,CAAC,MAAI,IAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzF,OAAO,CAAC,CAAC,YAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IACvD,CAAC,CAAC;IACF,SAAS,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc;QACnD,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,GAAG;YACC,KAAK,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;YAClC,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;SAC1D,QAAQ,KAAK,KAAK,cAAc,EAAE;QACnC,OAAO,cAAc,CAAC;IAC1B,CAAC;IACD,KAAK,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;IACzE,KAAK,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;IAErE,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9G,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,UAAS,KAAK;IACrC,0DAA0D;IAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;QAC5D,OAAO,cAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;KACvD;SAAM;QACH,OAAO,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;KACxE;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC;AACjC,kBAAe,MAAM,CAAC", "sourcesContent": ["import Node from './node';\nimport Variable from './variable';\nimport Property from './property';\n\nconst Quoted = function(str, content, escaped, index, currentFileInfo) {\n    this.escaped = (escaped == null) ? true : escaped;\n    this.value = content || '';\n    this.quote = str.charAt(0);\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.variableRegex = /@\\{([\\w-]+)\\}/g;\n    this.propRegex = /\\$\\{([\\w-]+)\\}/g;\n    this.allowRoot = escaped;\n};\n\nQuoted.prototype = new Node();\n\nQuoted.prototype.genCSS = function(context, output) {\n    if (!this.escaped) {\n        output.add(this.quote, this.fileInfo(), this.getIndex());\n    }\n    output.add(this.value);\n    if (!this.escaped) {\n        output.add(this.quote);\n    }\n};\n\nQuoted.prototype.containsVariables = function() {\n    return this.value.match(this.variableRegex);\n};\n\nQuoted.prototype.eval = function(context) {\n    const that = this;\n    let value = this.value;\n    const variableReplacement = (_, name) => {\n        const v = new Variable(`@${name}`, that.getIndex(), that.fileInfo()).eval(context, true);\n        return (v instanceof Quoted) ? v.value : v.toCSS();\n    };\n    const propertyReplacement = (_, name) => {\n        const v = new Property(`$${name}`, that.getIndex(), that.fileInfo()).eval(context, true);\n        return (v instanceof Quoted) ? v.value : v.toCSS();\n    };\n    function iterativeReplace(value, regexp, replacementFnc) {\n        let evaluatedValue = value;\n        do {\n            value = evaluatedValue.toString();\n            evaluatedValue = value.replace(regexp, replacementFnc);\n        } while (value !== evaluatedValue);\n        return evaluatedValue;\n    }\n    value = iterativeReplace(value, this.variableRegex, variableReplacement);\n    value = iterativeReplace(value, this.propRegex, propertyReplacement);\n\n    return new Quoted(this.quote + value + this.quote, value, this.escaped, this.getIndex(), this.fileInfo());\n};\n\nQuoted.prototype.compare = function(other) {\n    // when comparing quoted strings allow the quote to differ\n    if (other.type === 'Quoted' && !this.escaped && !other.escaped) {\n        return Node.numericCompare(this.value, other.value);\n    } else {\n        return other.toCSS && this.toCSS() === other.toCSS() ? 0 : undefined;\n    }\n};\n\nQuoted.prototype.type = 'Quoted';\nexport default Quoted;\n"]}