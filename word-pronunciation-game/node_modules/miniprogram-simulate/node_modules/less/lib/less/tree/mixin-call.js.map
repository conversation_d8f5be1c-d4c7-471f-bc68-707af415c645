{"version": 3, "file": "mixin-call.js", "sourceRoot": "", "sources": ["../../../src/less/tree/mixin-call.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,wDAAkC;AAClC,wEAAiD;AACjD,iEAA+C;AAE/C,IAAM,SAAS,GAAG,UAAS,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS;IACxE,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAQ,CAAC,QAAQ,CAAC,CAAC;IACvC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEjC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IACzC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAChD;IACD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACvD;AACL,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACvC,IAAI,MAAM,CAAC;IACX,IAAI,KAAK,CAAC;IACV,IAAI,SAAS,CAAC;IACd,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,IAAI,GAAG,CAAC;IACR,IAAI,QAAQ,CAAC;IACb,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,IAAI,WAAW,CAAC;IAChB,IAAI,UAAU,CAAC;IACf,IAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,SAAS,CAAC;IACd,IAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,IAAI,aAAa,CAAC;IAClB,IAAM,kBAAkB,GAAG,CAAC,CAAC,CAAC;IAC9B,IAAM,OAAO,GAAG,CAAC,CAAC;IAClB,IAAM,OAAO,GAAG,CAAC,CAAC;IAClB,IAAM,QAAQ,GAAG,CAAC,CAAC;IACnB,IAAI,KAAK,CAAC;IACV,IAAI,eAAe,CAAC;IACpB,IAAI,iBAAiB,CAAC;IAEtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE5C,SAAS,YAAY,CAAC,KAAK,EAAE,SAAS;QAClC,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAI,SAAS,CAAC;QAEd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACpB,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAC1B,iBAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzD,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,SAAS,CAAC,cAAc,EAAE;oBAC1B,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBACtF;aACJ;YACD,IAAI,KAAK,CAAC,cAAc,EAAE;gBACtB,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aAClF;SACJ;QACD,IAAI,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE;YAC1C,IAAI,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE;gBAC1C,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvB,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;aAC1B;YAED,OAAO,OAAO,CAAC;SAClB;QACD,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACxB,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,GAAG,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC7C,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC1B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClC,IAAI,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;aACnC;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC;SAChD;KACJ;IAED,iBAAiB,GAAG,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,EAA7B,CAA6B,CAAC;IAE1D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YACtF,UAAU,GAAG,IAAI,CAAC;YAElB,6FAA6F;YAC7F,+FAA+F;YAC/F,8FAA8F;YAC9F,4BAA4B;YAE5B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACvB,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC3B,WAAW,GAAG,KAAK,CAAC;gBACpB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACxC,IAAI,CAAC,CAAC,CAAC,KAAK,YAAY,0BAAe,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;wBAC7G,WAAW,GAAG,IAAI,CAAC;wBACnB,MAAM;qBACT;iBACJ;gBACD,IAAI,WAAW,EAAE;oBACb,SAAS;iBACZ;gBAED,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;oBAChC,SAAS,GAAG,EAAC,KAAK,OAAA,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,EAAC,CAAC;oBAE3D,IAAI,SAAS,CAAC,KAAK,KAAK,kBAAkB,EAAE;wBACxC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBAC9B;oBAED,KAAK,GAAG,IAAI,CAAC;iBAChB;aACJ;YAED,iBAAW,CAAC,KAAK,EAAE,CAAC;YAEpB,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;aAChC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACpB,aAAa,GAAG,QAAQ,CAAC;aAC5B;iBAAM;gBACH,aAAa,GAAG,OAAO,CAAC;gBACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;oBACxC,MAAM,EAAE,IAAI,EAAE,SAAS,EACnB,OAAO,EAAE,2DAA4D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAI,EAC1F,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;iBACpE;aACJ;YAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAChC,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,KAAK,aAAa,CAAC,EAAE;oBAC1D,IAAI;wBACA,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;wBAC5B,IAAI,CAAC,CAAC,KAAK,YAAY,0BAAe,CAAC,EAAE;4BACrC,eAAe,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC;4BACjD,KAAK,GAAG,IAAI,0BAAe,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC;4BACtG,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;yBAC3C;wBACD,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;wBACrE,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC;wBAC3C,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;qBAC/C;oBAAC,OAAO,CAAC,EAAE;wBACR,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;qBAC5G;iBACJ;aACJ;YAED,IAAI,KAAK,EAAE;gBACP,OAAO,KAAK,CAAC;aAChB;SACJ;KACJ;IACD,IAAI,UAAU,EAAE;QACZ,MAAM,EAAE,IAAI,EAAK,SAAS,EACtB,OAAO,EAAE,2CAA0C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAI,EACxE,KAAK,EAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;KACtE;SAAM;QACH,MAAM,EAAE,IAAI,EAAK,MAAM,EACnB,OAAO,EAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,kBAAe,EACvD,KAAK,EAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;KACtE;AACL,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAS,WAAW;IAClE,IAAI,CAAC,CAAC;IACN,IAAI,IAAI,CAAC;IACT,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;QACzB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;KACJ;AACL,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,IAAI;IACtC,OAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,UAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC;QACvD,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,CAAC,IAAI,EAAE;YACR,QAAQ,IAAO,CAAC,CAAC,IAAI,MAAG,CAAC;SAC5B;QACD,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE;YACf,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;SAC/B;aAAM;YACH,QAAQ,IAAI,KAAK,CAAC;SACrB;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,OAAG,CAAC;AAC1B,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC;AACvC,kBAAe,SAAS,CAAC", "sourcesContent": ["import Node from './node';\nimport Selector from './selector';\nimport MixinDefinition from './mixin-definition';\nimport defaultFunc from '../functions/default';\n\nconst MixinCall = function(elements, args, index, currentFileInfo, important) {\n    this.selector = new Selector(elements);\n    this.arguments = args || [];\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.important = important;\n    this.allowRoot = true;\n    this.setParent(this.selector, this);\n};\n\nMixinCall.prototype = new Node();\n\nMixinCall.prototype.accept = function(visitor) {\n    if (this.selector) {\n        this.selector = visitor.visit(this.selector);\n    }\n    if (this.arguments.length) {\n        this.arguments = visitor.visitArray(this.arguments);\n    }\n};\n\nMixinCall.prototype.eval = function(context) {\n    let mixins;\n    let mixin;\n    let mixinPath;\n    const args = [];\n    let arg;\n    let argValue;\n    const rules = [];\n    let match = false;\n    let i;\n    let m;\n    let f;\n    let isRecursive;\n    let isOneFound;\n    const candidates = [];\n    let candidate;\n    const conditionResult = [];\n    let defaultResult;\n    const defFalseEitherCase = -1;\n    const defNone = 0;\n    const defTrue = 1;\n    const defFalse = 2;\n    let count;\n    let originalRuleset;\n    let noArgumentsFilter;\n\n    this.selector = this.selector.eval(context);\n\n    function calcDefGroup(mixin, mixinPath) {\n        let f;\n        let p;\n        let namespace;\n\n        for (f = 0; f < 2; f++) {\n            conditionResult[f] = true;\n            defaultFunc.value(f);\n            for (p = 0; p < mixinPath.length && conditionResult[f]; p++) {\n                namespace = mixinPath[p];\n                if (namespace.matchCondition) {\n                    conditionResult[f] = conditionResult[f] && namespace.matchCondition(null, context);\n                }\n            }\n            if (mixin.matchCondition) {\n                conditionResult[f] = conditionResult[f] && mixin.matchCondition(args, context);\n            }\n        }\n        if (conditionResult[0] || conditionResult[1]) {\n            if (conditionResult[0] != conditionResult[1]) {\n                return conditionResult[1] ?\n                    defTrue : defFalse;\n            }\n\n            return defNone;\n        }\n        return defFalseEitherCase;\n    }\n\n    for (i = 0; i < this.arguments.length; i++) {\n        arg = this.arguments[i];\n        argValue = arg.value.eval(context);\n        if (arg.expand && Array.isArray(argValue.value)) {\n            argValue = argValue.value;\n            for (m = 0; m < argValue.length; m++) {\n                args.push({value: argValue[m]});\n            }\n        } else {\n            args.push({name: arg.name, value: argValue});\n        }\n    }\n\n    noArgumentsFilter = rule => rule.matchArgs(null, context);\n\n    for (i = 0; i < context.frames.length; i++) {\n        if ((mixins = context.frames[i].find(this.selector, null, noArgumentsFilter)).length > 0) {\n            isOneFound = true;\n\n            // To make `default()` function independent of definition order we have two \"subpasses\" here.\n            // At first we evaluate each guard *twice* (with `default() == true` and `default() == false`),\n            // and build candidate list with corresponding flags. Then, when we know all possible matches,\n            // we make a final decision.\n\n            for (m = 0; m < mixins.length; m++) {\n                mixin = mixins[m].rule;\n                mixinPath = mixins[m].path;\n                isRecursive = false;\n                for (f = 0; f < context.frames.length; f++) {\n                    if ((!(mixin instanceof MixinDefinition)) && mixin === (context.frames[f].originalRuleset || context.frames[f])) {\n                        isRecursive = true;\n                        break;\n                    }\n                }\n                if (isRecursive) {\n                    continue;\n                }\n\n                if (mixin.matchArgs(args, context)) {\n                    candidate = {mixin, group: calcDefGroup(mixin, mixinPath)};\n\n                    if (candidate.group !== defFalseEitherCase) {\n                        candidates.push(candidate);\n                    }\n\n                    match = true;\n                }\n            }\n\n            defaultFunc.reset();\n\n            count = [0, 0, 0];\n            for (m = 0; m < candidates.length; m++) {\n                count[candidates[m].group]++;\n            }\n\n            if (count[defNone] > 0) {\n                defaultResult = defFalse;\n            } else {\n                defaultResult = defTrue;\n                if ((count[defTrue] + count[defFalse]) > 1) {\n                    throw { type: 'Runtime',\n                        message: `Ambiguous use of \\`default()\\` found when matching for \\`${this.format(args)}\\``,\n                        index: this.getIndex(), filename: this.fileInfo().filename };\n                }\n            }\n\n            for (m = 0; m < candidates.length; m++) {\n                candidate = candidates[m].group;\n                if ((candidate === defNone) || (candidate === defaultResult)) {\n                    try {\n                        mixin = candidates[m].mixin;\n                        if (!(mixin instanceof MixinDefinition)) {\n                            originalRuleset = mixin.originalRuleset || mixin;\n                            mixin = new MixinDefinition('', [], mixin.rules, null, false, null, originalRuleset.visibilityInfo());\n                            mixin.originalRuleset = originalRuleset;\n                        }\n                        const newRules = mixin.evalCall(context, args, this.important).rules;\n                        this._setVisibilityToReplacement(newRules);\n                        Array.prototype.push.apply(rules, newRules);\n                    } catch (e) {\n                        throw { message: e.message, index: this.getIndex(), filename: this.fileInfo().filename, stack: e.stack };\n                    }\n                }\n            }\n\n            if (match) {\n                return rules;\n            }\n        }\n    }\n    if (isOneFound) {\n        throw { type:    'Runtime',\n            message: `No matching definition was found for \\`${this.format(args)}\\``,\n            index:   this.getIndex(), filename: this.fileInfo().filename };\n    } else {\n        throw { type:    'Name',\n            message: `${this.selector.toCSS().trim()} is undefined`,\n            index:   this.getIndex(), filename: this.fileInfo().filename };\n    }\n};\n\nMixinCall.prototype._setVisibilityToReplacement = function(replacement) {\n    let i;\n    let rule;\n    if (this.blocksVisibility()) {\n        for (i = 0; i < replacement.length; i++) {\n            rule = replacement[i];\n            rule.addVisibilityBlock();\n        }\n    }\n};\n\nMixinCall.prototype.format = function(args) {\n    return `${this.selector.toCSS().trim()}(${args ? args.map(a => {\n        let argValue = '';\n        if (a.name) {\n            argValue += `${a.name}:`;\n        }\n        if (a.value.toCSS) {\n            argValue += a.value.toCSS();\n        } else {\n            argValue += '???';\n        }\n        return argValue;\n    }).join(', ') : ''})`;\n};\n\nMixinCall.prototype.type = 'MixinCall';\nexport default MixinCall;\n"]}