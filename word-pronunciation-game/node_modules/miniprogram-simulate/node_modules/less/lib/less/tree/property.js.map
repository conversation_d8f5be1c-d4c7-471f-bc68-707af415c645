{"version": 3, "file": "property.js", "sourceRoot": "", "sources": ["../../../src/less/tree/property.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,8DAAwC;AAExC,IAAM,QAAQ,GAAG,UAAS,IAAI,EAAE,KAAK,EAAE,eAAe;IAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;AACrC,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEhC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACtC,IAAI,QAAQ,CAAC;IACb,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACvB,+BAA+B;IAC/B,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC;IAE1F,IAAI,IAAI,CAAC,UAAU,EAAE;QACjB,MAAM,EAAE,IAAI,EAAE,MAAM,EAChB,OAAO,EAAE,sCAAoC,IAAM,EACnD,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;YAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;KAChC;IAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAEvB,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAA,KAAK;QACtC,IAAI,CAAC,CAAC;QACN,IAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,IAAI,EAAE;YACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAEZ,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,qBAAW,CAAC,CAAC,CAAC,IAAI,EAC5B,CAAC,CAAC,KAAK,EACP,CAAC,CAAC,SAAS,EACX,CAAC,CAAC,KAAK,EACP,CAAC,CAAC,KAAK,EACP,CAAC,CAAC,eAAe,EACjB,CAAC,CAAC,MAAM,EACR,CAAC,CAAC,QAAQ,CACb,CAAC;aACL;YACD,UAAU,CAAC,IAAI,CAAC,CAAC;YAEjB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,CAAC,SAAS,EAAE;gBACb,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjF,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;aAC1C;YACD,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1B,OAAO,CAAC,CAAC;SACZ;IACL,CAAC,CAAC,CAAC;IACH,IAAI,QAAQ,EAAE;QACV,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,OAAO,QAAQ,CAAC;KACnB;SAAM;QACH,MAAM,EAAE,IAAI,EAAE,MAAM,EAChB,OAAO,EAAE,eAAa,IAAI,mBAAgB,EAC1C,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ;YACvC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;KAC3B;AACL,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,GAAG,EAAE,GAAG;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAA,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC;SAAE;KACvB;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC;AACrC,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Declaration from './declaration';\n\nconst Property = function(name, index, currentFileInfo) {\n    this.name = name;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n};\n\nProperty.prototype = new Node();\n\nProperty.prototype.eval = function(context) {\n    let property;\n    const name = this.name;\n    // TODO: shorten this reference\n    const mergeRules = context.pluginManager.less.visitors.ToCSSVisitor.prototype._mergeRules;\n\n    if (this.evaluating) {\n        throw { type: 'Name',\n            message: `Recursive property reference for ${name}`,\n            filename: this.fileInfo().filename,\n            index: this.getIndex() };\n    }\n\n    this.evaluating = true;\n\n    property = this.find(context.frames, frame => {\n        let v;\n        const vArr = frame.property(name);\n        if (vArr) {\n            for (let i = 0; i < vArr.length; i++) {\n                v = vArr[i];\n\n                vArr[i] = new Declaration(v.name,\n                    v.value,\n                    v.important,\n                    v.merge,\n                    v.index,\n                    v.currentFileInfo,\n                    v.inline,\n                    v.variable\n                );\n            }\n            mergeRules(vArr);\n\n            v = vArr[vArr.length - 1];\n            if (v.important) {\n                const importantScope = context.importantScope[context.importantScope.length - 1];\n                importantScope.important = v.important;\n            }\n            v = v.value.eval(context);\n            return v;\n        }\n    });\n    if (property) {\n        this.evaluating = false;\n        return property;\n    } else {\n        throw { type: 'Name',\n            message: `Property '${name}' is undefined`,\n            filename: this.currentFileInfo.filename,\n            index: this.index };\n    }\n};\n\nProperty.prototype.find = function(obj, fun) {\n    for (let i = 0, r; i < obj.length; i++) {\n        r = fun.call(obj, obj[i]);\n        if (r) { return r; }\n    }\n    return null;\n};\n\nProperty.prototype.type = 'Property';\nexport default Property;\n"]}