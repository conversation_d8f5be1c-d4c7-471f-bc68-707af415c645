{"version": 3, "file": "variable.js", "sourceRoot": "", "sources": ["../../../src/less/tree/variable.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,gDAA0B;AAE1B,IAAM,QAAQ,GAAG,UAAS,IAAI,EAAE,KAAK,EAAE,eAAe;IAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;AACrC,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEhC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACtC,IAAI,QAAQ,CAAC;IACb,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAErB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC1B,IAAI,GAAG,MAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAO,CAAC;KAClG;IAED,IAAI,IAAI,CAAC,UAAU,EAAE;QACjB,MAAM,EAAE,IAAI,EAAE,MAAM,EAChB,OAAO,EAAE,uCAAqC,IAAM,EACpD,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;YAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;KAChC;IAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAEvB,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAA,KAAK;QACtC,IAAM,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,EAAE;YACH,IAAI,CAAC,CAAC,SAAS,EAAE;gBACb,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjF,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;aAC1C;YACD,0EAA0E;YAC1E,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,OAAO,CAAC,IAAI,cAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACvD;iBACI;gBACD,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAChC;SACJ;IACL,CAAC,CAAC,CAAC;IACH,IAAI,QAAQ,EAAE;QACV,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,OAAO,QAAQ,CAAC;KACnB;SAAM;QACH,MAAM,EAAE,IAAI,EAAE,MAAM,EAChB,OAAO,EAAE,cAAY,IAAI,kBAAe,EACxC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;YAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;KAChC;AACL,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,GAAG,EAAE,GAAG;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAA,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC;SAAE;KACvB;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC;AACrC,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Call from './call';\n\nconst Variable = function(name, index, currentFileInfo) {\n    this.name = name;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n};\n\nVariable.prototype = new Node();\n\nVariable.prototype.eval = function(context) {\n    let variable;\n    let name = this.name;\n\n    if (name.indexOf('@@') === 0) {\n        name = `@${new Variable(name.slice(1), this.getIndex(), this.fileInfo()).eval(context).value}`;\n    }\n\n    if (this.evaluating) {\n        throw { type: 'Name',\n            message: `Recursive variable definition for ${name}`,\n            filename: this.fileInfo().filename,\n            index: this.getIndex() };\n    }\n\n    this.evaluating = true;\n\n    variable = this.find(context.frames, frame => {\n        const v = frame.variable(name);\n        if (v) {\n            if (v.important) {\n                const importantScope = context.importantScope[context.importantScope.length - 1];\n                importantScope.important = v.important;\n            }\n            // If in calc, wrap vars in a function call to cascade evaluate args first\n            if (context.inCalc) {\n                return (new Call('_SELF', [v.value])).eval(context);\n            }\n            else {\n                return v.value.eval(context);\n            }\n        }\n    });\n    if (variable) {\n        this.evaluating = false;\n        return variable;\n    } else {\n        throw { type: 'Name',\n            message: `variable ${name} is undefined`,\n            filename: this.fileInfo().filename,\n            index: this.getIndex() };\n    }\n};\n\nVariable.prototype.find = function(obj, fun) {\n    for (let i = 0, r; i < obj.length; i++) {\n        r = fun.call(obj, obj[i]);\n        if (r) { return r; }\n    }\n    return null;\n};\n\nVariable.prototype.type = 'Variable';\nexport default Variable;\n"]}