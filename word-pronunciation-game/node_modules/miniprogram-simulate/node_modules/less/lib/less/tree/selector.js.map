{"version": 3, "file": "selector.js", "sourceRoot": "", "sources": ["../../../src/less/tree/selector.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,sDAAgC;AAChC,6DAAsC;AAEtC,IAAM,QAAQ,GAAG,UAAS,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc;IAC7F,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,CAAC;IACjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IAChC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC,CAAA;AAED,QAAQ,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEhC,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IACxC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACrD;IACD,IAAI,IAAI,CAAC,UAAU,EAAE;QACjB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACzD;IACD,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAClD;AACL,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,aAAa,GAAG,UAAS,QAAQ,EAAE,UAAU,EAAE,cAAc;IAC5E,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACtC,IAAM,WAAW,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,UAAU,IAAI,IAAI,CAAC,UAAU,EACpE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACnE,WAAW,CAAC,cAAc,GAAG,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;IAC7F,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACzC,OAAO,WAAW,CAAC;AACvB,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,WAAW,GAAG,UAAS,GAAG;IACzC,IAAI,CAAC,GAAG,EAAE;QACN,OAAO,CAAC,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;KACrE;IACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACzB,IAAI,CAAC,KAAK,CAAC,SAAS,CAChB,GAAG,EACH,CAAC,UAAU,CAAC,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,UAAS,GAAG,EAAE,MAAM;YAChB,IAAI,GAAG,EAAE;gBACL,MAAM,IAAI,oBAAS,CAAC;oBAChB,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,OAAO,EAAE,GAAG,CAAC,OAAO;iBACvB,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;aACnD;YACD,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC7B,CAAC,CAAC,CAAC;KACV;IACD,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACtC,IAAM,EAAE,GAAG,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACpE,IAAM,IAAI,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3E,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;IAC1B,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,UAAS,KAAK;IACrC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/B,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC5B,IAAI,IAAI,CAAC;IACT,IAAI,CAAC,CAAC;IAEN,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;IAC9B,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IACpB,IAAI,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,IAAI,EAAE;QAC1B,OAAO,CAAC,CAAC;KACZ;SAAM;QACH,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YACvB,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;gBAChC,OAAO,CAAC,CAAC;aACZ;SACJ;KACJ;IAED,OAAO,IAAI,CAAC,CAAC,oCAAoC;AACrD,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,aAAa,GAAG;IAC/B,IAAI,IAAI,CAAC,cAAc,EAAE;QACrB,OAAO,IAAI,CAAC,cAAc,CAAC;KAC9B;IAED,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAE,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,EAA/C,CAA+C,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;IAEtI,IAAI,QAAQ,EAAE;QACV,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACrB,QAAQ,CAAC,KAAK,EAAE,CAAC;SACpB;KACJ;SAAM;QACH,QAAQ,GAAG,EAAE,CAAC;KACjB;IAED,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC;AAC5C,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACtC,OAAO,CAAC,IAAI,CAAC,UAAU;QACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG;QAC9B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;AAChG,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACtC,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACtE,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC7B,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IAEjC,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAf,CAAe,CAAC,CAAC;IAC1D,UAAU,GAAG,UAAU,IAAI,UAAU,CAAC,GAAG,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAApB,CAAoB,CAAC,CAAC;IAE1E,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AACpE,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAChD,IAAI,CAAC,CAAC;IACN,IAAI,OAAO,CAAC;IACZ,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,EAAE,EAAE;QAClF,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;KACrD;IACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3B,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KACnC;AACL,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,WAAW,GAAG;IAC7B,OAAO,IAAI,CAAC,cAAc,CAAC;AAC/B,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC;AACrC,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Element from './element';\nimport LessError from '../less-error';\n\nconst Selector = function(elements, extendList, condition, index, currentFileInfo, visibilityInfo) {\n    this.extendList = extendList;\n    this.condition = condition;\n    this.evaldCondition = !condition;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.elements = this.getElements(elements);\n    this.mixinElements_ = undefined;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.setParent(this.elements, this);\n}\n\nSelector.prototype = new Node();\n\nSelector.prototype.accept = function(visitor) {\n    if (this.elements) {\n        this.elements = visitor.visitArray(this.elements);\n    }\n    if (this.extendList) {\n        this.extendList = visitor.visitArray(this.extendList);\n    }\n    if (this.condition) {\n        this.condition = visitor.visit(this.condition);\n    }\n};\n\nSelector.prototype.createDerived = function(elements, extendList, evaldCondition) {\n    elements = this.getElements(elements);\n    const newSelector = new Selector(elements, extendList || this.extendList,\n        null, this.getIndex(), this.fileInfo(), this.visibilityInfo());\n    newSelector.evaldCondition = (evaldCondition != null) ? evaldCondition : this.evaldCondition;\n    newSelector.mediaEmpty = this.mediaEmpty;\n    return newSelector;\n};\n\nSelector.prototype.getElements = function(els) {\n    if (!els) {\n        return [new Element('', '&', false, this._index, this._fileInfo)];\n    }\n    if (typeof els === 'string') {\n        this.parse.parseNode(\n            els, \n            ['selector'],\n            this._index, \n            this._fileInfo, \n            function(err, result) {\n                if (err) {\n                    throw new LessError({\n                        index: err.index,\n                        message: err.message\n                    }, this.parse.imports, this._fileInfo.filename);\n                }\n                els = result[0].elements;\n            });\n    }\n    return els;\n};\n\nSelector.prototype.createEmptySelectors = function() {\n    const el = new Element('', '&', false, this._index, this._fileInfo);\n    const sels = [new Selector([el], null, null, this._index, this._fileInfo)];\n    sels[0].mediaEmpty = true;\n    return sels;\n};\n\nSelector.prototype.match = function(other) {\n    const elements = this.elements;\n    const len = elements.length;\n    let olen;\n    let i;\n\n    other = other.mixinElements();\n    olen = other.length;\n    if (olen === 0 || len < olen) {\n        return 0;\n    } else {\n        for (i = 0; i < olen; i++) {\n            if (elements[i].value !== other[i]) {\n                return 0;\n            }\n        }\n    }\n\n    return olen; // return number of matched elements\n};\n\nSelector.prototype.mixinElements = function() {\n    if (this.mixinElements_) {\n        return this.mixinElements_;\n    }\n\n    let elements = this.elements.map( v => v.combinator.value + (v.value.value || v.value)).join('').match(/[,&#\\*\\.\\w-]([\\w-]|(\\\\.))*/g);\n\n    if (elements) {\n        if (elements[0] === '&') {\n            elements.shift();\n        }\n    } else {\n        elements = [];\n    }\n\n    return (this.mixinElements_ = elements);\n};\n\nSelector.prototype.isJustParentSelector = function() {\n    return !this.mediaEmpty &&\n        this.elements.length === 1 &&\n        this.elements[0].value === '&' &&\n        (this.elements[0].combinator.value === ' ' || this.elements[0].combinator.value === '');\n};\n\nSelector.prototype.eval = function(context) {\n    const evaldCondition = this.condition && this.condition.eval(context);\n    let elements = this.elements;\n    let extendList = this.extendList;\n\n    elements = elements && elements.map(e => e.eval(context));\n    extendList = extendList && extendList.map(extend => extend.eval(context));\n\n    return this.createDerived(elements, extendList, evaldCondition);\n};\n\nSelector.prototype.genCSS = function(context, output) {\n    let i;\n    let element;\n    if ((!context || !context.firstSelector) && this.elements[0].combinator.value === '') {\n        output.add(' ', this.fileInfo(), this.getIndex());\n    }\n    for (i = 0; i < this.elements.length; i++) {\n        element = this.elements[i];\n        element.genCSS(context, output);\n    }\n};\n\nSelector.prototype.getIsOutput = function() {\n    return this.evaldCondition;\n};\n\nSelector.prototype.type = 'Selector';\nexport default Selector;\n"]}