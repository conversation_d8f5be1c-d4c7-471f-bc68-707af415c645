{"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["../../../src/less/tree/url.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAE1B,IAAM,GAAG,GAAG,UAAS,GAAG,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO;IACrD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,CAAC,CAAC;AAEF,GAAG,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAE3B,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IACnC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3C,CAAC,CAAC;AAEF,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAC3C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACnC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,CAAC,CAAC;AAEF,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACjC,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrC,IAAI,QAAQ,CAAC;IAEb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACf,iDAAiD;QACjD,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACvD,IAAI,OAAO,QAAQ,KAAK,QAAQ;YAC5B,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ;YAC7B,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACxC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;gBACZ,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;aACnC;YACD,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACxD;aAAM;YACH,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;SAChD;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;gBAC/B,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC5D,IAAM,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;gBAC5C,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC/B,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAK,OAAO,MAAG,CAAC,CAAC;iBACrD;qBAAM;oBACH,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC;iBACxB;aACJ;SACJ;KACJ;IAED,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;AAChE,CAAC,CAAC;AAEF,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC;AAE3B,SAAS,UAAU,CAAC,IAAI;IACpB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAA,KAAK,IAAI,OAAA,OAAK,KAAO,EAAZ,CAAY,CAAC,CAAC;AAC9D,CAAC;AAED,kBAAe,GAAG,CAAC", "sourcesContent": ["import Node from './node';\n\nconst URL = function(val, index, currentFileInfo, isEvald) {\n    this.value = val;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.isEvald = isEvald;\n};\n\nURL.prototype = new Node();\n\nURL.prototype.accept = function(visitor) {\n    this.value = visitor.visit(this.value);\n};\n\nURL.prototype.genCSS = function(context, output) {\n    output.add('url(');\n    this.value.genCSS(context, output);\n    output.add(')');\n};\n\nURL.prototype.eval = function(context) {\n    const val = this.value.eval(context);\n    let rootpath;\n\n    if (!this.isEvald) {\n        // Add the rootpath if the URL requires a rewrite\n        rootpath = this.fileInfo() && this.fileInfo().rootpath;\n        if (typeof rootpath === 'string' &&\n            typeof val.value === 'string' &&\n            context.pathRequiresRewrite(val.value)) {\n            if (!val.quote) {\n                rootpath = escapePath(rootpath);\n            }\n            val.value = context.rewritePath(val.value, rootpath);\n        } else {\n            val.value = context.normalizePath(val.value);\n        }\n\n        // Add url args if enabled\n        if (context.urlArgs) {\n            if (!val.value.match(/^\\s*data:/)) {\n                const delimiter = val.value.indexOf('?') === -1 ? '?' : '&';\n                const urlArgs = delimiter + context.urlArgs;\n                if (val.value.indexOf('#') !== -1) {\n                    val.value = val.value.replace('#', `${urlArgs}#`);\n                } else {\n                    val.value += urlArgs;\n                }\n            }\n        }\n    }\n\n    return new URL(val, this.getIndex(), this.fileInfo(), true);\n};\n\nURL.prototype.type = 'Url';\n\nfunction escapePath(path) {\n    return path.replace(/[\\(\\)'\"\\s]/g, match => `\\\\${match}`);\n}\n\nexport default URL;\n"]}