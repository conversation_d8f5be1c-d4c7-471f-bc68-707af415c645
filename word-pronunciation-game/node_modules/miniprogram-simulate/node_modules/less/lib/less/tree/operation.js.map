{"version": 3, "file": "operation.js", "sourceRoot": "", "sources": ["../../../src/less/tree/operation.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAA0B;AAC1B,kDAA4B;AAC5B,0DAAoC;AACpC,sDAA0C;AAC1C,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;AAE5B,IAAM,SAAS,GAAG,UAAS,EAAE,EAAE,QAAQ,EAAE,QAAQ;IAC7C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;IACpB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEjC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtD,CAAC,CAAA;AAED,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACvC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,IAAI,EAAE,CAAC;IAEP,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;QAC3B,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;QACtC,IAAI,CAAC,YAAY,mBAAS,IAAI,CAAC,YAAY,eAAK,EAAE;YAC9C,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;SACnB;QACD,IAAI,CAAC,YAAY,mBAAS,IAAI,CAAC,YAAY,eAAK,EAAE;YAC9C,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;SACnB;QACD,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;YACZ,IAAI,CAAC,YAAY,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE;gBACjF,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACxD;YACD,MAAM,EAAE,IAAI,EAAE,WAAW;gBACrB,OAAO,EAAE,8BAA8B,EAAE,CAAC;SACjD;QAED,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;KACpC;SAAM;QACH,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACxD;AACL,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IACjD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACzC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACnB;IACD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACnB;IACD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC;AACvC,kBAAe,SAAS,CAAC", "sourcesContent": ["import Node from './node';\nimport Color from './color';\nimport Dimension from './dimension';\nimport * as Constants from '../constants';\nconst MATH = Constants.Math;\n\nconst Operation = function(op, operands, isSpaced) {\n    this.op = op.trim();\n    this.operands = operands;\n    this.isSpaced = isSpaced;\n};\n\nOperation.prototype = new Node();\n\nOperation.prototype.accept = function(visitor) {\n    this.operands = visitor.visitArray(this.operands);\n}\n\nOperation.prototype.eval = function(context) {\n    let a = this.operands[0].eval(context);\n    let b = this.operands[1].eval(context);\n    let op;\n\n    if (context.isMathOn(this.op)) {\n        op = this.op === './' ? '/' : this.op;\n        if (a instanceof Dimension && b instanceof Color) {\n            a = a.toColor();\n        }\n        if (b instanceof Dimension && a instanceof Color) {\n            b = b.toColor();\n        }\n        if (!a.operate) {\n            if (a instanceof Operation && a.op === '/' && context.math === MATH.PARENS_DIVISION) {\n                return new Operation(this.op, [a, b], this.isSpaced);\n            }\n            throw { type: 'Operation',\n                message: 'Operation on an invalid type' };\n        }\n\n        return a.operate(context, op, b);\n    } else {\n        return new Operation(this.op, [a, b], this.isSpaced);\n    }\n};\n\nOperation.prototype.genCSS = function(context, output) {\n    this.operands[0].genCSS(context, output);\n    if (this.isSpaced) {\n        output.add(' ');\n    }\n    output.add(this.op);\n    if (this.isSpaced) {\n        output.add(' ');\n    }\n    this.operands[1].genCSS(context, output);\n};\n\nOperation.prototype.type = 'Operation';\nexport default Operation;\n"]}