{"version": 3, "file": "media.js", "sourceRoot": "", "sources": ["../../../src/less/tree/media.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,sDAAgC;AAChC,kDAA4B;AAC5B,wDAAkC;AAClC,0DAAoC;AACpC,4DAAsC;AACtC,oDAA8B;AAC9B,8CAAkC;AAElC,IAAM,KAAK,GAAG,UAAS,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc;IAC1E,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IAEjC,IAAM,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC;IAErG,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAK,CAAC,QAAQ,CAAC,CAAC;IACpC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,iBAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;IAClC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC,CAAA;AAED,KAAK,CAAC,SAAS,GAAG,IAAI,gBAAM,EAAE,CAAC;AAE/B,KAAK,CAAC,SAAS,CAAC,aAAa,GAAG;IAC5B,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAChD;IACD,IAAI,IAAI,CAAC,KAAK,EAAE;QACZ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC/C;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAC7C,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACnD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACtC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACpD,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACnC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;QACtB,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;QACzB,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;KAC1B;IAED,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACtF,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;KACpC;IAED,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE7C,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEhC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IAC9E,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5C,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAEvB,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IAExB,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5D,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AAClC,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,UAAS,OAAO;IACtC,IAAI,MAAM,GAAG,IAAI,CAAC;IAElB,qCAAqC;IACrC,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,IAAM,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC;QAC1G,MAAM,GAAG,IAAI,iBAAO,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACrD,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KAChC;IAED,OAAO,OAAO,CAAC,WAAW,CAAC;IAC3B,OAAO,OAAO,CAAC,SAAS,CAAC;IAEzB,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,UAAS,OAAO;IACzC,IAAI,CAAC,CAAC;IACN,IAAI,KAAK,CAAC;IACV,IAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAE9C,8DAA8D;IAC9D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9B,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,YAAY,eAAK,CAAC,CAAC;YACvC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC9C,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KACpD;IAED,gEAAgE;IAChE,EAAE;IACF,qCAAqC;IACrC,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI;QACjD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,mBAAS,CAAC,QAAQ,CAAC,EAAnD,CAAmD,CAAC,CAAC;QAEjF,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,mBAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SAC3C;QAED,OAAO,IAAI,oBAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAEpC,iDAAiD;IACjD,OAAO,IAAI,iBAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,UAAS,GAAG;IAClC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QAClB,OAAO,EAAE,CAAC;KACb;SAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;KACjB;SAAM;QACH,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5C;SACJ;QACD,OAAO,MAAM,CAAC;KACjB;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,eAAe,GAAG,UAAS,SAAS;IAChD,IAAI,CAAC,SAAS,EAAE;QACZ,OAAO;KACV;IACD,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,iBAAO,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC;AAC/B,kBAAe,KAAK,CAAC", "sourcesContent": ["import Ruleset from './ruleset';\nimport Value from './value';\nimport Selector from './selector';\nimport Anonymous from './anonymous';\nimport Expression from './expression';\nimport AtRule from './atrule';\nimport * as utils from '../utils';\n\nconst Media = function(value, features, index, currentFileInfo, visibilityInfo) {\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n\n    const selectors = (new Selector([], null, null, this._index, this._fileInfo)).createEmptySelectors();\n\n    this.features = new Value(features);\n    this.rules = [new Ruleset(selectors, value)];\n    this.rules[0].allowImports = true;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n    this.setParent(selectors, this);\n    this.setParent(this.features, this);\n    this.setParent(this.rules, this);\n}\n\nMedia.prototype = new AtRule();\n\nMedia.prototype.isRulesetLike = function() {\n    return true;\n};\n\nMedia.prototype.accept = function(visitor) {\n    if (this.features) {\n        this.features = visitor.visit(this.features);\n    }\n    if (this.rules) {\n        this.rules = visitor.visitArray(this.rules);\n    }\n};\n\nMedia.prototype.genCSS = function(context, output) {\n    output.add('@media ', this._fileInfo, this._index);\n    this.features.genCSS(context, output);\n    this.outputRuleset(context, output, this.rules);\n};\n\nMedia.prototype.eval = function(context) {\n    if (!context.mediaBlocks) {\n        context.mediaBlocks = [];\n        context.mediaPath = [];\n    }\n\n    const media = new Media(null, [], this._index, this._fileInfo, this.visibilityInfo());\n    if (this.debugInfo) {\n        this.rules[0].debugInfo = this.debugInfo;\n        media.debugInfo = this.debugInfo;\n    }\n    \n    media.features = this.features.eval(context);\n\n    context.mediaPath.push(media);\n    context.mediaBlocks.push(media);\n\n    this.rules[0].functionRegistry = context.frames[0].functionRegistry.inherit();\n    context.frames.unshift(this.rules[0]);\n    media.rules = [this.rules[0].eval(context)];\n    context.frames.shift();\n\n    context.mediaPath.pop();\n\n    return context.mediaPath.length === 0 ? media.evalTop(context) :\n        media.evalNested(context);\n};\n\nMedia.prototype.evalTop = function(context) {\n    let result = this;\n\n    // Render all dependent Media blocks.\n    if (context.mediaBlocks.length > 1) {\n        const selectors = (new Selector([], null, null, this.getIndex(), this.fileInfo())).createEmptySelectors();\n        result = new Ruleset(selectors, context.mediaBlocks);\n        result.multiMedia = true;\n        result.copyVisibilityInfo(this.visibilityInfo());\n        this.setParent(result, this);\n    }\n\n    delete context.mediaBlocks;\n    delete context.mediaPath;\n\n    return result;\n};\n\nMedia.prototype.evalNested = function(context) {\n    let i;\n    let value;\n    const path = context.mediaPath.concat([this]);\n\n    // Extract the media-query conditions separated with `,` (OR).\n    for (i = 0; i < path.length; i++) {\n        value = path[i].features instanceof Value ?\n            path[i].features.value : path[i].features;\n        path[i] = Array.isArray(value) ? value : [value];\n    }\n\n    // Trace all permutations to generate the resulting media-query.\n    //\n    // (a, b and c) with nested (d, e) ->\n    //    a and d\n    //    a and e\n    //    b and c and d\n    //    b and c and e\n    this.features = new Value(this.permute(path).map(path => {\n        path = path.map(fragment => fragment.toCSS ? fragment : new Anonymous(fragment));\n\n        for (i = path.length - 1; i > 0; i--) {\n            path.splice(i, 0, new Anonymous('and'));\n        }\n\n        return new Expression(path);\n    }));\n    this.setParent(this.features, this);\n\n    // Fake a tree-node that doesn't output anything.\n    return new Ruleset([], []);\n};\n\nMedia.prototype.permute = function(arr) {\n    if (arr.length === 0) {\n        return [];\n    } else if (arr.length === 1) {\n        return arr[0];\n    } else {\n        const result = [];\n        const rest = this.permute(arr.slice(1));\n        for (let i = 0; i < rest.length; i++) {\n            for (let j = 0; j < arr[0].length; j++) {\n                result.push([arr[0][j]].concat(rest[i]));\n            }\n        }\n        return result;\n    }\n};\n\nMedia.prototype.bubbleSelectors = function(selectors) {\n    if (!selectors) {\n        return;\n    }\n    this.rules = [new Ruleset(utils.copyArray(selectors), [this.rules[0]])];\n    this.setParent(this.rules, this);\n};\n\nMedia.prototype.type = 'Media';\nexport default Media;\n"]}