{"version": 3, "file": "namespace-value.js", "sourceRoot": "", "sources": ["../../../src/less/tree/namespace-value.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,wDAAkC;AAClC,sDAAgC;AAChC,wDAAkC;AAElC,IAAM,cAAc,GAAG,UAAS,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ;IAC9D,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,CAAC,CAAA;AAED,cAAc,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEtC,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IAC5C,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,IAAI,IAAI,CAAC;IACT,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAErC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEvB;;;;WAIG;QACH,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,KAAK,GAAG,IAAI,iBAAO,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;SAChD;QAED,IAAI,IAAI,KAAK,EAAE,EAAE;YACb,KAAK,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;SACnC;aACI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACxB,IAAI,GAAG,MAAI,IAAI,kBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAO,CAAC;aACjE;YACD,IAAI,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aAChC;YAED,IAAI,CAAC,KAAK,EAAE;gBACR,MAAM,EAAE,IAAI,EAAE,MAAM,EAChB,OAAO,EAAE,cAAY,IAAI,eAAY,EACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;oBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;aAChC;SACJ;aACI;YACD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;gBAC/B,IAAI,GAAG,MAAI,IAAI,kBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAO,CAAC;aACjE;iBACI;gBACD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAI,IAAM,CAAC;aACrD;YACD,IAAI,KAAK,CAAC,UAAU,EAAE;gBAClB,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aAChC;YAED,IAAI,CAAC,KAAK,EAAE;gBACR,MAAM,EAAE,IAAI,EAAE,MAAM,EAChB,OAAO,EAAE,gBAAa,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAa,EACjD,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;oBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;aAChC;YACD,8EAA8E;YAC9E,8CAA8C;YAC9C,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACnC;QAED,IAAI,KAAK,CAAC,KAAK,EAAE;YACb,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;SACrC;QACD,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACvC;KACJ;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,gBAAgB,CAAC;AACjD,kBAAe,cAAc,CAAC", "sourcesContent": ["import Node from './node';\nimport Variable from './variable';\nimport Ruleset from './ruleset';\nimport Selector from './selector';\n\nconst NamespaceValue = function(ruleCall, lookups, index, fileInfo) {\n    this.value = ruleCall;\n    this.lookups = lookups;\n    this._index = index;\n    this._fileInfo = fileInfo;\n}\n\nNamespaceValue.prototype = new Node();\n\nNamespaceValue.prototype.eval = function(context) {\n    let i;\n    let j;\n    let name;\n    let rules = this.value.eval(context);\n\n    for (i = 0; i < this.lookups.length; i++) {\n        name = this.lookups[i];\n\n        /**\n         * Eval'd DRs return rulesets.\n         * Eval'd mixins return rules, so let's make a ruleset if we need it.\n         * We need to do this because of late parsing of values\n         */\n        if (Array.isArray(rules)) {\n            rules = new Ruleset([new Selector()], rules);\n        }\n\n        if (name === '') {\n            rules = rules.lastDeclaration();\n        }\n        else if (name.charAt(0) === '@') {\n            if (name.charAt(1) === '@') {\n                name = `@${new Variable(name.substr(1)).eval(context).value}`;\n            }\n            if (rules.variables) {\n                rules = rules.variable(name);\n            }\n            \n            if (!rules) {\n                throw { type: 'Name',\n                    message: `variable ${name} not found`,\n                    filename: this.fileInfo().filename,\n                    index: this.getIndex() };\n            }\n        }\n        else {\n            if (name.substring(0, 2) === '$@') {\n                name = `$${new Variable(name.substr(1)).eval(context).value}`;\n            }\n            else {\n                name = name.charAt(0) === '$' ? name : `$${name}`;\n            }\n            if (rules.properties) {\n                rules = rules.property(name);\n            }\n        \n            if (!rules) {\n                throw { type: 'Name',\n                    message: `property \"${name.substr(1)}\" not found`,\n                    filename: this.fileInfo().filename,\n                    index: this.getIndex() };\n            }\n            // Properties are an array of values, since a ruleset can have multiple props.\n            // We pick the last one (the \"cascaded\" value)\n            rules = rules[rules.length - 1];\n        }\n\n        if (rules.value) {\n            rules = rules.eval(context).value;\n        }\n        if (rules.ruleset) {\n            rules = rules.ruleset.eval(context);\n        }\n    }\n    return rules;\n};\n\nNamespaceValue.prototype.type = 'NamespaceValue';\nexport default NamespaceValue;\n"]}