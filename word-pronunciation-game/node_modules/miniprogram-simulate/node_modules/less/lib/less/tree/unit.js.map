{"version": 3, "file": "unit.js", "sourceRoot": "", "sources": ["../../../src/less/tree/unit.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAA0B;AAC1B,8EAAuD;AACvD,8CAAkC;AAElC,IAAM,IAAI,GAAG,UAAS,SAAS,EAAE,WAAW,EAAE,UAAU;IACpD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACpE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1E,IAAI,UAAU,EAAE;QACZ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAChC;SAAM,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,EAAE;QACtC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAClC;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAE5B,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;IACnB,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACzG,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAC5C,oFAAoF;IACpF,IAAM,WAAW,GAAG,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC;IACnD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC7B,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;KACxD;SAAM,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;QACxC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC/B;SAAM,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;QAChD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;KACnC;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;IACtB,IAAI,CAAC,CAAC;IACN,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,SAAS,IAAI,MAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAG,CAAC;KAC1C;IACD,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAS,KAAK;IACnC,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AACrD,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,UAAS,UAAU;IACnC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,CAAC;AACtE,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;IACtB,OAAO,MAAM,CAAC,uDAAuD,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AACpG,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG;IACrB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;AACxE,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG;IACxB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;AACvE,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,UAAS,QAAQ;IAClC,IAAI,CAAC,CAAC;IAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;KAC1D;IAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;KAC7D;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;IACvB,IAAI,KAAK,CAAC;IACV,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAI,OAAO,CAAC;IACZ,IAAI,SAAS,CAAC;IAEd,OAAO,GAAG,UAAA,UAAU;QAChB,0BAA0B;QAC1B,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACxD,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;SAClC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC,CAAC;IAEF,KAAK,SAAS,IAAI,0BAAe,EAAE;QAC/B,IAAI,0BAAe,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;YAC3C,KAAK,GAAG,0BAAe,CAAC,SAAS,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACrB;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;IACpB,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAI,UAAU,CAAC;IACf,IAAI,CAAC,CAAC;IAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/B,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;KACxD;IAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;KACxD;IAED,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACpB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAEtB,KAAK,UAAU,IAAI,OAAO,EAAE;QACxB,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;YACpC,IAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YAElC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;oBACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBACnC;aACJ;iBAAM,IAAI,KAAK,GAAG,CAAC,EAAE;gBAClB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;oBACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBACrC;aACJ;SACJ;KACJ;IAED,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACtB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AAC5B,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC;AAC7B,kBAAe,IAAI,CAAC", "sourcesContent": ["import Node from './node';\nimport unitConversions from '../data/unit-conversions';\nimport * as utils from '../utils';\n\nconst Unit = function(numerator, denominator, backupUnit) {\n    this.numerator = numerator ? utils.copyArray(numerator).sort() : [];\n    this.denominator = denominator ? utils.copyArray(denominator).sort() : [];\n    if (backupUnit) {\n        this.backupUnit = backupUnit;\n    } else if (numerator && numerator.length) {\n        this.backupUnit = numerator[0];\n    }\n};\n\nUnit.prototype = new Node();\n\nUnit.prototype.clone = function() {\n    return new Unit(utils.copyArray(this.numerator), utils.copyArray(this.denominator), this.backupUnit);\n};\n\nUnit.prototype.genCSS = function(context, output) {\n    // Dimension checks the unit is singular and throws an error if in strict math mode.\n    const strictUnits = context && context.strictUnits;\n    if (this.numerator.length === 1) {\n        output.add(this.numerator[0]); // the ideal situation\n    } else if (!strictUnits && this.backupUnit) {\n        output.add(this.backupUnit);\n    } else if (!strictUnits && this.denominator.length) {\n        output.add(this.denominator[0]);\n    }\n};\n\nUnit.prototype.toString = function() {\n    let i;\n    let returnStr = this.numerator.join('*');\n    for (i = 0; i < this.denominator.length; i++) {\n        returnStr += `/${this.denominator[i]}`;\n    }\n    return returnStr;\n};\n\nUnit.prototype.compare = function(other) {\n    return this.is(other.toString()) ? 0 : undefined;\n};\n\nUnit.prototype.is = function(unitString) {\n    return this.toString().toUpperCase() === unitString.toUpperCase();\n};\n\nUnit.prototype.isLength = function() {\n    return RegExp('^(px|em|ex|ch|rem|in|cm|mm|pc|pt|ex|vw|vh|vmin|vmax)$', 'gi').test(this.toCSS());\n};\n\nUnit.prototype.isEmpty = function() {\n    return this.numerator.length === 0 && this.denominator.length === 0;\n};\n\nUnit.prototype.isSingular = function() {\n    return this.numerator.length <= 1 && this.denominator.length === 0;\n};\n\nUnit.prototype.map = function(callback) {\n    let i;\n\n    for (i = 0; i < this.numerator.length; i++) {\n        this.numerator[i] = callback(this.numerator[i], false);\n    }\n\n    for (i = 0; i < this.denominator.length; i++) {\n        this.denominator[i] = callback(this.denominator[i], true);\n    }\n};\n\nUnit.prototype.usedUnits = function() {\n    let group;\n    const result = {};\n    let mapUnit;\n    let groupName;\n\n    mapUnit = atomicUnit => {\n        /* jshint loopfunc:true */\n        if (group.hasOwnProperty(atomicUnit) && !result[groupName]) {\n            result[groupName] = atomicUnit;\n        }\n\n        return atomicUnit;\n    };\n\n    for (groupName in unitConversions) {\n        if (unitConversions.hasOwnProperty(groupName)) {\n            group = unitConversions[groupName];\n\n            this.map(mapUnit);\n        }\n    }\n\n    return result;\n};\n\nUnit.prototype.cancel = function() {\n    const counter = {};\n    let atomicUnit;\n    let i;\n\n    for (i = 0; i < this.numerator.length; i++) {\n        atomicUnit = this.numerator[i];\n        counter[atomicUnit] = (counter[atomicUnit] || 0) + 1;\n    }\n\n    for (i = 0; i < this.denominator.length; i++) {\n        atomicUnit = this.denominator[i];\n        counter[atomicUnit] = (counter[atomicUnit] || 0) - 1;\n    }\n\n    this.numerator = [];\n    this.denominator = [];\n\n    for (atomicUnit in counter) {\n        if (counter.hasOwnProperty(atomicUnit)) {\n            const count = counter[atomicUnit];\n\n            if (count > 0) {\n                for (i = 0; i < count; i++) {\n                    this.numerator.push(atomicUnit);\n                }\n            } else if (count < 0) {\n                for (i = 0; i < -count; i++) {\n                    this.denominator.push(atomicUnit);\n                }\n            }\n        }\n    }\n\n    this.numerator.sort();\n    this.denominator.sort();\n};\n\nUnit.prototype.type = 'Unit';\nexport default Unit;\n"]}