{"version": 3, "file": "ruleset.js", "sourceRoot": "", "sources": ["../../../src/less/tree/ruleset.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAA0B;AAC1B,8DAAwC;AACxC,sDAAgC;AAChC,sDAAgC;AAChC,kDAA4B;AAC5B,wDAAkC;AAClC,sDAAgC;AAChC,0DAAoC;AACpC,yDAAmC;AACnC,qFAAoE;AACpE,iEAA+C;AAC/C,4DAAwC;AACxC,8CAAkC;AAElC,IAAM,OAAO,GAAG,UAAS,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,cAAc;IACpE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACnC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAEtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC,CAAA;AAED,OAAO,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAE/B,OAAO,CAAC,SAAS,CAAC,aAAa,GAAG;IAC9B,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IACvC,IAAI,IAAI,CAAC,KAAK,EAAE;QACZ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KACrD;SAAM,IAAI,IAAI,CAAC,SAAS,EAAE;QACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACvD;IACD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC/C;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACrC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,IAAI,SAAS,CAAC;IACd,IAAI,MAAM,CAAC;IACX,IAAI,QAAQ,CAAC;IACb,IAAI,CAAC,CAAC;IACN,IAAI,WAAW,CAAC;IAChB,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAElC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;QACpD,SAAS,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9B,iBAAW,CAAC,KAAK,CAAC;YACd,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,0DAA0D;SACtE,CAAC,CAAC;QAEH,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YACzB,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;oBACjC,WAAW,GAAG,IAAI,CAAC;oBACnB,MAAM;iBACT;aACJ;YACD,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;YACxB,IAAI,QAAQ,CAAC,cAAc,EAAE;gBACzB,qBAAqB,GAAG,IAAI,CAAC;aAChC;SACJ;QAED,IAAI,WAAW,EAAE;YACb,IAAM,gBAAgB,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;YAC3C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzB,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACxB,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;aACjD;YACD,IAAI,CAAC,KAAK,CAAC,SAAS,CAChB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAC1B,CAAC,WAAW,CAAC,EACb,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EACvB,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EACvB,UAAC,GAAG,EAAE,MAAM;gBACR,IAAI,MAAM,EAAE;oBACR,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;iBAC1C;YACL,CAAC,CAAC,CAAC;SACV;QAED,iBAAW,CAAC,KAAK,EAAE,CAAC;KACvB;SAAM;QACH,qBAAqB,GAAG,IAAI,CAAC;KAChC;IAED,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5D,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACzF,IAAI,IAAI,CAAC;IACT,IAAI,OAAO,CAAC;IAEZ,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;IAC/B,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACzB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IACnC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;IAEzC,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;KACtC;IAED,IAAI,CAAC,qBAAqB,EAAE;QACxB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KACpB;IAED,mEAAmE;IACnE,qCAAqC;IACrC,OAAO,CAAC,gBAAgB,GAAG,CAAC,UAAA,MAAM;QAC9B,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACxB,IAAI,KAAK,CAAC;QACV,OAAQ,CAAC,KAAK,CAAC,EAAG,EAAE,CAAC,EAAG;YACpB,KAAK,GAAG,MAAM,CAAE,CAAC,CAAE,CAAC,gBAAgB,CAAC;YACrC,IAAK,KAAK,EAAG;gBAAE,OAAO,KAAK,CAAC;aAAE;SACjC;QACD,OAAO,2BAAsB,CAAC;IAClC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;IAE7B,+CAA+C;IAC/C,IAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;IACjC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE3B,qBAAqB;IACrB,IAAI,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC;IACrC,IAAI,CAAC,YAAY,EAAE;QACf,OAAO,CAAC,SAAS,GAAG,YAAY,GAAG,EAAE,CAAC;KACzC;IACD,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAErC,mBAAmB;IACnB,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;QAChE,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAChC;IAED,6CAA6C;IAC7C,8DAA8D;IAC9D,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;IAC9B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACnC;KACJ;IAED,IAAM,eAAe,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEjF,wBAAwB;IACxB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;YAC3B,0BAA0B;YAC1B,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC;gBAC/B,IAAI,CAAC,CAAC,YAAY,qBAAW,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;oBAC1C,8CAA8C;oBAC9C,+CAA+C;oBAC/C,qDAAqD;oBACrD,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;iBACtC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,MAAM,OAAd,OAAO,EAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACxC,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YACtB,OAAO,CAAC,UAAU,EAAE,CAAC;SACxB;aAAM,IAAI,IAAI,CAAC,IAAI,KAAM,cAAc,EAAE;YACtC,0BAA0B;YAC1B,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC;gBACrC,IAAI,CAAC,CAAC,YAAY,qBAAW,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;oBAC1C,kCAAkC;oBAClC,OAAO,KAAK,CAAC;iBAChB;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,MAAM,OAAd,OAAO,EAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACxC,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YACtB,OAAO,CAAC,UAAU,EAAE,CAAC;SACxB;KACJ;IAED,2BAA2B;IAC3B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;SAC7D;KACJ;IAED,2BAA2B;IAC3B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAClC,8DAA8D;QAC9D,IAAI,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1E,8CAA8C;YAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,oBAAoB,EAAE,EAAE;gBAC/D,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC5C,IAAI,OAAO,YAAY,cAAI,EAAE;wBACzB,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;wBAClD,IAAI,CAAC,CAAC,OAAO,YAAY,qBAAW,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;4BACxD,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;yBACnC;qBACJ;iBACJ;aACJ;SACJ;KACJ;IAED,gBAAgB;IAChB,SAAS,CAAC,KAAK,EAAE,CAAC;IAClB,YAAY,CAAC,KAAK,EAAE,CAAC;IAErB,IAAI,OAAO,CAAC,WAAW,EAAE;QACrB,KAAK,CAAC,GAAG,eAAe,EAAE,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3D,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SACrD;KACJ;IAED,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,UAAS,OAAO;IAC5C,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAAC,CAAC;IACN,IAAI,WAAW,CAAC;IAChB,IAAI,CAAC,KAAK,EAAE;QAAE,OAAO;KAAE;IAEvB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC5B,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;gBACjE,KAAK,CAAC,MAAM,OAAZ,KAAK,EAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;gBAC5C,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;aAC/B;iBAAM;gBACH,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;aACnC;YACD,IAAI,CAAC,UAAU,EAAE,CAAC;SACrB;KACJ;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,aAAa,GAAG;IAC9B,IAAM,MAAM,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC;QACvD,IAAI,CAAC,CAAC,aAAa,EAAE;YACjB,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;SAC5B;aAAM;YACH,OAAO,CAAC,CAAC;SACZ;IACL,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAE/C,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,UAAS,IAAI;IACvC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF,4CAA4C;AAC5C,OAAO,CAAC,SAAS,CAAC,cAAc,GAAG,UAAS,IAAI,EAAE,OAAO;IACrD,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC/D,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;QAC9B,OAAO,KAAK,CAAC;KAChB;IACD,IAAI,YAAY,CAAC,SAAS;QACtB,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CACxB,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EACrB,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;QAC1B,OAAO,KAAK,CAAC;KAChB;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG;IAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG;IAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAClB,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,CAAC;YAC3D,IAAI,CAAC,YAAY,qBAAW,IAAI,CAAC,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACjD,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACpB;YACD,iFAAiF;YACjF,6CAA6C;YAC7C,kEAAkE;YAClE,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnD,IAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChC,KAAK,IAAM,MAAI,IAAI,IAAI,EAAE;oBACrB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAI,CAAC,EAAE;wBAC3B,IAAI,CAAC,MAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAI,CAAC,CAAC;qBACtC;iBACJ;aACJ;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,EAAE,EAAE,CAAC,CAAC;KACV;IACD,OAAO,IAAI,CAAC,UAAU,CAAC;AAC3B,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG;IAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QACnB,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,CAAC;YAC5D,IAAI,CAAC,YAAY,qBAAW,IAAI,CAAC,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACjD,IAAM,MAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,iBAAO,CAAC,CAAC,CAAC;oBAClE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC7B,+CAA+C;gBAC/C,IAAI,CAAC,IAAI,CAAC,MAAI,MAAM,CAAC,EAAE;oBACnB,IAAI,CAAC,MAAI,MAAM,CAAC,GAAG,CAAE,CAAC,CAAE,CAAC;iBAC5B;qBACI;oBACD,IAAI,CAAC,MAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5B;aACJ;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,EAAE,EAAE,CAAC,CAAC;KACV;IACD,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAS,IAAI;IACtC,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC;IACpC,IAAI,IAAI,EAAE;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAChC;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAS,IAAI;IACtC,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC;IACrC,IAAI,IAAI,EAAE;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAChC;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,eAAe,GAAG;IAChC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/B,IAAI,IAAI,YAAY,qBAAW,EAAE;YAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAChC;KACJ;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,UAAS,OAAO;IAC3C,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,SAAS,oBAAoB,CAAC,IAAI;QAC9B,IAAI,IAAI,CAAC,KAAK,YAAY,mBAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACjD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE;gBACtC,IAAI,CAAC,KAAK,CAAC,SAAS,CAChB,IAAI,CAAC,KAAK,CAAC,KAAK,EAChB,CAAC,OAAO,EAAE,WAAW,CAAC,EACtB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EACrB,IAAI,CAAC,QAAQ,EAAE,EACf,UAAC,GAAG,EAAE,MAAM;oBACR,IAAI,GAAG,EAAE;wBACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;qBACtB;oBACD,IAAI,MAAM,EAAE;wBACR,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;wBACvB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;qBACtB;gBACL,CAAC,CAAC,CAAC;aACV;iBAAM;gBACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;aACtB;YAED,OAAO,IAAI,CAAC;SACf;aACI;YACD,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACnD;SACI;QACD,IAAM,OAAK,GAAG,EAAE,CAAC;QACjB,OAAO,CAAC,OAAO,CAAC,UAAA,CAAC;YACb,OAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QACH,OAAO,OAAK,CAAC;KAChB;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,QAAQ,GAAG;IACzB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;QAAE,OAAO,EAAE,CAAC;KAAE;IAE/B,IAAM,SAAS,GAAG,EAAE,CAAC;IACrB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAAC,CAAC;IACN,IAAI,IAAI,CAAC;IAET,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACxB;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,UAAS,IAAI;IACzC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,KAAK,EAAE;QACP,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACvB;SAAM;QACH,IAAI,CAAC,KAAK,GAAG,CAAE,IAAI,CAAE,CAAC;KACzB;IACD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,QAAQ,EAAE,IAAW,EAAE,MAAM;IAAnB,qBAAA,EAAA,WAAW;IACnD,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAI,KAAK,CAAC;IACV,IAAI,WAAW,CAAC;IAChB,IAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;IAE7B,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KAAE;IAExD,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,UAAA,IAAI;QACxB,IAAI,IAAI,KAAK,IAAI,EAAE;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1C,IAAI,KAAK,EAAE;oBACP,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE;wBAClC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;4BACzB,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,kBAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;4BACpF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gCACzC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;6BAClC;4BACD,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;yBAClD;qBACJ;yBAAM;wBACH,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC,CAAC;qBACjC;oBACD,MAAM;iBACT;aACJ;SACJ;IACL,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAC3B,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAC/C,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,IAAM,gBAAgB,GAAG,EAAE,CAAC;IAC5B,IAAI,SAAS,GAAG,EAAE,CAAC;IAEnB,IAAI,wBAAwB;IACxB,SAAS,CAAC;IAEd,IAAI,IAAI,CAAC;IACT,IAAI,IAAI,CAAC;IAET,OAAO,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;IAE3C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACZ,OAAO,CAAC,QAAQ,EAAE,CAAC;KACtB;IAED,IAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClF,IAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7E,IAAI,GAAG,CAAC;IAER,IAAI,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAI,eAAe,GAAG,CAAC,CAAC;IACxB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,IAAI,YAAY,iBAAO,EAAE;YACzB,IAAI,eAAe,KAAK,CAAC,EAAE;gBACvB,eAAe,EAAE,CAAC;aACrB;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACxB;aAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAC3C,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;YAC5C,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,CAAC;SACrB;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC/B,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;YAC3C,eAAe,EAAE,CAAC;SACrB;aAAM;YACH,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACxB;KACJ;IACD,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAE/C,4CAA4C;IAC5C,qBAAqB;IACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACZ,SAAS,GAAG,oBAAY,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAEnD,IAAI,SAAS,EAAE;YACX,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACtB,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SACzB;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7B,IAAI,UAAU,SAAA,CAAC;QAEf,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAM,SAAW,CAAC,CAAC;QAEnD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE;gBAAE,SAAS;aAAE;YAC9C,IAAI,CAAC,GAAG,CAAC,EAAE;gBAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAAE;YAE/B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEhC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aACnC;SACJ;QAED,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC;KAC9D;IAED,6BAA6B;IAC7B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAEpC,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE;YAC5B,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;SAC3B;QAED,IAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;QACzC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAChC;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACnB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;SACrC;QAED,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC;QAEnC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YACvC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAK,UAAY,CAAC,CAAC,CAAC;SAC3D;aAAM;YACH,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC5B;KACJ;IAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACZ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAK,SAAS,MAAG,CAAC,CAAC,CAAC;QACzD,OAAO,CAAC,QAAQ,EAAE,CAAC;KACtB;IAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;QAC1D,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACpB;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,aAAa,GAAG,UAAS,KAAK,EAAE,OAAO,EAAE,SAAS;IAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KACnD;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,YAAY,GAAG,UAAS,KAAK,EAAE,OAAO,EAAE,QAAQ;IAC9D,SAAS,iBAAiB,CAAC,aAAa,EAAE,eAAe;QACrD,IAAI,gBAAgB,CAAC;QACrB,IAAI,CAAC,CAAC;QACN,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,gBAAgB,GAAG,IAAI,eAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;SAClD;aAAM;YACH,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACrD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,iBAAO,CACzB,IAAI,EACJ,aAAa,CAAC,CAAC,CAAC,EAChB,eAAe,CAAC,UAAU,EAC1B,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,SAAS,CAC5B,CAAC;aACL;YACD,gBAAgB,GAAG,IAAI,eAAK,CAAC,IAAI,kBAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;SAC5D;QACD,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED,SAAS,cAAc,CAAC,gBAAgB,EAAE,eAAe;QACrD,IAAI,OAAO,CAAC;QACZ,IAAI,QAAQ,CAAC;QACb,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,gBAAgB,EAAE,eAAe,CAAC,UAAU,EAAE,eAAe,CAAC,MAAM,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;QAC7H,QAAQ,GAAG,IAAI,kBAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACnC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,2EAA2E;IAC3E,yEAAyE;IACzE,4BAA4B;IAC5B,SAAS,sBAAsB,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB;QACrF,IAAI,eAAe,CAAC;QACpB,IAAI,YAAY,CAAC;QACjB,IAAI,iBAAiB,CAAC;QACtB,wBAAwB;QACxB,eAAe,GAAG,EAAE,CAAC;QAErB,8EAA8E;QAC9E,4EAA4E;QAC5E,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACjD,YAAY,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC;YACrC,iBAAiB,GAAG,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9F;aACI;YACD,iBAAiB,GAAG,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;SAC1D;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,6DAA6D;YAC7D,gDAAgD;YAChD,gEAAgE;YAChE,2DAA2D;YAC3D,yFAAyF;YACzF,IAAI,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;YAE5C,IAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,UAAU,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,EAAE;gBACxE,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;aACpC;YACD,6DAA6D;YAC7D,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,iBAAO,CACvC,UAAU,EACV,QAAQ,CAAC,KAAK,EACd,eAAe,CAAC,UAAU,EAC1B,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,SAAS,CAC5B,CAAC,CAAC;YACH,iBAAiB,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAChG;QAED,4DAA4D;QAC5D,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC3C;QAED,iFAAiF;QACjF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAClC,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,EAA7C,CAA6C,CAAC,CAAC;YACvF,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACxD;QACD,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,wFAAwF;IACxF,yEAAyE;IACzE,4CAA4C;IAC5C,SAAS,0BAA0B,CAAE,aAAa,EAAE,QAAQ,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM;QACnG,IAAI,CAAC,CAAC;QACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,eAAe,GAAG,sBAAsB,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC;YAC9G,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAChC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,SAAS,0BAA0B,CAAC,QAAQ,EAAE,SAAS;QACnD,IAAI,CAAC,CAAC;QACN,IAAI,GAAG,CAAC;QAER,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAQ;SACX;QACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,SAAS,CAAC,IAAI,CAAC,CAAE,IAAI,kBAAQ,CAAC,QAAQ,CAAC,CAAE,CAAC,CAAC;YAC3C,OAAO;SACV;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACnC,uEAAuE;YACvE,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChB,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC1G;iBACI;gBACD,GAAG,CAAC,IAAI,CAAC,IAAI,kBAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;aACpC;SACJ;IACL,CAAC;IAED,iFAAiF;IACjF,wDAAwD;IACxD,sEAAsE;IACtE,SAAS,qBAAqB,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU;QACrD,6BAA6B;QAC7B,wDAAwD;QACxD,8DAA8D;QAC9D,OAAO;QACP,WAAW;QACX,SAAS;QACT,MAAM;QACN,IAAI;QACJ,6BAA6B;QAC7B,EAAE;QACF,IAAI,CAAC,CAAC;QAEN,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAI,eAAe,CAAC;QACpB,IAAI,YAAY,CAAC;QACjB,IAAI,mBAAmB,CAAC;QACxB,IAAI,GAAG,CAAC;QACR,IAAI,EAAE,CAAC;QACP,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,MAAM,CAAC;QACX,IAAI,YAAY,CAAC;QACjB,SAAS,kBAAkB,CAAC,OAAO;YAC/B,IAAI,aAAa,CAAC;YAClB,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,eAAK,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC;aACf;YAED,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;YACpC,IAAI,CAAC,CAAC,aAAa,YAAY,kBAAQ,CAAC,EAAE;gBACtC,OAAO,IAAI,CAAC;aACf;YAED,OAAO,aAAa,CAAC;QACzB,CAAC;QAED,gDAAgD;QAChD,eAAe,GAAG,EAAE,CAAC;QACrB,wDAAwD;QACxD,iGAAiG;QACjG,iBAAiB;QACjB,YAAY,GAAG;YACX,EAAE;SACL,CAAC;QAEF,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,+CAA+C;YAC/C,IAAI,EAAE,CAAC,KAAK,KAAK,GAAG,EAAE;gBAClB,IAAM,cAAc,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAC;gBAC9C,IAAI,cAAc,IAAI,IAAI,EAAE;oBACxB,yDAAyD;oBACzD,6CAA6C;oBAC7C,0BAA0B,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;oBAE1D,IAAM,WAAW,GAAG,EAAE,CAAC;oBACvB,IAAI,QAAQ,SAAA,CAAC;oBACb,IAAM,oBAAoB,GAAG,EAAE,CAAC;oBAChC,QAAQ,GAAG,qBAAqB,CAAC,WAAW,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;oBACvE,iBAAiB,GAAG,iBAAiB,IAAI,QAAQ,CAAC;oBAClD,wGAAwG;oBACxG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACrC,IAAM,mBAAmB,GAAG,cAAc,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;wBACtF,0BAA0B,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,oBAAoB,CAAC,CAAC;qBACzG;oBACD,YAAY,GAAG,oBAAoB,CAAC;oBACpC,eAAe,GAAG,EAAE,CAAC;iBACxB;qBAAM;oBACH,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC5B;aAEJ;iBAAM;gBACH,iBAAiB,GAAG,IAAI,CAAC;gBACzB,mCAAmC;gBACnC,mBAAmB,GAAG,EAAE,CAAC;gBAEzB,yDAAyD;gBACzD,6CAA6C;gBAC7C,0BAA0B,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;gBAE1D,qCAAqC;gBACrC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACtC,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBACtB,sFAAsF;oBACtF,mCAAmC;oBACnC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,sFAAsF;wBACtF,iBAAiB;wBACjB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;4BAChB,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,iBAAO,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;yBAChG;wBACD,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;qBACjC;yBACI;wBACD,2BAA2B;wBAC3B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BACjC,uCAAuC;4BACvC,qEAAqE;4BACrE,IAAM,eAAe,GAAG,sBAAsB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC;4BAChF,uCAAuC;4BACvC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;yBAC7C;qBACJ;iBACJ;gBAED,4DAA4D;gBAC5D,YAAY,GAAG,mBAAmB,CAAC;gBACnC,eAAe,GAAG,EAAE,CAAC;aACxB;SACJ;QAED,wDAAwD;QACxD,2CAA2C;QAC3C,0BAA0B,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE1D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAChC,IAAI,MAAM,GAAG,CAAC,EAAE;gBACZ,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3C,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;aAC1G;SACJ;QAED,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED,SAAS,cAAc,CAAC,cAAc,EAAE,UAAU;QAC9C,IAAM,WAAW,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;QACpH,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAC/C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,4BAA4B;IAC5B,IAAI,CAAC,CAAC;IAEN,IAAI,QAAQ,CAAC;IACb,IAAI,iBAAiB,CAAC;IAEtB,QAAQ,GAAG,EAAE,CAAC;IACd,iBAAiB,GAAG,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAEvE,IAAI,CAAC,iBAAiB,EAAE;QACpB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,QAAQ,GAAG,EAAE,CAAC;YACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAEjC,IAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBAE1F,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC5B,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC/B;SACJ;aACI;YACD,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3B;KACJ;IAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAClC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3B;AACL,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC;AACnC,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;AACnC,kBAAe,OAAO,CAAC", "sourcesContent": ["import Node from './node';\nimport Declaration from './declaration';\nimport Keyword from './keyword';\nimport Comment from './comment';\nimport Paren from './paren';\nimport Selector from './selector';\nimport Element from './element';\nimport Anonymous from './anonymous';\nimport contexts from '../contexts';\nimport globalFunctionRegistry from '../functions/function-registry';\nimport defaultFunc from '../functions/default';\nimport getDebugInfo from './debug-info';\nimport * as utils from '../utils';\n\nconst Ruleset = function(selectors, rules, strictImports, visibilityInfo) {\n    this.selectors = selectors;\n    this.rules = rules;\n    this._lookups = {};\n    this._variables = null;\n    this._properties = null;\n    this.strictImports = strictImports;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n\n    this.setParent(this.selectors, this);\n    this.setParent(this.rules, this);\n}\n\nRuleset.prototype = new Node();\n\nRuleset.prototype.isRulesetLike = function() {\n    return true;\n};\n\nRuleset.prototype.accept = function(visitor) {\n    if (this.paths) {\n        this.paths = visitor.visitArray(this.paths, true);\n    } else if (this.selectors) {\n        this.selectors = visitor.visitArray(this.selectors);\n    }\n    if (this.rules && this.rules.length) {\n        this.rules = visitor.visitArray(this.rules);\n    }\n};\n\nRuleset.prototype.eval = function(context) {\n    const that = this;\n    let selectors;\n    let selCnt;\n    let selector;\n    let i;\n    let hasVariable;\n    let hasOnePassingSelector = false;\n\n    if (this.selectors && (selCnt = this.selectors.length)) {\n        selectors = new Array(selCnt);\n        defaultFunc.error({\n            type: 'Syntax',\n            message: 'it is currently only allowed in parametric mixin guards,'\n        });\n\n        for (i = 0; i < selCnt; i++) {\n            selector = this.selectors[i].eval(context);\n            for (var j = 0; j < selector.elements.length; j++) {\n                if (selector.elements[j].isVariable) {\n                    hasVariable = true;\n                    break;\n                }\n            }\n            selectors[i] = selector;\n            if (selector.evaldCondition) {\n                hasOnePassingSelector = true;\n            }\n        }\n\n        if (hasVariable) {\n            const toParseSelectors = new Array(selCnt);\n            for (i = 0; i < selCnt; i++) {\n                selector = selectors[i];\n                toParseSelectors[i] = selector.toCSS(context);\n            }\n            this.parse.parseNode(\n                toParseSelectors.join(','),\n                [\"selectors\"], \n                selectors[0].getIndex(), \n                selectors[0].fileInfo(), \n                (err, result) => {\n                    if (result) {\n                        selectors = utils.flattenArray(result);\n                    }\n                });\n        }\n\n        defaultFunc.reset();\n    } else {\n        hasOnePassingSelector = true;\n    }\n\n    let rules = this.rules ? utils.copyArray(this.rules) : null;\n    const ruleset = new Ruleset(selectors, rules, this.strictImports, this.visibilityInfo());\n    let rule;\n    let subRule;\n\n    ruleset.originalRuleset = this;\n    ruleset.root = this.root;\n    ruleset.firstRoot = this.firstRoot;\n    ruleset.allowImports = this.allowImports;\n\n    if (this.debugInfo) {\n        ruleset.debugInfo = this.debugInfo;\n    }\n\n    if (!hasOnePassingSelector) {\n        rules.length = 0;\n    }\n\n    // inherit a function registry from the frames stack when possible;\n    // otherwise from the global registry\n    ruleset.functionRegistry = (frames => {\n        let i = 0;\n        const n = frames.length;\n        let found;\n        for ( ; i !== n ; ++i ) {\n            found = frames[ i ].functionRegistry;\n            if ( found ) { return found; }\n        }\n        return globalFunctionRegistry;\n    })(context.frames).inherit();\n\n    // push the current ruleset to the frames stack\n    const ctxFrames = context.frames;\n    ctxFrames.unshift(ruleset);\n\n    // currrent selectors\n    let ctxSelectors = context.selectors;\n    if (!ctxSelectors) {\n        context.selectors = ctxSelectors = [];\n    }\n    ctxSelectors.unshift(this.selectors);\n\n    // Evaluate imports\n    if (ruleset.root || ruleset.allowImports || !ruleset.strictImports) {\n        ruleset.evalImports(context);\n    }\n\n    // Store the frames around mixin definitions,\n    // so they can be evaluated like closures when the time comes.\n    const rsRules = ruleset.rules;\n    for (i = 0; (rule = rsRules[i]); i++) {\n        if (rule.evalFirst) {\n            rsRules[i] = rule.eval(context);\n        }\n    }\n\n    const mediaBlockCount = (context.mediaBlocks && context.mediaBlocks.length) || 0;\n\n    // Evaluate mixin calls.\n    for (i = 0; (rule = rsRules[i]); i++) {\n        if (rule.type === 'MixinCall') {\n            /* jshint loopfunc:true */\n            rules = rule.eval(context).filter(r => {\n                if ((r instanceof Declaration) && r.variable) {\n                    // do not pollute the scope if the variable is\n                    // already there. consider returning false here\n                    // but we need a way to \"return\" variable from mixins\n                    return !(ruleset.variable(r.name));\n                }\n                return true;\n            });\n            rsRules.splice(...[i, 1].concat(rules));\n            i += rules.length - 1;\n            ruleset.resetCache();\n        } else if (rule.type ===  'VariableCall') {\n            /* jshint loopfunc:true */\n            rules = rule.eval(context).rules.filter(r => {\n                if ((r instanceof Declaration) && r.variable) {\n                    // do not pollute the scope at all\n                    return false;\n                }\n                return true;\n            });\n            rsRules.splice(...[i, 1].concat(rules));\n            i += rules.length - 1;\n            ruleset.resetCache();\n        }\n    }\n\n    // Evaluate everything else\n    for (i = 0; (rule = rsRules[i]); i++) {\n        if (!rule.evalFirst) {\n            rsRules[i] = rule = rule.eval ? rule.eval(context) : rule;\n        }\n    }\n\n    // Evaluate everything else\n    for (i = 0; (rule = rsRules[i]); i++) {\n        // for rulesets, check if it is a css guard and can be removed\n        if (rule instanceof Ruleset && rule.selectors && rule.selectors.length === 1) {\n            // check if it can be folded in (e.g. & where)\n            if (rule.selectors[0] && rule.selectors[0].isJustParentSelector()) {\n                rsRules.splice(i--, 1);\n\n                for (var j = 0; (subRule = rule.rules[j]); j++) {\n                    if (subRule instanceof Node) {\n                        subRule.copyVisibilityInfo(rule.visibilityInfo());\n                        if (!(subRule instanceof Declaration) || !subRule.variable) {\n                            rsRules.splice(++i, 0, subRule);\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    // Pop the stack\n    ctxFrames.shift();\n    ctxSelectors.shift();\n\n    if (context.mediaBlocks) {\n        for (i = mediaBlockCount; i < context.mediaBlocks.length; i++) {\n            context.mediaBlocks[i].bubbleSelectors(selectors);\n        }\n    }\n\n    return ruleset;\n};\n\nRuleset.prototype.evalImports = function(context) {\n    const rules = this.rules;\n    let i;\n    let importRules;\n    if (!rules) { return; }\n\n    for (i = 0; i < rules.length; i++) {\n        if (rules[i].type === 'Import') {\n            importRules = rules[i].eval(context);\n            if (importRules && (importRules.length || importRules.length === 0)) {\n                rules.splice(...[i, 1].concat(importRules));\n                i += importRules.length - 1;\n            } else {\n                rules.splice(i, 1, importRules);\n            }\n            this.resetCache();\n        }\n    }\n};\n\nRuleset.prototype.makeImportant = function() {\n    const result = new Ruleset(this.selectors, this.rules.map(r => {\n        if (r.makeImportant) {\n            return r.makeImportant();\n        } else {\n            return r;\n        }\n    }), this.strictImports, this.visibilityInfo());\n\n    return result;\n};\n\nRuleset.prototype.matchArgs = function(args) {\n    return !args || args.length === 0;\n};\n\n// lets you call a css selector with a guard\nRuleset.prototype.matchCondition = function(args, context) {\n    const lastSelector = this.selectors[this.selectors.length - 1];\n    if (!lastSelector.evaldCondition) {\n        return false;\n    }\n    if (lastSelector.condition &&\n        !lastSelector.condition.eval(\n            new contexts.Eval(context,\n                context.frames))) {\n        return false;\n    }\n    return true;\n};\n\nRuleset.prototype.resetCache = function() {\n    this._rulesets = null;\n    this._variables = null;\n    this._properties = null;\n    this._lookups = {};\n};\n\nRuleset.prototype.variables = function() {\n    if (!this._variables) {\n        this._variables = !this.rules ? {} : this.rules.reduce((hash, r) => {\n            if (r instanceof Declaration && r.variable === true) {\n                hash[r.name] = r;\n            }\n            // when evaluating variables in an import statement, imports have not been eval'd\n            // so we need to go inside import statements.\n            // guard against root being a string (in the case of inlined less)\n            if (r.type === 'Import' && r.root && r.root.variables) {\n                const vars = r.root.variables();\n                for (const name in vars) {\n                    if (vars.hasOwnProperty(name)) {\n                        hash[name] = r.root.variable(name);\n                    }\n                }\n            }\n            return hash;\n        }, {});\n    }\n    return this._variables;\n};\n\nRuleset.prototype.properties = function() {\n    if (!this._properties) {\n        this._properties = !this.rules ? {} : this.rules.reduce((hash, r) => {\n            if (r instanceof Declaration && r.variable !== true) {\n                const name = (r.name.length === 1) && (r.name[0] instanceof Keyword) ?\n                    r.name[0].value : r.name;\n                // Properties don't overwrite as they can merge\n                if (!hash[`$${name}`]) {\n                    hash[`$${name}`] = [ r ];\n                }\n                else {\n                    hash[`$${name}`].push(r);\n                }\n            }\n            return hash;\n        }, {});\n    }\n    return this._properties;\n};\n\nRuleset.prototype.variable = function(name) {\n    const decl = this.variables()[name];\n    if (decl) {\n        return this.parseValue(decl);\n    }\n};\n\nRuleset.prototype.property = function(name) {\n    const decl = this.properties()[name];\n    if (decl) {\n        return this.parseValue(decl);\n    }\n};\n\nRuleset.prototype.lastDeclaration = function() {\n    for (let i = this.rules.length; i > 0; i--) {\n        const decl = this.rules[i - 1];\n        if (decl instanceof Declaration) {\n            return this.parseValue(decl);\n        }\n    }\n};\n\nRuleset.prototype.parseValue = function(toParse) {\n    const self = this;\n    function transformDeclaration(decl) {\n        if (decl.value instanceof Anonymous && !decl.parsed) {\n            if (typeof decl.value.value === 'string') {\n                this.parse.parseNode(\n                    decl.value.value,\n                    ['value', 'important'], \n                    decl.value.getIndex(), \n                    decl.fileInfo(), \n                    (err, result) => {\n                        if (err) {\n                            decl.parsed = true;\n                        }\n                        if (result) {\n                            decl.value = result[0];\n                            decl.important = result[1] || '';\n                            decl.parsed = true;\n                        }\n                    });\n            } else {\n                decl.parsed = true;\n            }\n\n            return decl;\n        }\n        else {\n            return decl;\n        }\n    }\n    if (!Array.isArray(toParse)) {\n        return transformDeclaration.call(self, toParse);\n    }\n    else {\n        const nodes = [];\n        toParse.forEach(n => {\n            nodes.push(transformDeclaration.call(self, n));\n        });\n        return nodes;\n    }\n};\n\nRuleset.prototype.rulesets = function() {\n    if (!this.rules) { return []; }\n\n    const filtRules = [];\n    const rules = this.rules;\n    let i;\n    let rule;\n\n    for (i = 0; (rule = rules[i]); i++) {\n        if (rule.isRuleset) {\n            filtRules.push(rule);\n        }\n    }\n\n    return filtRules;\n};\n\nRuleset.prototype.prependRule = function(rule) {\n    const rules = this.rules;\n    if (rules) {\n        rules.unshift(rule);\n    } else {\n        this.rules = [ rule ];\n    }\n    this.setParent(rule, this);\n};\n\nRuleset.prototype.find = function(selector, self = this, filter) {\n    const rules = [];\n    let match;\n    let foundMixins;\n    const key = selector.toCSS();\n\n    if (key in this._lookups) { return this._lookups[key]; }\n\n    this.rulesets().forEach(rule => {\n        if (rule !== self) {\n            for (let j = 0; j < rule.selectors.length; j++) {\n                match = selector.match(rule.selectors[j]);\n                if (match) {\n                    if (selector.elements.length > match) {\n                        if (!filter || filter(rule)) {\n                            foundMixins = rule.find(new Selector(selector.elements.slice(match)), self, filter);\n                            for (let i = 0; i < foundMixins.length; ++i) {\n                                foundMixins[i].path.push(rule);\n                            }\n                            Array.prototype.push.apply(rules, foundMixins);\n                        }\n                    } else {\n                        rules.push({ rule, path: []});\n                    }\n                    break;\n                }\n            }\n        }\n    });\n    this._lookups[key] = rules;\n    return rules;\n};\n\nRuleset.prototype.genCSS = function(context, output) {\n    let i;\n    let j;\n    const charsetRuleNodes = [];\n    let ruleNodes = [];\n\n    let // Line number debugging\n        debugInfo;\n\n    let rule;\n    let path;\n\n    context.tabLevel = (context.tabLevel || 0);\n\n    if (!this.root) {\n        context.tabLevel++;\n    }\n\n    const tabRuleStr = context.compress ? '' : Array(context.tabLevel + 1).join('  ');\n    const tabSetStr = context.compress ? '' : Array(context.tabLevel).join('  ');\n    let sep;\n\n    let charsetNodeIndex = 0;\n    let importNodeIndex = 0;\n    for (i = 0; (rule = this.rules[i]); i++) {\n        if (rule instanceof Comment) {\n            if (importNodeIndex === i) {\n                importNodeIndex++;\n            }\n            ruleNodes.push(rule);\n        } else if (rule.isCharset && rule.isCharset()) {\n            ruleNodes.splice(charsetNodeIndex, 0, rule);\n            charsetNodeIndex++;\n            importNodeIndex++;\n        } else if (rule.type === 'Import') {\n            ruleNodes.splice(importNodeIndex, 0, rule);\n            importNodeIndex++;\n        } else {\n            ruleNodes.push(rule);\n        }\n    }\n    ruleNodes = charsetRuleNodes.concat(ruleNodes);\n\n    // If this is the root node, we don't render\n    // a selector, or {}.\n    if (!this.root) {\n        debugInfo = getDebugInfo(context, this, tabSetStr);\n\n        if (debugInfo) {\n            output.add(debugInfo);\n            output.add(tabSetStr);\n        }\n\n        const paths = this.paths;\n        const pathCnt = paths.length;\n        let pathSubCnt;\n\n        sep = context.compress ? ',' : (`,\\n${tabSetStr}`);\n\n        for (i = 0; i < pathCnt; i++) {\n            path = paths[i];\n            if (!(pathSubCnt = path.length)) { continue; }\n            if (i > 0) { output.add(sep); }\n\n            context.firstSelector = true;\n            path[0].genCSS(context, output);\n\n            context.firstSelector = false;\n            for (j = 1; j < pathSubCnt; j++) {\n                path[j].genCSS(context, output);\n            }\n        }\n\n        output.add((context.compress ? '{' : ' {\\n') + tabRuleStr);\n    }\n\n    // Compile rules and rulesets\n    for (i = 0; (rule = ruleNodes[i]); i++) {\n\n        if (i + 1 === ruleNodes.length) {\n            context.lastRule = true;\n        }\n\n        const currentLastRule = context.lastRule;\n        if (rule.isRulesetLike(rule)) {\n            context.lastRule = false;\n        }\n\n        if (rule.genCSS) {\n            rule.genCSS(context, output);\n        } else if (rule.value) {\n            output.add(rule.value.toString());\n        }\n\n        context.lastRule = currentLastRule;\n\n        if (!context.lastRule && rule.isVisible()) {\n            output.add(context.compress ? '' : (`\\n${tabRuleStr}`));\n        } else {\n            context.lastRule = false;\n        }\n    }\n\n    if (!this.root) {\n        output.add((context.compress ? '}' : `\\n${tabSetStr}}`));\n        context.tabLevel--;\n    }\n\n    if (!output.isEmpty() && !context.compress && this.firstRoot) {\n        output.add('\\n');\n    }\n};\n\nRuleset.prototype.joinSelectors = function(paths, context, selectors) {\n    for (let s = 0; s < selectors.length; s++) {\n        this.joinSelector(paths, context, selectors[s]);\n    }\n};\n\nRuleset.prototype.joinSelector = function(paths, context, selector) {\n    function createParenthesis(elementsToPak, originalElement) {\n        let replacementParen;\n        let j;\n        if (elementsToPak.length === 0) {\n            replacementParen = new Paren(elementsToPak[0]);\n        } else {\n            const insideParent = new Array(elementsToPak.length);\n            for (j = 0; j < elementsToPak.length; j++) {\n                insideParent[j] = new Element(\n                    null,\n                    elementsToPak[j],\n                    originalElement.isVariable,\n                    originalElement._index,\n                    originalElement._fileInfo\n                );\n            }\n            replacementParen = new Paren(new Selector(insideParent));\n        }\n        return replacementParen;\n    }\n\n    function createSelector(containedElement, originalElement) {\n        let element;\n        let selector;\n        element = new Element(null, containedElement, originalElement.isVariable, originalElement._index, originalElement._fileInfo);\n        selector = new Selector([element]);\n        return selector;\n    }\n\n    // joins selector path from `beginningPath` with selector path in `addPath`\n    // `replacedElement` contains element that is being replaced by `addPath`\n    // returns concatenated path\n    function addReplacementIntoPath(beginningPath, addPath, replacedElement, originalSelector) {\n        let newSelectorPath;\n        let lastSelector;\n        let newJoinedSelector;\n        // our new selector path\n        newSelectorPath = [];\n\n        // construct the joined selector - if & is the first thing this will be empty,\n        // if not newJoinedSelector will be the last set of elements in the selector\n        if (beginningPath.length > 0) {\n            newSelectorPath = utils.copyArray(beginningPath);\n            lastSelector = newSelectorPath.pop();\n            newJoinedSelector = originalSelector.createDerived(utils.copyArray(lastSelector.elements));\n        }\n        else {\n            newJoinedSelector = originalSelector.createDerived([]);\n        }\n\n        if (addPath.length > 0) {\n            // /deep/ is a CSS4 selector - (removed, so should deprecate)\n            // that is valid without anything in front of it\n            // so if the & does not have a combinator that is \"\" or \" \" then\n            // and there is a combinator on the parent, then grab that.\n            // this also allows + a { & .b { .a & { ... though not sure why you would want to do that\n            let combinator = replacedElement.combinator;\n\n            const parentEl = addPath[0].elements[0];\n            if (combinator.emptyOrWhitespace && !parentEl.combinator.emptyOrWhitespace) {\n                combinator = parentEl.combinator;\n            }\n            // join the elements so far with the first part of the parent\n            newJoinedSelector.elements.push(new Element(\n                combinator,\n                parentEl.value,\n                replacedElement.isVariable,\n                replacedElement._index,\n                replacedElement._fileInfo\n            ));\n            newJoinedSelector.elements = newJoinedSelector.elements.concat(addPath[0].elements.slice(1));\n        }\n\n        // now add the joined selector - but only if it is not empty\n        if (newJoinedSelector.elements.length !== 0) {\n            newSelectorPath.push(newJoinedSelector);\n        }\n\n        // put together the parent selectors after the join (e.g. the rest of the parent)\n        if (addPath.length > 1) {\n            let restOfPath = addPath.slice(1);\n            restOfPath = restOfPath.map(selector => selector.createDerived(selector.elements, []));\n            newSelectorPath = newSelectorPath.concat(restOfPath);\n        }\n        return newSelectorPath;\n    }\n\n    // joins selector path from `beginningPath` with every selector path in `addPaths` array\n    // `replacedElement` contains element that is being replaced by `addPath`\n    // returns array with all concatenated paths\n    function addAllReplacementsIntoPath( beginningPath, addPaths, replacedElement, originalSelector, result) {\n        let j;\n        for (j = 0; j < beginningPath.length; j++) {\n            const newSelectorPath = addReplacementIntoPath(beginningPath[j], addPaths, replacedElement, originalSelector);\n            result.push(newSelectorPath);\n        }\n        return result;\n    }\n\n    function mergeElementsOnToSelectors(elements, selectors) {\n        let i;\n        let sel;\n\n        if (elements.length === 0) {\n            return ;\n        }\n        if (selectors.length === 0) {\n            selectors.push([ new Selector(elements) ]);\n            return;\n        }\n\n        for (i = 0; (sel = selectors[i]); i++) {\n            // if the previous thing in sel is a parent this needs to join on to it\n            if (sel.length > 0) {\n                sel[sel.length - 1] = sel[sel.length - 1].createDerived(sel[sel.length - 1].elements.concat(elements));\n            }\n            else {\n                sel.push(new Selector(elements));\n            }\n        }\n    }\n\n    // replace all parent selectors inside `inSelector` by content of `context` array\n    // resulting selectors are returned inside `paths` array\n    // returns true if `inSelector` contained at least one parent selector\n    function replaceParentSelector(paths, context, inSelector) {\n        // The paths are [[Selector]]\n        // The first list is a list of comma separated selectors\n        // The inner list is a list of inheritance separated selectors\n        // e.g.\n        // .a, .b {\n        //   .c {\n        //   }\n        // }\n        // == [[.a] [.c]] [[.b] [.c]]\n        //\n        let i;\n\n        let j;\n        let k;\n        let currentElements;\n        let newSelectors;\n        let selectorsMultiplied;\n        let sel;\n        let el;\n        let hadParentSelector = false;\n        let length;\n        let lastSelector;\n        function findNestedSelector(element) {\n            let maybeSelector;\n            if (!(element.value instanceof Paren)) {\n                return null;\n            }\n\n            maybeSelector = element.value.value;\n            if (!(maybeSelector instanceof Selector)) {\n                return null;\n            }\n\n            return maybeSelector;\n        }\n\n        // the elements from the current selector so far\n        currentElements = [];\n        // the current list of new selectors to add to the path.\n        // We will build it up. We initiate it with one empty selector as we \"multiply\" the new selectors\n        // by the parents\n        newSelectors = [\n            []\n        ];\n\n        for (i = 0; (el = inSelector.elements[i]); i++) {\n            // non parent reference elements just get added\n            if (el.value !== '&') {\n                const nestedSelector = findNestedSelector(el);\n                if (nestedSelector != null) {\n                    // merge the current list of non parent selector elements\n                    // on to the current list of selectors to add\n                    mergeElementsOnToSelectors(currentElements, newSelectors);\n\n                    const nestedPaths = [];\n                    let replaced;\n                    const replacedNewSelectors = [];\n                    replaced = replaceParentSelector(nestedPaths, context, nestedSelector);\n                    hadParentSelector = hadParentSelector || replaced;\n                    // the nestedPaths array should have only one member - replaceParentSelector does not multiply selectors\n                    for (k = 0; k < nestedPaths.length; k++) {\n                        const replacementSelector = createSelector(createParenthesis(nestedPaths[k], el), el);\n                        addAllReplacementsIntoPath(newSelectors, [replacementSelector], el, inSelector, replacedNewSelectors);\n                    }\n                    newSelectors = replacedNewSelectors;\n                    currentElements = [];\n                } else {\n                    currentElements.push(el);\n                }\n\n            } else {\n                hadParentSelector = true;\n                // the new list of selectors to add\n                selectorsMultiplied = [];\n\n                // merge the current list of non parent selector elements\n                // on to the current list of selectors to add\n                mergeElementsOnToSelectors(currentElements, newSelectors);\n\n                // loop through our current selectors\n                for (j = 0; j < newSelectors.length; j++) {\n                    sel = newSelectors[j];\n                    // if we don't have any parent paths, the & might be in a mixin so that it can be used\n                    // whether there are parents or not\n                    if (context.length === 0) {\n                        // the combinator used on el should now be applied to the next element instead so that\n                        // it is not lost\n                        if (sel.length > 0) {\n                            sel[0].elements.push(new Element(el.combinator, '', el.isVariable, el._index, el._fileInfo));\n                        }\n                        selectorsMultiplied.push(sel);\n                    }\n                    else {\n                        // and the parent selectors\n                        for (k = 0; k < context.length; k++) {\n                            // We need to put the current selectors\n                            // then join the last selector's elements on to the parents selectors\n                            const newSelectorPath = addReplacementIntoPath(sel, context[k], el, inSelector);\n                            // add that to our new set of selectors\n                            selectorsMultiplied.push(newSelectorPath);\n                        }\n                    }\n                }\n\n                // our new selectors has been multiplied, so reset the state\n                newSelectors = selectorsMultiplied;\n                currentElements = [];\n            }\n        }\n\n        // if we have any elements left over (e.g. .a& .b == .b)\n        // add them on to all the current selectors\n        mergeElementsOnToSelectors(currentElements, newSelectors);\n\n        for (i = 0; i < newSelectors.length; i++) {\n            length = newSelectors[i].length;\n            if (length > 0) {\n                paths.push(newSelectors[i]);\n                lastSelector = newSelectors[i][length - 1];\n                newSelectors[i][length - 1] = lastSelector.createDerived(lastSelector.elements, inSelector.extendList);\n            }\n        }\n\n        return hadParentSelector;\n    }\n\n    function deriveSelector(visibilityInfo, deriveFrom) {\n        const newSelector = deriveFrom.createDerived(deriveFrom.elements, deriveFrom.extendList, deriveFrom.evaldCondition);\n        newSelector.copyVisibilityInfo(visibilityInfo);\n        return newSelector;\n    }\n\n    // joinSelector code follows\n    let i;\n\n    let newPaths;\n    let hadParentSelector;\n\n    newPaths = [];\n    hadParentSelector = replaceParentSelector(newPaths, context, selector);\n\n    if (!hadParentSelector) {\n        if (context.length > 0) {\n            newPaths = [];\n            for (i = 0; i < context.length; i++) {\n\n                const concatenated = context[i].map(deriveSelector.bind(this, selector.visibilityInfo()));\n\n                concatenated.push(selector);\n                newPaths.push(concatenated);\n            }\n        }\n        else {\n            newPaths = [[selector]];\n        }\n    }\n\n    for (i = 0; i < newPaths.length; i++) {\n        paths.push(newPaths[i]);\n    }\n};\n\nRuleset.prototype.type = 'Ruleset';\nRuleset.prototype.isRuleset = true;\nexport default Ruleset;\n"]}