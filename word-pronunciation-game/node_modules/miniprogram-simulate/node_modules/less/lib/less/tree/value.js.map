{"version": 3, "file": "value.js", "sourceRoot": "", "sources": ["../../../src/less/tree/value.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAE1B,IAAM,KAAK,GAAG,UAAS,KAAK;IACxB,IAAI,CAAC,KAAK,EAAE;QACR,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACvD;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACvB,IAAI,CAAC,KAAK,GAAG,CAAE,KAAK,CAAE,CAAC;KAC1B;SACI;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAE7B,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO;IACrC,IAAI,IAAI,CAAC,KAAK,EAAE;QACZ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC/C;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACnC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACtC;SAAM;QACH,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAf,CAAe,CAAC,CAAC,CAAC;KAC1D;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAC7C,IAAI,CAAC,CAAC;IACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAC3B,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SAC1D;KACJ;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC;AAC/B,kBAAe,KAAK,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Value = function(value) {\n    if (!value) {\n        throw new Error('Value requires an array argument');\n    }\n    if (!Array.isArray(value)) {\n        this.value = [ value ];\n    }\n    else {\n        this.value = value;\n    }\n};\n\nValue.prototype = new Node();\n\nValue.prototype.accept = function(visitor) {\n    if (this.value) {\n        this.value = visitor.visitArray(this.value);\n    }\n};\n\nValue.prototype.eval = function(context) {\n    if (this.value.length === 1) {\n        return this.value[0].eval(context);\n    } else {\n        return new Value(this.value.map(v => v.eval(context)));\n    }\n};\n\nValue.prototype.genCSS = function(context, output) {\n    let i;\n    for (i = 0; i < this.value.length; i++) {\n        this.value[i].genCSS(context, output);\n        if (i + 1 < this.value.length) {\n            output.add((context && context.compress) ? ',' : ', ');\n        }\n    }\n};\n\nValue.prototype.type = 'Value';\nexport default Value;\n"]}