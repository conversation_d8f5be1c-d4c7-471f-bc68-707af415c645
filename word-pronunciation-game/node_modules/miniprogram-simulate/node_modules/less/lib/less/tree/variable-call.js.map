{"version": 3, "file": "variable-call.js", "sourceRoot": "", "sources": ["../../../src/less/tree/variable-call.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,wDAAkC;AAClC,sDAAgC;AAChC,wEAAiD;AACjD,6DAAsC;AAEtC,IAAM,YAAY,GAAG,UAAS,QAAQ,EAAE,KAAK,EAAE,eAAe;IAC1D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEpC,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IAC1C,IAAI,KAAK,CAAC;IACV,IAAI,eAAe,GAAG,IAAI,kBAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAClG,IAAM,KAAK,GAAG,IAAI,oBAAS,CAAC,EAAC,OAAO,EAAE,sCAAoC,IAAI,CAAC,QAAU,EAAC,CAAC,CAAC;IAE5F,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;QAC1B,IAAI,eAAe,CAAC,KAAK,EAAE;YACvB,KAAK,GAAG,eAAe,CAAC;SAC3B;aACI,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;YACrC,KAAK,GAAG,IAAI,iBAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;SAC5C;aACI,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;YAC3C,KAAK,GAAG,IAAI,iBAAO,CAAC,EAAE,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;SAClD;aACI;YACD,MAAM,KAAK,CAAC;SACf;QACD,eAAe,GAAG,IAAI,0BAAe,CAAC,KAAK,CAAC,CAAC;KAChD;IAED,IAAI,eAAe,CAAC,OAAO,EAAE;QACzB,OAAO,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;KAC5C;IACD,MAAM,KAAK,CAAC;AAChB,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC;AAC7C,kBAAe,YAAY,CAAC", "sourcesContent": ["import Node from './node';\nimport Variable from './variable';\nimport Ruleset from './ruleset';\nimport DetachedRuleset from './detached-ruleset';\nimport LessError from '../less-error';\n\nconst VariableCall = function(variable, index, currentFileInfo) {\n    this.variable = variable;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.allowRoot = true;\n};\n\nVariableCall.prototype = new Node();\n\nVariableCall.prototype.eval = function(context) {\n    let rules;\n    let detachedRuleset = new Variable(this.variable, this.getIndex(), this.fileInfo()).eval(context);\n    const error = new LessError({message: `Could not evaluate variable call ${this.variable}`});\n\n    if (!detachedRuleset.ruleset) {\n        if (detachedRuleset.rules) {\n            rules = detachedRuleset;\n        }\n        else if (Array.isArray(detachedRuleset)) {\n            rules = new Ruleset('', detachedRuleset);\n        }\n        else if (Array.isArray(detachedRuleset.value)) {\n            rules = new Ruleset('', detachedRuleset.value);\n        }\n        else {\n            throw error;\n        }\n        detachedRuleset = new DetachedRuleset(rules);\n    }\n\n    if (detachedRuleset.ruleset) {\n        return detachedRuleset.callEval(context);\n    }\n    throw error;\n};\n\nVariableCall.prototype.type = 'VariableCall';\nexport default VariableCall;\n"]}