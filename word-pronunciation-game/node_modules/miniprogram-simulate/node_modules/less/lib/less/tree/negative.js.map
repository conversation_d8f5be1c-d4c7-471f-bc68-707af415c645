{"version": 3, "file": "negative.js", "sourceRoot": "", "sources": ["../../../src/less/tree/negative.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAC1B,0DAAoC;AACpC,0DAAoC;AAEpC,IAAM,QAAQ,GAAG,UAAS,IAAI;IAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,CAAC,CAAA;AAED,QAAQ,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AAEhC,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,UAAS,OAAO,EAAE,MAAM;IAChD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAS,OAAO;IACtC,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE;QACpB,OAAO,CAAC,IAAI,mBAAS,CAAC,GAAG,EAAE,CAAC,IAAI,mBAAS,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC9E;IACD,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC;AACrC,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Operation from './operation';\nimport Dimension from './dimension';\n\nconst Negative = function(node) {\n    this.value = node;\n}\n\nNegative.prototype = new Node();\n\nNegative.prototype.genCSS = function(context, output) {\n    output.add('-');\n    this.value.genCSS(context, output);\n};\n\nNegative.prototype.eval = function(context) {\n    if (context.isMathOn()) {\n        return (new Operation('*', [new Dimension(-1), this.value])).eval(context);\n    }\n    return new Negative(this.value.eval(context));\n};\n\nNegative.prototype.type = 'Negative';\nexport default Negative;\n"]}