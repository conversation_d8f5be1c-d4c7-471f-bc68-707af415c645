{"version": 3, "file": "unicode-descriptor.js", "sourceRoot": "", "sources": ["../../../src/less/tree/unicode-descriptor.js"], "names": [], "mappings": ";;;;;AAAA,gDAA0B;AAE1B,IAAM,iBAAiB,GAAG,UAAS,KAAK;IACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,CAAC,CAAA;AAED,iBAAiB,CAAC,SAAS,GAAG,IAAI,cAAI,EAAE,CAAC;AACzC,iBAAiB,CAAC,SAAS,CAAC,IAAI,GAAG,mBAAmB,CAAC;AAEvD,kBAAe,iBAAiB,CAAC", "sourcesContent": ["import Node from './node';\n\nconst UnicodeDescriptor = function(value) {\n    this.value = value;\n}\n\nUnicodeDescriptor.prototype = new Node();\nUnicodeDescriptor.prototype.type = 'UnicodeDescriptor';\n\nexport default UnicodeDescriptor;\n"]}