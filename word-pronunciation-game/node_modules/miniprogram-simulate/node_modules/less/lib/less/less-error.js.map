{"version": 3, "file": "less-error.js", "sourceRoot": "", "sources": ["../../src/less/less-error.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,6CAAiC;AAEjC,IAAM,aAAa,GAAG,oCAAoC,CAAC;AAE3D;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,IAAM,SAAS,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,cAAc,EAAE,eAAe;IACnE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,IAAI,eAAe,CAAC;IAE/C,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;IACzB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;IAErB,IAAI,cAAc,IAAI,QAAQ,EAAE;QAC5B,IAAM,KAAK,GAAG,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAM,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,IAAM,GAAG,GAAI,GAAG,CAAC,MAAM,CAAC;QACxB,IAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC;QACjE,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE7C,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,QAAQ,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvD,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;QAElB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAE9C;;;;;;eAMG;YACH,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;YACpD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI;gBACA,IAAI,EAAE,CAAC;aACV;YAAC,OAAO,CAAC,EAAE;gBACR,IAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBAC3C,IAAM,MAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,UAAU,GAAG,CAAC,GAAG,MAAI,CAAC;aACzB;YAED,IAAI,KAAK,EAAE;gBACP,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;oBACV,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;iBAC/C;gBACD,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;oBACV,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpC;aACJ;SACJ;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEnC,IAAI,CAAC,OAAO,GAAG;YACX,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YACpB,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YACpB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;SACnB,CAAC;KACL;AAEL,CAAC,CAAC;AAEF,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE;IACtC,IAAM,CAAC,GAAG,cAAO,CAAC,CAAC;IACnB,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IAC9B,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC;CACjC;KAAM;IACH,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;CACxD;AAED,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC;AAE5C;;;;;;GAMG;AACH,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAS,OAAY;IAAZ,wBAAA,EAAA,YAAY;IAChD,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;IACnC,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,OAAO,GAAG,UAAA,GAAG,IAAI,OAAA,GAAG,EAAH,CAAG,CAAC;IACzB,IAAI,OAAO,CAAC,OAAO,EAAE;QACjB,IAAM,IAAI,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC;QACpC,IAAI,IAAI,KAAK,UAAU,EAAE;YACrB,MAAM,KAAK,CAAC,iDAA+C,IAAI,MAAG,CAAC,CAAC;SACvE;QACD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;KAC7B;IAED,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;QACpB,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAChC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAI,IAAI,CAAC,IAAI,GAAG,CAAC,SAAI,OAAO,CAAC,CAAC,CAAG,EAAE,MAAM,CAAC,CAAC,CAAC;SACjE;QAED,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAChC,IAAI,QAAQ,GAAM,IAAI,CAAC,IAAI,MAAG,CAAC;YAC/B,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACZ,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;oBACxC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;wBAC9D,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;aACjE;YACD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxB;QAED,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAChC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAI,IAAI,CAAC,IAAI,GAAG,CAAC,SAAI,OAAO,CAAC,CAAC,CAAG,EAAE,MAAM,CAAC,CAAC,CAAC;SACjE;QACD,KAAK,GAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,OAAI,CAAC;KAC1D;IAED,OAAO,IAAI,OAAO,CAAI,IAAI,CAAC,IAAI,eAAU,IAAI,CAAC,OAAS,EAAE,KAAK,CAAC,CAAC;IAChE,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;KACrD;IACD,IAAI,IAAI,CAAC,IAAI,EAAE;QACX,OAAO,IAAI,OAAO,CAAC,cAAY,IAAI,CAAC,IAAI,kBAAY,IAAI,CAAC,MAAM,GAAG,CAAC,OAAG,EAAE,MAAM,CAAC,CAAC;KACnF;IAED,OAAO,IAAI,OAAK,KAAO,CAAC;IAExB,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,OAAO,IAAO,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,OAAI,CAAC;QAClE,OAAO,IAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAI,IAAI,CAAC,WAAW,OAAI,CAAC;KACxE;IAED,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,kBAAe,SAAS,CAAC", "sourcesContent": ["import * as utils from './utils';\n\nconst anonymousFunc = /(<anonymous>|Function):(\\d+):(\\d+)/;\n\n/**\n * This is a centralized class of any error that could be thrown internally (mostly by the parser).\n * Besides standard .message it keeps some additional data like a path to the file where the error\n * occurred along with line and column numbers.\n *\n * @class\n * @extends Error\n * @type {module.LessError}\n *\n * @prop {string} type\n * @prop {string} filename\n * @prop {number} index\n * @prop {number} line\n * @prop {number} column\n * @prop {number} callLine\n * @prop {number} callExtract\n * @prop {string[]} extract\n *\n * @param {Object} e              - An error object to wrap around or just a descriptive object\n * @param {Object} fileContentMap - An object with file contents in 'contents' property (like importManager) @todo - move to fileManager?\n * @param {string} [currentFilename]\n */\nconst LessError = function LessError(e, fileContentMap, currentFilename) {\n    Error.call(this);\n\n    const filename = e.filename || currentFilename;\n\n    this.message = e.message;\n    this.stack = e.stack;\n\n    if (fileContentMap && filename) {\n        const input = fileContentMap.contents[filename];\n        const loc = utils.getLocation(e.index, input);\n        const line = loc.line;\n        const col  = loc.column;\n        const callLine = e.call && utils.getLocation(e.call, input).line;\n        const lines = input ? input.split('\\n') : '';\n\n        this.type = e.type || 'Syntax';\n        this.filename = filename;\n        this.index = e.index;\n        this.line = typeof line === 'number' ? line + 1 : null;\n        this.column = col;\n\n        if (!this.line && this.stack) {\n            const found = this.stack.match(anonymousFunc);\n\n            /**\n             * We have to figure out how this environment stringifies anonymous functions\n             * so we can correctly map plugin errors.\n             * \n             * Note, in Node 8, the output of anonymous funcs varied based on parameters\n             * being present or not, so we inject dummy params.\n             */\n            const func = new Function('a', 'throw new Error()');\n            let lineAdjust = 0;\n            try {\n                func();\n            } catch (e) {\n                const match = e.stack.match(anonymousFunc);\n                const line = parseInt(match[2]);\n                lineAdjust = 1 - line;\n            }\n\n            if (found) {\n                if (found[2]) {\n                    this.line = parseInt(found[2]) + lineAdjust;\n                }\n                if (found[3]) {\n                    this.column = parseInt(found[3]);\n                }\n            }\n        }\n\n        this.callLine = callLine + 1;\n        this.callExtract = lines[callLine];\n\n        this.extract = [\n            lines[this.line - 2],\n            lines[this.line - 1],\n            lines[this.line]\n        ];\n    }\n\n};\n\nif (typeof Object.create === 'undefined') {\n    const F = () => {};\n    F.prototype = Error.prototype;\n    LessError.prototype = new F();\n} else {\n    LessError.prototype = Object.create(Error.prototype);\n}\n\nLessError.prototype.constructor = LessError;\n\n/**\n * An overridden version of the default Object.prototype.toString\n * which uses additional information to create a helpful message.\n *\n * @param {Object} options\n * @returns {string}\n */\nLessError.prototype.toString = function(options = {}) {\n    let message = '';\n    const extract = this.extract || [];\n    let error = [];\n    let stylize = str => str;\n    if (options.stylize) {\n        const type = typeof options.stylize;\n        if (type !== 'function') {\n            throw Error(`options.stylize should be a function, got a ${type}!`);\n        }\n        stylize = options.stylize;\n    }\n\n    if (this.line !== null) {\n        if (typeof extract[0] === 'string') {\n            error.push(stylize(`${this.line - 1} ${extract[0]}`, 'grey'));\n        }\n\n        if (typeof extract[1] === 'string') {\n            let errorTxt = `${this.line} `;\n            if (extract[1]) {\n                errorTxt += extract[1].slice(0, this.column) +\n                    stylize(stylize(stylize(extract[1].substr(this.column, 1), 'bold') +\n                        extract[1].slice(this.column + 1), 'red'), 'inverse');\n            }\n            error.push(errorTxt);\n        }\n\n        if (typeof extract[2] === 'string') {\n            error.push(stylize(`${this.line + 1} ${extract[2]}`, 'grey'));\n        }\n        error = `${error.join('\\n') + stylize('', 'reset')}\\n`;\n    }\n\n    message += stylize(`${this.type}Error: ${this.message}`, 'red');\n    if (this.filename) {\n        message += stylize(' in ', 'red') + this.filename;\n    }\n    if (this.line) {\n        message += stylize(` on line ${this.line}, column ${this.column + 1}:`, 'grey');\n    }\n\n    message += `\\n${error}`;\n\n    if (this.callLine) {\n        message += `${stylize('from ', 'red') + (this.filename || '')}/n`;\n        message += `${stylize(this.callLine, 'grey')} ${this.callExtract}/n`;\n    }\n\n    return message;\n};\n\nexport default LessError;"]}