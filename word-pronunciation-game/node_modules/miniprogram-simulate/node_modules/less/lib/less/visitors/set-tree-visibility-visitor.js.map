{"version": 3, "file": "set-tree-visibility-visitor.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/set-tree-visibility-visitor.js"], "names": [], "mappings": ";;AAAA;IACI,kCAAY,OAAO;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAED,sCAAG,GAAH,UAAI,IAAI;QACJ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAED,6CAAU,GAAV,UAAW,KAAK;QACZ,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;QAED,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,CAAC;QACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACxB;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,wCAAK,GAAL,UAAM,IAAI;QACN,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;YAC5B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;YACnD,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;aAAM;YACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IACL,+BAAC;AAAD,CAAC,AA1CD,IA0CC;AAED,kBAAe,wBAAwB,CAAC", "sourcesContent": ["class SetTreeVisibilityVisitor {\n    constructor(visible) {\n        this.visible = visible;\n    }\n\n    run(root) {\n        this.visit(root);\n    }\n\n    visitArray(nodes) {\n        if (!nodes) {\n            return nodes;\n        }\n\n        const cnt = nodes.length;\n        let i;\n        for (i = 0; i < cnt; i++) {\n            this.visit(nodes[i]);\n        }\n        return nodes;\n    }\n\n    visit(node) {\n        if (!node) {\n            return node;\n        }\n        if (node.constructor === Array) {\n            return this.visitArray(node);\n        }\n\n        if (!node.blocksVisibility || node.blocksVisibility()) {\n            return node;\n        }\n        if (this.visible) {\n            node.ensureVisibility();\n        } else {\n            node.ensureInvisibility();\n        }\n\n        node.accept(this);\n        return node;\n    }\n}\n\nexport default SetTreeVisibilityVisitor;"]}