{"version": 3, "file": "transform-tree.js", "sourceRoot": "", "sources": ["../../src/less/transform-tree.js"], "names": [], "mappings": ";;;;;AAAA,wDAAkC;AAClC,wDAAiC;AACjC,gDAA0B;AAE1B,mBAAe,UAAC,IAAI,EAAE,OAAY;IAAZ,wBAAA,EAAA,YAAY;IAC9B,IAAI,SAAS,CAAC;IACd,IAAI,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IAClC,IAAM,OAAO,GAAG,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE3C,EAAE;IACF,4CAA4C;IAC5C,EAAE;IACF,qDAAqD;IACrD,EAAE;IACF,mCAAmC;IACnC,uBAAuB;IACvB,8BAA8B;IAC9B,iCAAiC;IACjC,WAAW;IACX,SAAS;IACT,MAAM;IACN,EAAE;IACF,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QAC5D,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC;YACpC,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAEzB,IAAI,CAAC,CAAC,KAAK,YAAY,cAAI,CAAC,KAAK,CAAC,EAAE;gBAChC,IAAI,CAAC,CAAC,KAAK,YAAY,cAAI,CAAC,UAAU,CAAC,EAAE;oBACrC,KAAK,GAAG,IAAI,cAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;iBACxC;gBACD,KAAK,GAAG,IAAI,cAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;aACnC;YACD,OAAO,IAAI,cAAI,CAAC,WAAW,CAAC,MAAI,CAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,cAAI,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;KACxD;IAED,IAAM,QAAQ,GAAG;QACb,IAAI,kBAAO,CAAC,mBAAmB,EAAE;QACjC,IAAI,kBAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC;QAC7C,IAAI,kBAAO,CAAC,aAAa,EAAE;QAC3B,IAAI,kBAAO,CAAC,YAAY,CAAC,EAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAC,CAAC;KAClE,CAAC;IAEF,IAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,IAAI,CAAC,CAAC;IACN,IAAI,eAAe,CAAC;IAEpB;;;;OAIG;IACH,IAAI,OAAO,CAAC,aAAa,EAAE;QACvB,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,eAAe,CAAC,KAAK,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE;gBAChC,IAAI,CAAC,CAAC,gBAAgB,EAAE;oBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC9C,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;qBACf;iBACJ;qBACI;oBACD,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;wBACvC,IAAI,CAAC,CAAC,YAAY,EAAE;4BAChB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;yBACvB;6BACI;4BACD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACpB;qBACJ;iBACJ;aACJ;SACJ;KACJ;IAED,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KAC9B;IAED,mDAAmD;IACnD,IAAI,OAAO,CAAC,aAAa,EAAE;QACvB,eAAe,CAAC,KAAK,EAAE,CAAC;QACxB,OAAO,CAAC,CAAC,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE;YAChC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;gBACjE,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aACpB;SACJ;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,EAAC", "sourcesContent": ["import contexts from './contexts';\nimport visitor from './visitors';\nimport tree from './tree';\n\nexport default (root, options = {}) => {\n    let evaldRoot;\n    let variables = options.variables;\n    const evalEnv = new contexts.Eval(options);\n\n    //\n    // Allows setting variables with a hash, so:\n    //\n    //   `{ color: new tree.Color('#f01') }` will become:\n    //\n    //   new tree.Declaration('@color',\n    //     new tree.Value([\n    //       new tree.Expression([\n    //         new tree.Color('#f01')\n    //       ])\n    //     ])\n    //   )\n    //\n    if (typeof variables === 'object' && !Array.isArray(variables)) {\n        variables = Object.keys(variables).map(k => {\n            let value = variables[k];\n\n            if (!(value instanceof tree.Value)) {\n                if (!(value instanceof tree.Expression)) {\n                    value = new tree.Expression([value]);\n                }\n                value = new tree.Value([value]);\n            }\n            return new tree.Declaration(`@${k}`, value, false, null, 0);\n        });\n        evalEnv.frames = [new tree.Ruleset(null, variables)];\n    }\n\n    const visitors = [\n        new visitor.JoinSelectorVisitor(),\n        new visitor.MarkVisibleSelectorsVisitor(true),\n        new visitor.ExtendVisitor(),\n        new visitor.ToCSSVisitor({compress: Boolean(options.compress)})\n    ];\n\n    const preEvalVisitors = [];\n    let v;\n    let visitorIterator;\n\n    /**\n     * first() / get() allows visitors to be added while visiting\n     * \n     * @todo Add scoping for visitors just like functions for @plugin; right now they're global\n     */\n    if (options.pluginManager) {\n        visitorIterator = options.pluginManager.visitor();\n        for (var i = 0; i < 2; i++) {\n            visitorIterator.first();\n            while ((v = visitorIterator.get())) {\n                if (v.isPreEvalVisitor) {\n                    if (i === 0 || preEvalVisitors.indexOf(v) === -1) {\n                        preEvalVisitors.push(v);\n                        v.run(root);\n                    }\n                }\n                else {\n                    if (i === 0 || visitors.indexOf(v) === -1) {\n                        if (v.isPreVisitor) {\n                            visitors.unshift(v);\n                        }\n                        else {\n                            visitors.push(v);\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    evaldRoot = root.eval(evalEnv);\n\n    for (var i = 0; i < visitors.length; i++) {\n        visitors[i].run(evaldRoot);\n    }\n\n    // Run any remaining visitors added after eval pass\n    if (options.pluginManager) {\n        visitorIterator.first();\n        while ((v = visitorIterator.get())) {\n            if (visitors.indexOf(v) === -1 && preEvalVisitors.indexOf(v) === -1) {\n                v.run(evaldRoot);\n            }\n        }\n    }\n\n    return evaldRoot;\n};\n"]}