{"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["../../src/less-node/environment.js"], "names": [], "mappings": ";;AAAA,kBAAe;IACX,YAAY,EAAE,SAAS,YAAY,CAAC,GAAG;QACnC,yDAAyD;QACzD,IAAM,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpE,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IACD,UAAU,EAAE,UAAU,QAAQ;QAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD,aAAa,EAAE,UAAU,IAAI;QACzB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IACD,qBAAqB,EAAE,SAAS,qBAAqB;QACjD,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,kBAAkB,CAAC;IACpD,CAAC;CACJ,CAAC", "sourcesContent": ["export default {\n    encodeBase64: function encodeBase64(str) {\n        // Avoid Buffer constructor on newer versions of Node.js.\n        const buffer = (Buffer.from ? Buffer.from(str) : (new Buffer(str)));\n        return buffer.toString('base64');\n    },\n    mimeLookup: function (filename) {\n        return require('mime').lookup(filename);\n    },\n    charsetLookup: function (mime) {\n        return require('mime').charsets.lookup(mime);\n    },\n    getSourceMapGenerator: function getSourceMapGenerator() {\n        return require('source-map').SourceMapGenerator;\n    }\n};\n"]}