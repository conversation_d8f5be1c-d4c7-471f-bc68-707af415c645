{"version": 3, "file": "url-file-manager.js", "sourceRoot": "", "sources": ["../../src/less-node/url-file-manager.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAM,OAAO,GAAG,oBAAoB,CAAC;AACrC,4CAAsB;AACtB,IAAI,OAAO,CAAC;AACZ,0GAA+E;AAC/E,0DAAoC;AAEpC;IAA6B,kCAAmB;IAAhD;;IAqCA,CAAC;IApCG,iCAAQ,GAAR,UAAS,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;QACrD,OAAO,OAAO,CAAC,IAAI,CAAE,QAAQ,CAAE,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACtE,CAAC;IAED,iCAAQ,GAAR,UAAS,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;QACrD,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,IAAI,OAAO,KAAK,SAAS,EAAE;gBACvB,IAAI;oBAAE,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;iBAAE;gBAC5C,OAAO,CAAC,EAAE;oBAAE,OAAO,GAAG,IAAI,CAAC;iBAAE;aAChC;YACD,IAAI,CAAC,OAAO,EAAE;gBACV,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,0EAA0E,EAAE,CAAC,CAAC;gBAC9G,OAAO;aACV;YAED,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,CAAE,QAAQ,CAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAE3F,yCAAyC;YACzC,IAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAA;YAErE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,UAAC,KAAK,EAAE,IAAI,EAAE,MAAM;gBACxC,IAAI,MAAM,KAAK,GAAG,EAAE;oBAChB,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,eAAa,MAAM,sBAAmB,EAAE,CAAC,CAAC;oBAC1E,OAAO;iBACV;gBACD,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,eAAa,MAAM,8BAAyB,KAAK,OAAI,EAAE,CAAC,CAAC;oBACzF,OAAO;iBACV;gBACD,IAAI,CAAC,IAAI,EAAE;oBACP,gBAAM,CAAC,IAAI,CAAC,+BAA6B,MAAM,wBAAkB,MAAM,OAAG,CAAC,CAAC;iBAC/E;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IACL,qBAAC;AAAD,CAAC,AArCD,CAA6B,kCAAmB,GAqC/C;AAED,kBAAe,cAAc,CAAC", "sourcesContent": ["const isUrlRe = /^(?:https?:)?\\/\\//i;\nimport url from 'url';\nlet request;\nimport AbstractFileManager from '../less/environment/abstract-file-manager.js';\nimport logger from '../less/logger';\n\nclass UrlFileManager extends AbstractFileManager {\n    supports(filename, currentDirectory, options, environment) {\n        return isUrlRe.test( filename ) || isUrlRe.test(currentDirectory);\n    }\n\n    loadFile(filename, currentDirectory, options, environment) {\n        return new Promise((fulfill, reject) => {\n            if (request === undefined) {\n                try { request = require('native-request'); }\n                catch (e) { request = null; }\n            }\n            if (!request) {\n                reject({ type: 'File', message: 'optional dependency \\'native-request\\' required to import over http(s)\\n' });\n                return;\n            }\n\n            let urlStr = isUrlRe.test( filename ) ? filename : url.resolve(currentDirectory, filename);\n            \n            /** native-request currently has a bug */\n            const hackUrlStr = urlStr.indexOf('?') === -1 ? urlStr + '?' : urlStr\n\n            request.get(hackUrlStr, (error, body, status) => {\n                if (status === 404) {\n                    reject({ type: 'File', message: `resource '${urlStr}' was not found\\n` });\n                    return;\n                }\n                if (error) {\n                    reject({ type: 'File', message: `resource '${urlStr}' gave this Error:\\n  ${error}\\n` });\n                    return;\n                }\n                if (!body) {\n                    logger.warn(`Warning: Empty body (HTTP ${status}) returned by \"${urlStr}\"`);\n                }\n                fulfill({ contents: body, filename: urlStr });\n            });\n        });\n    }\n}\n\nexport default UrlFileManager;\n"]}