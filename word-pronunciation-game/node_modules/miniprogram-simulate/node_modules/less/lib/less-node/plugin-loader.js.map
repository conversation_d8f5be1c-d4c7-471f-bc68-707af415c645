{"version": 3, "file": "plugin-loader.js", "sourceRoot": "", "sources": ["../../src/less-node/plugin-loader.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,8CAAwB;AACxB,4GAAiF;AAEjF;;GAEG;AACH;IAA2B,gCAAoB;IAC3C,sBAAY,IAAI;QAAhB,YACI,iBAAO,SAeV;QAbG,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,KAAI,CAAC,OAAO,GAAG,UAAA,MAAM;YACjB,MAAM,GAAG,cAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9B,OAAO,UAAA,EAAE;gBACL,IAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5B,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE;oBAC9B,OAAO,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;iBACzC;qBACI;oBACD,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;iBACtB;YACL,CAAC,CAAC;QACN,CAAC,CAAC;;IACN,CAAC;IAED,iCAAU,GAAV,UAAW,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW;QAC5D,IAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAM,QAAQ,GAAG,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;QAChG,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;SAC3C;QAED,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;SAC7E;QAED,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,IAAI,CAC/D,UAAA,IAAI;gBACA,IAAI;oBACA,OAAO,CAAC,IAAI,CAAC,CAAC;iBACjB;gBACD,OAAO,CAAC,EAAE;oBACN,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACf,MAAM,CAAC,CAAC,CAAC,CAAC;iBACb;YACL,CAAC,CACJ,CAAC,KAAK,CAAC,UAAA,GAAG;gBACP,MAAM,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED,qCAAc,GAAd,UAAe,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW;QAChE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IAClF,CAAC;IACL,mBAAC;AAAD,CAAC,AAnDD,CAA2B,mCAAoB,GAmD9C;AAED,kBAAe,YAAY,CAAC", "sourcesContent": ["import path from 'path';\nimport AbstractPluginLoader from '../less/environment/abstract-plugin-loader.js';\n\n/**\n * Node Plugin Loader\n */\nclass PluginLoader extends AbstractPluginLoader {\n    constructor(less) {\n        super();\n\n        this.less = less;\n        this.require = prefix => {\n            prefix = path.dirname(prefix);\n            return id => {\n                const str = id.substr(0, 2);\n                if (str === '..' || str === './') {\n                    return require(path.join(prefix, id));\n                }\n                else {\n                    return require(id);\n                }\n            };\n        };\n    }\n\n    loadPlugin(filename, basePath, context, environment, fileManager) {\n        const prefix = filename.slice(0, 1);\n        const explicit = prefix === '.' || prefix === '/' || filename.slice(-3).toLowerCase() === '.js';\n        if (!explicit) {\n            context.prefixes = ['less-plugin-', ''];\n        }\n\n        if (context.syncImport) {\n            return fileManager.loadFileSync(filename, basePath, context, environment);\n        }\n\n        return new Promise((fulfill, reject) => {\n            fileManager.loadFile(filename, basePath, context, environment).then(\n                data => {\n                    try {\n                        fulfill(data);\n                    }\n                    catch (e) {\n                        console.log(e);\n                        reject(e);\n                    }\n                }\n            ).catch(err => {\n                reject(err);\n            });\n        });\n    }\n\n    loadPluginSync(filename, basePath, context, environment, fileManager) {\n        context.syncImport = true;\n        return this.loadPlugin(filename, basePath, context, environment, fileManager);\n    }\n}\n\nexport default PluginLoader;\n\n"]}