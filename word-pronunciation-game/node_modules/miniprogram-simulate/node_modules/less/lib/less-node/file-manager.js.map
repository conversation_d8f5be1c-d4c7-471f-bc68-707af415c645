{"version": 3, "file": "file-manager.js", "sourceRoot": "", "sources": ["../../src/less-node/file-manager.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,8CAAwB;AACxB,4CAAsB;AACtB,0GAA+E;AAE/E;IAA0B,+BAAmB;IAA7C;;IA4IA,CAAC;IA3IG,8BAAQ,GAAR;QACI,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,kCAAY,GAAZ;QACI,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,8BAAQ,GAAR,UAAS,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ;QAC/D,IAAI,YAAY,CAAC;QACjB,IAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAM,QAAQ,GAAG,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,CAAC;QAClD,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAM,SAAS,GAAG,QAAQ,CAAC;QAE3B,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAExB,IAAM,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAE7D,IAAI,OAAO,CAAC,KAAK,EAAE;YAAE,KAAK,CAAC,IAAI,OAAV,KAAK,EAAS,OAAO,CAAC,KAAK,EAAE;SAAE;QAEpD,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAAE;QAE1E,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAEjD,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YACpC,IAAI,QAAQ,EAAE;gBACV,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aAClC;iBACI;gBACD,OAAO,MAAM,CAAC;aACjB;SACJ;aACI;YACD,0CAA0C;YAC1C,2CAA2C;YAC3C,sDAAsD;YACtD,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC;SACnC;QAED,SAAS,UAAU,CAAC,IAAI;YACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB,MAAM,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;aAC5B;iBACI;gBACD,MAAM,GAAG,IAAI,CAAC;aACjB;QACL,CAAC;QAED,SAAS,WAAW,CAAC,OAAO,EAAE,MAAM;YAChC,CAAC,SAAS,YAAY,CAAC,CAAC;gBACpB,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE;oBAClB,CAAC,SAAS,SAAS,CAAC,CAAC;wBACjB,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE;4BACrB,YAAY,GAAG,KAAK,CAAC;4BACrB,YAAY,GAAG,SAAS,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC;4BAEpE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gCACV,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;6BACpD;4BAED,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gCAC/B,IAAI;oCACA,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oCAC7C,YAAY,GAAG,IAAI,CAAC;iCACvB;gCACD,OAAO,CAAC,EAAE;oCACN,cAAc,CAAC,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC;oCAC9C,gBAAgB,EAAE,CAAC;iCACtB;6BACJ;iCACI;gCACD,gBAAgB,EAAE,CAAC;6BACtB;4BAED,SAAS,gBAAgB;gCACrB,IAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;gCAEpG,IAAI,WAAW,KAAK,YAAY,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oCAC/D,IAAI;wCACA,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wCAC5C,YAAY,GAAG,IAAI,CAAC;qCACvB;oCACD,OAAO,CAAC,EAAE;wCACN,cAAc,CAAC,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;wCAC7C,YAAY,GAAG,WAAW,CAAC;qCAC9B;iCACJ;qCACI;oCACD,YAAY,GAAG,WAAW,CAAC;iCAC9B;4BACL,CAAC;4BAED,IAAM,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;4BACpC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gCACpB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;6BAC9B;4BACD,IAAI,OAAO,CAAC,UAAU,EAAE;gCACpB,IAAI;oCACA,IAAM,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;oCACvD,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAC,CAAC,CAAC;iCACtD;gCACD,OAAO,CAAC,EAAE;oCACN,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;oCAC5E,OAAO,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iCAC3B;6BACJ;iCACI;gCACD,YAAY,CAAC,IAAI,CAAC,UAAS,CAAC,EAAE,IAAI;oCAC9B,IAAI,CAAC,EAAE;wCACH,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;wCAC5E,OAAO,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;qCAC3B;oCACD,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAC,CAAC,CAAC;gCACvD,CAAC,CAAC,CAAC;gCACH,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;6BACzC;yBACJ;6BACI;4BACD,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;yBACvB;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACT;qBAAM;oBACH,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAI,QAAQ,gCAA2B,cAAc,CAAC,IAAI,CAAC,GAAG,CAAG,EAAE,CAAC,CAAC;iBACxG;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC;IACL,CAAC;IAED,kCAAY,GAAZ,UAAa,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;QACzD,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IAC3E,CAAC;IACL,kBAAC;AAAD,CAAC,AA5ID,CAA0B,kCAAmB,GA4I5C;AAED,kBAAe,WAAW,CAAC", "sourcesContent": ["import path from 'path';\nimport fs from './fs';\nimport AbstractFileManager from '../less/environment/abstract-file-manager.js';\n\nclass FileManager extends AbstractFileManager {\n    supports() {\n        return true;\n    }\n\n    supportsSync() {\n        return true;\n    }\n\n    loadFile(filename, currentDirectory, options, environment, callback) {\n        let fullFilename;\n        const isAbsoluteFilename = this.isPathAbsolute(filename);\n        const filenamesTried = [];\n        const self = this;\n        const prefix = filename.slice(0, 1);\n        const explicit = prefix === '.' || prefix === '/';\n        let result = null;\n        let isNodeModule = false;\n        const npmPrefix = 'npm://';\n\n        options = options || {};\n\n        const paths = isAbsoluteFilename ? [''] : [currentDirectory];\n\n        if (options.paths) { paths.push(...options.paths); }\n\n        if (!isAbsoluteFilename && paths.indexOf('.') === -1) { paths.push('.'); }\n\n        const prefixes = options.prefixes || [''];\n        const fileParts = this.extractUrlParts(filename);\n\n        if (options.syncImport) {\n            getFileData(returnData, returnData);\n            if (callback) {\n                callback(result.error, result);\n            }\n            else {\n                return result;\n            }\n        }\n        else {\n            // promise is guaranteed to be asyncronous\n            // which helps as it allows the file handle\n            // to be closed before it continues with the next file\n            return new Promise(getFileData);\n        }\n\n        function returnData(data) {\n            if (!data.filename) {\n                result = { error: data };\n            }\n            else {\n                result = data;\n            }\n        }\n\n        function getFileData(fulfill, reject) {\n            (function tryPathIndex(i) {\n                if (i < paths.length) {\n                    (function tryPrefix(j) {\n                        if (j < prefixes.length) {\n                            isNodeModule = false;\n                            fullFilename = fileParts.rawPath + prefixes[j] + fileParts.filename;\n\n                            if (paths[i]) {\n                                fullFilename = path.join(paths[i], fullFilename);\n                            }\n\n                            if (!explicit && paths[i] === '.') {\n                                try {\n                                    fullFilename = require.resolve(fullFilename);\n                                    isNodeModule = true;\n                                }\n                                catch (e) {\n                                    filenamesTried.push(npmPrefix + fullFilename);\n                                    tryWithExtension();\n                                }\n                            }\n                            else {\n                                tryWithExtension();\n                            }\n\n                            function tryWithExtension() {\n                                const extFilename = options.ext ? self.tryAppendExtension(fullFilename, options.ext) : fullFilename;\n\n                                if (extFilename !== fullFilename && !explicit && paths[i] === '.') {\n                                    try {\n                                        fullFilename = require.resolve(extFilename);\n                                        isNodeModule = true;\n                                    }\n                                    catch (e) {\n                                        filenamesTried.push(npmPrefix + extFilename);\n                                        fullFilename = extFilename;\n                                    }\n                                }\n                                else {\n                                    fullFilename = extFilename;\n                                }\n                            }\n                        \n                            const readFileArgs = [fullFilename];\n                            if (!options.rawBuffer) {\n                                readFileArgs.push('utf-8');\n                            }\n                            if (options.syncImport) {\n                                try {\n                                    const data = fs.readFileSync.apply(this, readFileArgs);\n                                    fulfill({ contents: data, filename: fullFilename});\n                                }\n                                catch (e) {\n                                    filenamesTried.push(isNodeModule ? npmPrefix + fullFilename : fullFilename);\n                                    return tryPrefix(j + 1);\n                                }\n                            }\n                            else {\n                                readFileArgs.push(function(e, data) {\n                                    if (e) {\n                                        filenamesTried.push(isNodeModule ? npmPrefix + fullFilename : fullFilename);\n                                        return tryPrefix(j + 1);\n                                    }\n                                    fulfill({ contents: data, filename: fullFilename});\n                                });\n                                fs.readFile.apply(this, readFileArgs);\n                            }\n                        }\n                        else {\n                            tryPathIndex(i + 1);\n                        }\n                    })(0);\n                } else {\n                    reject({ type: 'File', message: `'${filename}' wasn't found. Tried - ${filenamesTried.join(',')}` });\n                }\n            }(0));\n        }\n    }\n\n    loadFileSync(filename, currentDirectory, options, environment) {\n        options.syncImport = true;\n        return this.loadFile(filename, currentDirectory, options, environment);\n    }\n}\n\nexport default FileManager;"]}