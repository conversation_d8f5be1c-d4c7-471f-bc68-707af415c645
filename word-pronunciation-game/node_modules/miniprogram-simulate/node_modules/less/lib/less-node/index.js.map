{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/less-node/index.js"], "names": [], "mappings": ";;;;;AAAA,8DAAwC;AACxC,gEAAyC;AACzC,wEAAgD;AAChD,iDAA4C;AAC5C,gEAAyC;AACzC,kEAA2C;AAC3C,4CAAsB;AACtB,4EAAqD;AACrD,4DAAqC;AAErC,IAAM,IAAI,GAAG,cAAqB,CAAC,qBAAW,EAAE,CAAC,IAAI,sBAAW,EAAE,EAAE,IAAI,0BAAc,EAAE,CAAC,CAAC,CAAC;AAE3F,yDAAyD;AACzD,IAAI,CAAC,qBAAqB,GAAG,cAAqB,CAAC;AACnD,IAAI,CAAC,WAAW,GAAG,sBAAW,CAAC;AAC/B,IAAI,CAAC,YAAY,GAAG,uBAAY,CAAC;AACjC,IAAI,CAAC,EAAE,GAAG,YAAE,CAAC;AACb,IAAI,CAAC,WAAW,GAAG,sBAAW,CAAC;AAC/B,IAAI,CAAC,cAAc,GAAG,0BAAc,CAAC;AAErC,iBAAiB;AACjB,IAAI,CAAC,OAAO,GAAG,yBAAc,EAAE,CAAC;AAEhC,mCAAmC;AACnC,oBAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAE5B,kBAAe,IAAI,CAAC", "sourcesContent": ["import environment from './environment';\nimport FileManager from './file-manager';\nimport UrlFileManager from './url-file-manager';\nimport createFromEnvironment from '../less';\nimport lesscHelper from './lessc-helper';\nimport PluginLoader from './plugin-loader';\nimport fs from './fs';\nimport defaultOptions from '../less/default-options';\nimport imageSize from './image-size';\n\nconst less = createFromEnvironment(environment, [new FileManager(), new UrlFileManager()]);\n\n// allow people to create less with their own environment\nless.createFromEnvironment = createFromEnvironment;\nless.lesscHelper = lesscHelper;\nless.PluginLoader = PluginLoader;\nless.fs = fs;\nless.FileManager = FileManager;\nless.UrlFileManager = UrlFileManager;\n\n// Set up options\nless.options = defaultOptions();\n\n// provide image-size functionality\nimageSize(less.environment);\n\nexport default less;\n"]}