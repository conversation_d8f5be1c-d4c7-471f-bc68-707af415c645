{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../../src/less-node/fs.js"], "names": [], "mappings": ";;AAAA,IAAI,EAAE,CAAC;AACP,IACA;IACI,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;CAC/B;AACD,OAAO,CAAC,EACR;IACI,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACtB;AACD,kBAAe,EAAE,CAAC", "sourcesContent": ["let fs;\ntry\n{\n    fs = require('graceful-fs');\n}\ncatch (e)\n{\n    fs = require('fs');\n}\nexport default fs;\n"]}