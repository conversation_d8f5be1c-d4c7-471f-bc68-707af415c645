{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/less-browser/index.js"], "names": [], "mappings": ";;;;;AAAA,EAAE;AACF,WAAW;AACX,uEAAuE;AACvE,EAAE;AACF,iCAAoC;AACpC,iDAA+B;AAC/B,sDAAgC;AAChC,gEAAgC;AAChC,kEAA2C;AAC3C,gEAAyC;AACzC,sEAA+C;AAC/C,kDAA4B;AAC5B,4DAAqC;AAErC,mBAAe,UAAC,MAAM,EAAE,OAAO;IAC3B,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IACjC,IAAM,IAAI,GAAG,cAAQ,EAAE,CAAC;IAExB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IACrC,IAAM,WAAW,GAAG,sBAAE,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IACtC,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IACxC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAC/B,IAAI,CAAC,YAAY,GAAG,uBAAY,CAAC;IAEjC,sBAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3B,IAAM,MAAM,GAAG,yBAAc,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACrD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,eAAK,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAChF,oBAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAE5B,oCAAoC;IACpC,IAAI,OAAO,CAAC,SAAS,EAAE;QACnB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;KAClE;IAED,IAAM,WAAW,GAAG,mBAAmB,CAAC;IAExC,SAAS,KAAK,CAAC,GAAG;QACd,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,IAAM,IAAI,IAAI,GAAG,EAAE;YACpB,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;aAC5B;SACJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,iCAAiC;IACjC,SAAS,IAAI,CAAC,IAAI,EAAE,OAAO;QACvB,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAC3D,OAAO;YACH,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC;IACN,CAAC;IAED,SAAS,UAAU,CAAC,UAAU;QAC1B,IAAM,MAAM,GAAG,QAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,KAAK,CAAC;QAEV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;gBAC/B,IAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;gBACvC,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;gBACxC,IAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC;gBACvC,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAEtE,0BAA0B;gBAC1B,qCAAqC;gBACrC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,eAAe,EACjC,IAAI,CAAC,UAAC,KAAK,EAAE,CAAC,EAAE,MAAM;oBAClB,IAAI,CAAC,EAAE;wBACH,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;qBAC3B;yBAAM;wBACH,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC;wBACxB,IAAI,KAAK,CAAC,UAAU,EAAE;4BAClB,KAAK,CAAC,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC;yBACzC;6BAAM;4BACH,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC;yBAChC;qBACJ;gBACL,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;aACxB;SACJ;IACL,CAAC;IAED,SAAS,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU;QAElE,IAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;QACvC,mBAAW,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACpC,eAAe,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAElC,IAAI,UAAU,EAAE;YACZ,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;SAC3C;QAED,SAAS,uBAAuB,CAAC,UAAU;YACvC,IAAM,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC;YACjC,IAAM,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC;YACjC,IAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YAEnC,IAAM,WAAW,GAAG;gBAChB,gBAAgB,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC3C,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,eAAe,CAAC,WAAW;aAC3C,CAAC;YAEF,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,gBAAgB,CAAC;YACrD,WAAW,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,IAAI,WAAW,CAAC,gBAAgB,CAAC;YAEhF,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;gBAE9B,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,eAAe,CAAC,UAAU,CAAC,CAAC;gBACpE,IAAI,CAAC,MAAM,IAAI,GAAG,EAAE;oBAChB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;oBACrB,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBAChD,OAAO;iBACV;aAEJ;YAED,wDAAwD;YACxD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAEpB,eAAe,CAAC,YAAY,GAAG,WAAW,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,eAAe,EAAE,UAAC,CAAC,EAAE,MAAM;gBACzC,IAAI,CAAC,EAAE;oBACH,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;oBACd,QAAQ,CAAC,CAAC,CAAC,CAAC;iBACf;qBAAM;oBACH,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,YAAY,EAAE,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;oBACvF,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;iBAC1D;YACL,CAAC,CAAC,CAAC;QACP,CAAC;QAED,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,CAAC;aAC/D,IAAI,CAAC,UAAA,UAAU;YACZ,uBAAuB,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,GAAG;YACR,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjB,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IAEX,CAAC;IAED,SAAS,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU;QACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;SAC9F;IACL,CAAC;IAED,SAAS,eAAe;QACpB,IAAI,IAAI,CAAC,GAAG,KAAK,aAAa,EAAE;YAC5B,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC;gBAC1B,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,WAAW,CAAC,cAAc,EAAE,CAAC;oBAC7B,eAAe,CAAC,UAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO;wBACtC,IAAI,CAAC,EAAE;4BACH,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;yBACvC;6BAAM,IAAI,GAAG,EAAE;4BACZ,iBAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;yBAClD;oBACL,CAAC,CAAC,CAAC;iBACN;YACL,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;IACL,CAAC;IAED,EAAE;IACF,aAAa;IACb,EAAE;IACF,IAAI,CAAC,KAAK,GAAK;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAG;YAClB,IAAI,CAAC,GAAG,GAAG,aAAa,CAAC;YACzB,eAAe,EAAE,CAAC;SACrB;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,IAAI,CAAC,OAAO,GAAG,cAAa,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAEpG,EAAE;IACF,oEAAoE;IACpE,qBAAqB;IACrB,EAAE;IACF,IAAI,CAAC,8BAA8B,GAAG;QAClC,IAAM,KAAK,GAAG,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,iBAAiB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC;gBACvE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;gBACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9B;SACJ;IACL,CAAC,CAAC;IAEF,EAAE;IACF,qEAAqE;IACrE,0CAA0C;IAC1C,EAAE;IACF,IAAI,CAAC,mBAAmB,GAAG,cAAM,OAAA,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QACzD,IAAI,CAAC,8BAA8B,EAAE,CAAC;QACtC,OAAO,EAAE,CAAC;IACd,CAAC,CAAC,EAH+B,CAG/B,CAAC;IAEH,EAAE;IACF,qEAAqE;IACrE,mCAAmC;IACnC,EAAE;IACF,IAAI,CAAC,UAAU,GAAG,UAAA,MAAM,IAAI,OAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,EAAjC,CAAiC,CAAC;IAE9D,IAAI,CAAC,OAAO,GAAG,UAAC,MAAM,EAAE,UAAU,EAAE,cAAc;QAC9C,IAAI,CAAC,MAAM,IAAI,cAAc,CAAC,IAAI,cAAc,KAAK,KAAK,EAAE;YACxD,WAAW,CAAC,cAAc,EAAE,CAAC;SAChC;QACD,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,IAAI,SAAS,CAAC;YACd,IAAI,OAAO,CAAC;YACZ,IAAI,iBAAiB,CAAC;YACtB,IAAI,eAAe,CAAC;YACpB,SAAS,GAAG,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAEjC,+CAA+C;YAC/C,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAErC,IAAI,eAAe,KAAK,CAAC,EAAE;gBAEvB,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBACrB,iBAAiB,GAAG,OAAO,GAAG,SAAS,CAAC;gBACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;gBACjE,OAAO,CAAC;oBACJ,SAAS,WAAA;oBACT,OAAO,SAAA;oBACP,iBAAiB,mBAAA;oBACjB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;iBAC7B,CAAC,CAAC;aAEN;iBAAM;gBACH,2GAA2G;gBAC3G,eAAe,CAAC,UAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO;oBACtC,IAAI,CAAC,EAAE;wBACH,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;wBACpC,MAAM,CAAC,CAAC,CAAC,CAAC;wBACV,OAAO;qBACV;oBACD,IAAI,OAAO,CAAC,KAAK,EAAE;wBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAW,KAAK,CAAC,IAAI,iBAAc,CAAC,CAAC;qBACzD;yBAAM;wBACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAY,KAAK,CAAC,IAAI,mBAAgB,CAAC,CAAC;qBAC5D;oBACD,iBAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAW,KAAK,CAAC,IAAI,uBAAiB,IAAI,IAAI,EAAE,GAAG,OAAO,QAAI,CAAC,CAAC;oBAEjF,wBAAwB;oBACxB,eAAe,EAAE,CAAC;oBAElB,4EAA4E;oBAC5E,IAAI,eAAe,KAAK,CAAC,EAAE;wBACvB,iBAAiB,GAAG,IAAI,IAAI,EAAE,GAAG,SAAS,CAAC;wBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAuC,iBAAiB,OAAI,CAAC,CAAC;wBAC/E,OAAO,CAAC;4BACJ,SAAS,WAAA;4BACT,OAAO,SAAA;4BACP,iBAAiB,mBAAA;4BACjB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;yBAC7B,CAAC,CAAC;qBACN;oBACD,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBACzB,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aAC1B;YAED,UAAU,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;IAChC,OAAO,IAAI,CAAC;AAChB,CAAC,EAAC", "sourcesContent": ["//\n// index.js\n// Should expose the additional browser functions on to the less object\n//\nimport {addDataAttr} from './utils';\nimport lessRoot from '../less';\nimport browser from './browser';\nimport FM from './file-manager';\nimport PluginLoader from './plugin-loader';\nimport LogListener from './log-listener';\nimport ErrorReporting from './error-reporting';\nimport Cache from './cache';\nimport ImageSize from './image-size';\n\nexport default (window, options) => {\n    const document = window.document;\n    const less = lessRoot();\n\n    less.options = options;\n    const environment = less.environment;\n    const FileManager = FM(options, less.logger);\n    const fileManager = new FileManager();\n    environment.addFileManager(fileManager);\n    less.FileManager = FileManager;\n    less.PluginLoader = PluginLoader;\n\n    LogListener(less, options);\n    const errors = ErrorReporting(window, less, options);\n    const cache = less.cache = options.cache || Cache(window, options, less.logger);\n    ImageSize(less.environment);\n\n    // Setup user functions - Deprecate?\n    if (options.functions) {\n        less.functions.functionRegistry.addMultiple(options.functions);\n    }\n\n    const typePattern = /^text\\/(x-)?less$/;\n\n    function clone(obj) {\n        const cloned = {};\n        for (const prop in obj) {\n            if (obj.hasOwnProperty(prop)) {\n                cloned[prop] = obj[prop];\n            }\n        }\n        return cloned;\n    }\n\n    // only really needed for phantom\n    function bind(func, thisArg) {\n        const curryArgs = Array.prototype.slice.call(arguments, 2);\n        return function() {\n            const args = curryArgs.concat(Array.prototype.slice.call(arguments, 0));\n            return func.apply(thisArg, args);\n        };\n    }\n\n    function loadStyles(modifyVars) {\n        const styles = document.getElementsByTagName('style');\n        let style;\n\n        for (let i = 0; i < styles.length; i++) {\n            style = styles[i];\n            if (style.type.match(typePattern)) {\n                const instanceOptions = clone(options);\n                instanceOptions.modifyVars = modifyVars;\n                const lessText = style.innerHTML || '';\n                instanceOptions.filename = document.location.href.replace(/#.*$/, '');\n\n                /* jshint loopfunc:true */\n                // use closure to store current style\n                less.render(lessText, instanceOptions,\n                    bind((style, e, result) => {\n                        if (e) {\n                            errors.add(e, 'inline');\n                        } else {\n                            style.type = 'text/css';\n                            if (style.styleSheet) {\n                                style.styleSheet.cssText = result.css;\n                            } else {\n                                style.innerHTML = result.css;\n                            }\n                        }\n                    }, null, style));\n            }\n        }\n    }\n\n    function loadStyleSheet(sheet, callback, reload, remaining, modifyVars) {\n\n        const instanceOptions = clone(options);\n        addDataAttr(instanceOptions, sheet);\n        instanceOptions.mime = sheet.type;\n\n        if (modifyVars) {\n            instanceOptions.modifyVars = modifyVars;\n        }\n\n        function loadInitialFileCallback(loadedFile) {\n            const data = loadedFile.contents;\n            const path = loadedFile.filename;\n            const webInfo = loadedFile.webInfo;\n\n            const newFileInfo = {\n                currentDirectory: fileManager.getPath(path),\n                filename: path,\n                rootFilename: path,\n                rewriteUrls: instanceOptions.rewriteUrls\n            };\n\n            newFileInfo.entryPath = newFileInfo.currentDirectory;\n            newFileInfo.rootpath = instanceOptions.rootpath || newFileInfo.currentDirectory;\n\n            if (webInfo) {\n                webInfo.remaining = remaining;\n\n                const css = cache.getCSS(path, webInfo, instanceOptions.modifyVars);\n                if (!reload && css) {\n                    webInfo.local = true;\n                    callback(null, css, data, sheet, webInfo, path);\n                    return;\n                }\n\n            }\n\n            // TODO add tests around how this behaves when reloading\n            errors.remove(path);\n\n            instanceOptions.rootFileInfo = newFileInfo;\n            less.render(data, instanceOptions, (e, result) => {\n                if (e) {\n                    e.href = path;\n                    callback(e);\n                } else {\n                    cache.setCSS(sheet.href, webInfo.lastModified, instanceOptions.modifyVars, result.css);\n                    callback(null, result.css, data, sheet, webInfo, path);\n                }\n            });\n        }\n\n        fileManager.loadFile(sheet.href, null, instanceOptions, environment)\n            .then(loadedFile => {\n                loadInitialFileCallback(loadedFile);\n            }).catch(err => {\n                console.log(err);\n                callback(err);\n            });\n\n    }\n\n    function loadStyleSheets(callback, reload, modifyVars) {\n        for (let i = 0; i < less.sheets.length; i++) {\n            loadStyleSheet(less.sheets[i], callback, reload, less.sheets.length - (i + 1), modifyVars);\n        }\n    }\n\n    function initRunningMode() {\n        if (less.env === 'development') {\n            less.watchTimer = setInterval(() => {\n                if (less.watchMode) {\n                    fileManager.clearFileCache();\n                    loadStyleSheets((e, css, _, sheet, webInfo) => {\n                        if (e) {\n                            errors.add(e, e.href || sheet.href);\n                        } else if (css) {\n                            browser.createCSS(window.document, css, sheet);\n                        }\n                    });\n                }\n            }, options.poll);\n        }\n    }\n\n    //\n    // Watch mode\n    //\n    less.watch   = function () {\n        if (!less.watchMode ) {\n            less.env = 'development';\n            initRunningMode();\n        }\n        this.watchMode = true;\n        return true;\n    };\n\n    less.unwatch = function () {clearInterval(less.watchTimer); this.watchMode = false; return false; };\n\n    //\n    // Synchronously get all <link> tags with the 'rel' attribute set to\n    // \"stylesheet/less\".\n    //\n    less.registerStylesheetsImmediately = () => {\n        const links = document.getElementsByTagName('link');\n        less.sheets = [];\n\n        for (let i = 0; i < links.length; i++) {\n            if (links[i].rel === 'stylesheet/less' || (links[i].rel.match(/stylesheet/) &&\n                (links[i].type.match(typePattern)))) {\n                less.sheets.push(links[i]);\n            }\n        }\n    };\n\n    //\n    // Asynchronously get all <link> tags with the 'rel' attribute set to\n    // \"stylesheet/less\", returning a Promise.\n    //\n    less.registerStylesheets = () => new Promise((resolve, reject) => {\n        less.registerStylesheetsImmediately();\n        resolve();\n    });\n\n    //\n    // With this function, it's possible to alter variables and re-render\n    // CSS without reloading less-files\n    //\n    less.modifyVars = record => less.refresh(true, record, false);\n\n    less.refresh = (reload, modifyVars, clearFileCache) => {\n        if ((reload || clearFileCache) && clearFileCache !== false) {\n            fileManager.clearFileCache();\n        }\n        return new Promise((resolve, reject) => {\n            let startTime;\n            let endTime;\n            let totalMilliseconds;\n            let remainingSheets;\n            startTime = endTime = new Date();\n\n            // Set counter for remaining unprocessed sheets\n            remainingSheets = less.sheets.length;\n\n            if (remainingSheets === 0) {\n\n                endTime = new Date();\n                totalMilliseconds = endTime - startTime;\n                less.logger.info('Less has finished and no sheets were loaded.');\n                resolve({\n                    startTime,\n                    endTime,\n                    totalMilliseconds,\n                    sheets: less.sheets.length\n                });\n\n            } else {\n                // Relies on less.sheets array, callback seems to be guaranteed to be called for every element of the array\n                loadStyleSheets((e, css, _, sheet, webInfo) => {\n                    if (e) {\n                        errors.add(e, e.href || sheet.href);\n                        reject(e);\n                        return;\n                    }\n                    if (webInfo.local) {\n                        less.logger.info(`Loading ${sheet.href} from cache.`);\n                    } else {\n                        less.logger.info(`Rendered ${sheet.href} successfully.`);\n                    }\n                    browser.createCSS(window.document, css, sheet);\n                    less.logger.info(`CSS for ${sheet.href} generated in ${new Date() - endTime}ms`);\n\n                    // Count completed sheet\n                    remainingSheets--;\n\n                    // Check if the last remaining sheet was processed and then call the promise\n                    if (remainingSheets === 0) {\n                        totalMilliseconds = new Date() - startTime;\n                        less.logger.info(`Less has finished. CSS generated in ${totalMilliseconds}ms`);\n                        resolve({\n                            startTime,\n                            endTime,\n                            totalMilliseconds,\n                            sheets: less.sheets.length\n                        });\n                    }\n                    endTime = new Date();\n                }, reload, modifyVars);\n            }\n\n            loadStyles(modifyVars);\n        });\n    };\n\n    less.refreshStyles = loadStyles;\n    return less;\n};\n"]}