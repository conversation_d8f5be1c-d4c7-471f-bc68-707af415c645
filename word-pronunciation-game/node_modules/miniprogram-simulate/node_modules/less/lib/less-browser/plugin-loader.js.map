{"version": 3, "file": "plugin-loader.js", "sourceRoot": "", "sources": ["../../src/less-browser/plugin-loader.js"], "names": [], "mappings": ";AAAA,sCAAsC;AACtC,mBAAmB;;;;;;;;;;;;;;;;;;AAEnB,4GAAiF;AAEjF;;GAEG;AACH;IAA2B,gCAAoB;IAC3C,sBAAY,IAAI;QAAhB,YACI,iBAAO,SAIV;QAFG,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;;QACjB,yDAAyD;IAC7D,CAAC;IAED,iCAAU,GAAV,UAAW,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW;QAC5D,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC;iBACzD,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IACL,mBAAC;AAAD,CAAC,AAdD,CAA2B,mCAAoB,GAc9C;AAED,kBAAe,YAAY,CAAC", "sourcesContent": ["// TODO: Add tests for browser @plugin\n/* global window */\n\nimport AbstractPluginLoader from '../less/environment/abstract-plugin-loader.js';\n\n/**\n * Browser Plugin Loader\n */\nclass PluginLoader extends AbstractPluginLoader {\n    constructor(less) {\n        super();\n\n        this.less = less;\n        // Should we shim this.require for browser? Probably not?\n    }\n\n    loadPlugin(filename, basePath, context, environment, fileManager) {\n        return new Promise((fulfill, reject) => {\n            fileManager.loadFile(filename, basePath, context, environment)\n                .then(fulfill).catch(reject);\n        });\n    }\n}\n\nexport default PluginLoader;\n\n"]}