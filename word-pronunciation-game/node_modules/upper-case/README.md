# Upper Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transforms the string to upper case.

## Installation

```
npm install upper-case --save
```

## Usage

```js
import { upperCase, localeUpperCase } from "upper-case";

upperCase("string"); //=> "STRING"

localeUpperCase("string", "tr"); //=> "STRİNG"
```

## License

MIT

[npm-image]: https://img.shields.io/npm/v/upper-case.svg?style=flat
[npm-url]: https://npmjs.org/package/upper-case
[downloads-image]: https://img.shields.io/npm/dm/upper-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/upper-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/upper-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=upper-case
