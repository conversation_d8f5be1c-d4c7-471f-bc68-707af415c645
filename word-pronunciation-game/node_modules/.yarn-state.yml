# Warning: This file is automatically generated. Removing it is fine, but will
# cause your node_modules installation to become invalidated.

__metadata:
  version: 1
  nmMode: classic

"@ampproject/remapping@npm:2.3.0":
  locations:
    - "node_modules/@ampproject/remapping"

"@asamuzakjp/css-color@npm:3.2.0":
  locations:
    - "node_modules/@asamuzakjp/css-color"

"@babel/code-frame@npm:7.27.1":
  locations:
    - "node_modules/@babel/code-frame"

"@babel/compat-data@npm:7.28.0":
  locations:
    - "node_modules/@babel/compat-data"

"@babel/core@npm:7.28.0":
  locations:
    - "node_modules/@babel/core"

"@babel/eslint-parser@virtual:f17df7b218a3670ebca7472c220766bd25fcf3ce525b4f4e3f6d044d127e793c5f67ca710071f2156eac6e1ea2d8ec2cac0a3e68c256ebd1921c52945227ae76#npm:7.28.0":
  locations:
    - "node_modules/@babel/eslint-parser"

"@babel/generator@npm:7.24.4":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/generator"

"@babel/generator@npm:7.28.0":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/traverse/node_modules/@babel/generator"
    - "node_modules/@babel/generator"

"@babel/helper-annotate-as-pure@npm:7.27.3":
  locations:
    - "node_modules/@babel/helper-annotate-as-pure"

"@babel/helper-compilation-targets@npm:7.27.2":
  locations:
    - "node_modules/@babel/helper-compilation-targets"

"@babel/helper-create-class-features-plugin@virtual:241dc776bdf691c21cc9f86cff191070664698362f952d99c299fad4778cc08813b64ab83a039e4432929d3f9890514d206b32498d73bfd38264dab0695ebd71#npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-create-class-features-plugin"

"@babel/helper-create-regexp-features-plugin@virtual:6d71fbf8ed554d5afd968aef29b1d650bad8ed28e66fb732086a6cfd7a4a8e463cfe735d233fa8987dcb645f38c9f7c4d009ee173f620304f615f3a5fb523478#npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-create-regexp-features-plugin"

"@babel/helper-define-polyfill-provider@virtual:b92e7e6ec4ef3c80527ed22e22c103eb404be73932d2131e7a8fd340122b97b2432570499e512c63ff4641c2b9f5e01d9318531f4a97d6631f34d40b103e36ca#npm:0.6.5":
  locations:
    - "node_modules/@babel/helper-define-polyfill-provider"

"@babel/helper-environment-visitor@npm:7.24.7":
  locations:
    - "node_modules/@babel/helper-environment-visitor"

"@babel/helper-function-name@npm:7.24.7":
  locations:
    - "node_modules/@babel/helper-function-name"

"@babel/helper-globals@npm:7.28.0":
  locations:
    - "node_modules/@babel/helper-globals"

"@babel/helper-hoist-variables@npm:7.24.7":
  locations:
    - "node_modules/@babel/helper-hoist-variables"

"@babel/helper-member-expression-to-functions@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-member-expression-to-functions"

"@babel/helper-module-imports@npm:7.18.6":
  locations:
    - "node_modules/babel-plugin-transform-solid-jsx/node_modules/@babel/helper-module-imports"

"@babel/helper-module-imports@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-module-imports"

"@babel/helper-module-transforms@virtual:2c032490421458ee4e212ed9bd0627762ff65ed1232d4208f2d615b0d0187bb07fc168cbfc1670b2da389400360e723c4eeeceee24d006e509ab345b44149a9f#npm:7.27.3":
  locations:
    - "node_modules/@babel/helper-module-transforms"

"@babel/helper-optimise-call-expression@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-optimise-call-expression"

"@babel/helper-plugin-utils@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-plugin-utils"

"@babel/helper-remap-async-to-generator@virtual:2f274ab3722c4cae229fc6a9fe17b63f1155f9a1d81cab0f78f64ead16f5c540f0ffc2f512aea3b0cb159dc22fed2d9d3c3dcbbe3b66b565fa3258a89ad1e199#npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-remap-async-to-generator"

"@babel/helper-replace-supers@virtual:a8f1183b9556ca1b33d42d43b8a0fb2fef017cdd80773e1d4038a819e0718c2863558dd828c021e2cb65a11c2bcc393a8960c874086140634c3b80bb567380f5#npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-replace-supers"

"@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-skip-transparent-expression-wrappers"

"@babel/helper-split-export-declaration@npm:7.24.7":
  locations:
    - "node_modules/@babel/helper-split-export-declaration"

"@babel/helper-string-parser@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-string-parser"

"@babel/helper-validator-identifier@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-identifier"

"@babel/helper-validator-option@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-option"

"@babel/helper-wrap-function@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-wrap-function"

"@babel/helpers@npm:7.27.6":
  locations:
    - "node_modules/@babel/helpers"

"@babel/parser@npm:7.24.4":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/parser"

"@babel/parser@npm:7.28.0":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/traverse/node_modules/@babel/parser"
    - "node_modules/@babel/parser"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-bugfix-firefox-class-in-computed-class-key"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-bugfix-safari-class-field-initializer-scope"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly"

"@babel/plugin-proposal-decorators@virtual:56b47e8002a4ebbd445800a5cd3a3c4782238514eac00c9754693b11b67e5d5217641d3c29a87aa594161f0994b6c73908c3379fa4ee6277e4428bbf19cc4748#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-proposal-decorators"

"@babel/plugin-proposal-private-property-in-object@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.21.0-placeholder-for-preset-env.2":
  locations:
    - "node_modules/@babel/plugin-proposal-private-property-in-object"

"@babel/plugin-syntax-decorators@virtual:7e66143be38194a518175c32606b78eba06a2a058cec9c06c13187ddce712e51903cdaf383b2b68e04bc1bcdf144aae60dc89b0dc7521d80ac871114f219af41#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-decorators"

"@babel/plugin-syntax-import-assertions@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-import-assertions"

"@babel/plugin-syntax-import-attributes@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-import-attributes"

"@babel/plugin-syntax-jsx@virtual:5eb1f8da17543d1932f5bd00007bfa33ec3bb60fa25bea46e6e0e0e298180546e60a8ab4d64b63b9a63cbf5c1a18614cbcf778ae9a0706afa3de32ca66c9fadf#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-jsx"
  aliases:
    - "virtual:cb73f4a00ec70d2b2ebb1553fb57bb9682fd550d497bad27e4343f71f87986d83bd4d2a3a82134d71892d96f52c521b2aa7c6c0aa7c07fc1893f6b2fe929edf3#npm:7.27.1"

"@babel/plugin-syntax-typescript@virtual:8ebb991832d2e3a4023f908afcb2da7c82a82f0faad68c2d5074cebb0cc242acab97e8df8f65973cf2d27cca43dbc427d70a52afe7db8885bb81b6e3f9d7e040#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-typescript"

"@babel/plugin-syntax-unicode-sets-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.18.6":
  locations:
    - "node_modules/@babel/plugin-syntax-unicode-sets-regex"

"@babel/plugin-transform-arrow-functions@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-arrow-functions"

"@babel/plugin-transform-async-generator-functions@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-async-generator-functions"

"@babel/plugin-transform-async-to-generator@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-async-to-generator"

"@babel/plugin-transform-block-scoped-functions@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-block-scoped-functions"

"@babel/plugin-transform-block-scoping@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-block-scoping"

"@babel/plugin-transform-class-properties@virtual:56b47e8002a4ebbd445800a5cd3a3c4782238514eac00c9754693b11b67e5d5217641d3c29a87aa594161f0994b6c73908c3379fa4ee6277e4428bbf19cc4748#npm:7.27.1":
  locations:
    - "node_modules/babel-preset-taro/node_modules/@babel/plugin-transform-class-properties"
    - "node_modules/@babel/preset-env/node_modules/@babel/plugin-transform-class-properties"

"@babel/plugin-transform-class-properties@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-class-properties"

"@babel/plugin-transform-class-static-block@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-class-static-block"

"@babel/plugin-transform-classes@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-classes"

"@babel/plugin-transform-computed-properties@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-computed-properties"

"@babel/plugin-transform-destructuring@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-destructuring"

"@babel/plugin-transform-dotall-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-dotall-regex"

"@babel/plugin-transform-duplicate-keys@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-duplicate-keys"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-duplicate-named-capturing-groups-regex"

"@babel/plugin-transform-dynamic-import@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-dynamic-import"

"@babel/plugin-transform-explicit-resource-management@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-explicit-resource-management"

"@babel/plugin-transform-exponentiation-operator@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-exponentiation-operator"

"@babel/plugin-transform-export-namespace-from@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-export-namespace-from"

"@babel/plugin-transform-for-of@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-for-of"

"@babel/plugin-transform-function-name@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-function-name"

"@babel/plugin-transform-json-strings@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-json-strings"

"@babel/plugin-transform-literals@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-literals"

"@babel/plugin-transform-logical-assignment-operators@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-logical-assignment-operators"

"@babel/plugin-transform-member-expression-literals@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-member-expression-literals"

"@babel/plugin-transform-modules-amd@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-modules-amd"

"@babel/plugin-transform-modules-commonjs@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-modules-commonjs"

"@babel/plugin-transform-modules-systemjs@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-modules-systemjs"

"@babel/plugin-transform-modules-umd@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-modules-umd"

"@babel/plugin-transform-named-capturing-groups-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-named-capturing-groups-regex"

"@babel/plugin-transform-new-target@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-new-target"

"@babel/plugin-transform-nullish-coalescing-operator@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-nullish-coalescing-operator"

"@babel/plugin-transform-numeric-separator@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-numeric-separator"

"@babel/plugin-transform-object-rest-spread@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-object-rest-spread"

"@babel/plugin-transform-object-super@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-object-super"

"@babel/plugin-transform-optional-catch-binding@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-optional-catch-binding"

"@babel/plugin-transform-optional-chaining@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-optional-chaining"

"@babel/plugin-transform-parameters@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.7":
  locations:
    - "node_modules/@babel/plugin-transform-parameters"

"@babel/plugin-transform-private-methods@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-private-methods"

"@babel/plugin-transform-private-property-in-object@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-private-property-in-object"

"@babel/plugin-transform-property-literals@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-property-literals"

"@babel/plugin-transform-react-display-name@virtual:391b9f08b8d0fecd1900441897c86a92ba52904659dff39cccba8d947f9d68454866b6355dded198e70e550c9e8b5e032599cd5a700dca16ff4496ec7440bf9c#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-react-display-name"

"@babel/plugin-transform-react-jsx-development@virtual:391b9f08b8d0fecd1900441897c86a92ba52904659dff39cccba8d947f9d68454866b6355dded198e70e550c9e8b5e032599cd5a700dca16ff4496ec7440bf9c#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-development"

"@babel/plugin-transform-react-jsx@virtual:391b9f08b8d0fecd1900441897c86a92ba52904659dff39cccba8d947f9d68454866b6355dded198e70e550c9e8b5e032599cd5a700dca16ff4496ec7440bf9c#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx"

"@babel/plugin-transform-react-pure-annotations@virtual:391b9f08b8d0fecd1900441897c86a92ba52904659dff39cccba8d947f9d68454866b6355dded198e70e550c9e8b5e032599cd5a700dca16ff4496ec7440bf9c#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-pure-annotations"

"@babel/plugin-transform-regenerator@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.28.1":
  locations:
    - "node_modules/@babel/plugin-transform-regenerator"

"@babel/plugin-transform-regexp-modifiers@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-regexp-modifiers"

"@babel/plugin-transform-reserved-words@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-reserved-words"

"@babel/plugin-transform-runtime@virtual:56b47e8002a4ebbd445800a5cd3a3c4782238514eac00c9754693b11b67e5d5217641d3c29a87aa594161f0994b6c73908c3379fa4ee6277e4428bbf19cc4748#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-runtime"

"@babel/plugin-transform-shorthand-properties@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-shorthand-properties"

"@babel/plugin-transform-spread@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-spread"

"@babel/plugin-transform-sticky-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-sticky-regex"

"@babel/plugin-transform-template-literals@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-template-literals"

"@babel/plugin-transform-typeof-symbol@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-typeof-symbol"

"@babel/plugin-transform-typescript@virtual:722d473ebc4c827f4cb4367c4165dad6894af9536a6b723150b2381c8f4561523f930226879366eaf2c1e429441f028cb10f639824b2287ed1c1642c3d992d41#npm:7.28.0":
  locations:
    - "node_modules/@babel/plugin-transform-typescript"

"@babel/plugin-transform-unicode-escapes@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-unicode-escapes"

"@babel/plugin-transform-unicode-property-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-unicode-property-regex"

"@babel/plugin-transform-unicode-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-unicode-regex"

"@babel/plugin-transform-unicode-sets-regex@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-unicode-sets-regex"

"@babel/preset-env@virtual:56b47e8002a4ebbd445800a5cd3a3c4782238514eac00c9754693b11b67e5d5217641d3c29a87aa594161f0994b6c73908c3379fa4ee6277e4428bbf19cc4748#npm:7.28.0":
  locations:
    - "node_modules/@babel/preset-env"

"@babel/preset-modules@virtual:ab84b8b8d1bc026eb3b53a2fd8ab4a202d173bcbc03079949706d04e139abbeec52fd9bf858091934921e1d1dbf89d205cac5ad42b57ed2b0222a621285be07f#npm:0.1.6-no-external-plugins":
  locations:
    - "node_modules/@babel/preset-modules"

"@babel/preset-react@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:7.27.1":
  locations:
    - "node_modules/@babel/preset-react"

"@babel/preset-typescript@virtual:56b47e8002a4ebbd445800a5cd3a3c4782238514eac00c9754693b11b67e5d5217641d3c29a87aa594161f0994b6c73908c3379fa4ee6277e4428bbf19cc4748#npm:7.27.1":
  locations:
    - "node_modules/@babel/preset-typescript"

"@babel/runtime-corejs3@npm:7.28.0":
  locations:
    - "node_modules/@babel/runtime-corejs3"

"@babel/runtime@npm:7.27.6":
  locations:
    - "node_modules/@babel/runtime"

"@babel/template@npm:7.27.2":
  locations:
    - "node_modules/@babel/template"

"@babel/traverse@npm:7.24.1":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/traverse"

"@babel/traverse@npm:7.28.0":
  locations:
    - "node_modules/@babel/traverse"

"@babel/types@npm:7.24.0":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/types"

"@babel/types@npm:7.28.1":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/traverse/node_modules/@babel/types"
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/parser/node_modules/@babel/types"
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/generator/node_modules/@babel/types"
    - "node_modules/@babel/types"

"@commitlint/cli@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/cli"

"@commitlint/config-conventional@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/config-conventional"

"@commitlint/config-validator@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/config-validator"

"@commitlint/ensure@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/ensure"

"@commitlint/execute-rule@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/execute-rule"

"@commitlint/format@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/format"

"@commitlint/is-ignored@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/is-ignored"

"@commitlint/lint@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/lint"

"@commitlint/load@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/load"

"@commitlint/message@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/message"

"@commitlint/parse@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/parse"

"@commitlint/read@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/read"

"@commitlint/resolve-extends@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/resolve-extends"

"@commitlint/rules@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/rules"

"@commitlint/to-lines@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/to-lines"

"@commitlint/top-level@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/top-level"

"@commitlint/types@npm:19.8.1":
  locations:
    - "node_modules/@commitlint/types"

"@csstools/color-helpers@npm:5.0.2":
  locations:
    - "node_modules/@csstools/color-helpers"

"@csstools/css-calc@virtual:ed5b7465ba8cf0eb21975dec62bfcf6d291ea8fcead25822592225aae1675a11a9ab5730181ed5da294a62f7379a3e67d78ef9ef50d04ba4802b6770c14cecdd#npm:2.1.4":
  locations:
    - "node_modules/@csstools/css-calc"

"@csstools/css-color-parser@virtual:ed5b7465ba8cf0eb21975dec62bfcf6d291ea8fcead25822592225aae1675a11a9ab5730181ed5da294a62f7379a3e67d78ef9ef50d04ba4802b6770c14cecdd#npm:3.0.10":
  locations:
    - "node_modules/@csstools/css-color-parser"

"@csstools/css-parser-algorithms@virtual:e1dfb8a09cde37d074aa7a8d2f6ecd2796b7856d149dea1cd15a4a241c4e9eca60fba7381a4b09c69f3c4d10d83e54139dd2241e17a7687512511520ed208d2e#npm:3.0.5":
  locations:
    - "node_modules/@csstools/css-parser-algorithms"

"@csstools/css-tokenizer@npm:3.0.4":
  locations:
    - "node_modules/@csstools/css-tokenizer"

"@csstools/media-query-list-parser@virtual:e1dfb8a09cde37d074aa7a8d2f6ecd2796b7856d149dea1cd15a4a241c4e9eca60fba7381a4b09c69f3c4d10d83e54139dd2241e17a7687512511520ed208d2e#npm:4.0.3":
  locations:
    - "node_modules/@csstools/media-query-list-parser"

"@csstools/selector-specificity@virtual:e1dfb8a09cde37d074aa7a8d2f6ecd2796b7856d149dea1cd15a4a241c4e9eca60fba7381a4b09c69f3c4d10d83e54139dd2241e17a7687512511520ed208d2e#npm:5.0.0":
  locations:
    - "node_modules/stylelint/node_modules/@csstools/selector-specificity"

"@dual-bundle/import-meta-resolve@npm:4.1.0":
  locations:
    - "node_modules/@dual-bundle/import-meta-resolve"

"@esbuild/darwin-arm64@npm:0.21.5":
  locations:
    - "node_modules/@esbuild/darwin-arm64"

"@esbuild/darwin-arm64@npm:0.25.8":
  locations:
    - "node_modules/esbuild-loader/node_modules/@esbuild/darwin-arm64"

"@eslint-community/eslint-utils@virtual:0e83e90d273f68a1bd1543dde3480eaf525801444ba81cbede9215242f0a1bd9563c2ba1980136297cb6880745c63575691537145c6b548460570102de527f4e#npm:4.7.0":
  locations:
    - "node_modules/@tarojs/plugin-doctor/node_modules/@eslint-community/eslint-utils"

"@eslint-community/eslint-utils@virtual:dd20287a5a1e86b12a5b04609f98bd729fafd847d08e1fc89cdc68f92d1acf209e53b09ef0af4b6e7781d88e1f9acf94e3bf34619939e434ad5ffb0f24855eb4#npm:4.7.0":
  locations:
    - "node_modules/@eslint-community/eslint-utils"

"@eslint-community/regexpp@npm:4.12.1":
  locations:
    - "node_modules/@eslint-community/regexpp"

"@eslint/eslintrc@npm:2.1.4":
  locations:
    - "node_modules/@eslint/eslintrc"

"@eslint/js@npm:8.41.0":
  locations:
    - "node_modules/@eslint/js"

"@eslint/js@npm:8.57.1":
  locations:
    - "node_modules/eslint/node_modules/@eslint/js"

"@hapi/hoek@npm:9.3.0":
  locations:
    - "node_modules/@hapi/hoek"

"@hapi/topo@npm:5.1.0":
  locations:
    - "node_modules/@hapi/topo"

"@humanwhocodes/config-array@npm:0.11.14":
  locations:
    - "node_modules/@humanwhocodes/config-array"

"@humanwhocodes/config-array@npm:0.13.0":
  locations:
    - "node_modules/eslint/node_modules/@humanwhocodes/config-array"

"@humanwhocodes/module-importer@npm:1.0.1":
  locations:
    - "node_modules/@humanwhocodes/module-importer"

"@humanwhocodes/object-schema@npm:2.0.3":
  locations:
    - "node_modules/@humanwhocodes/object-schema"

"@isaacs/balanced-match@npm:4.0.1":
  locations:
    - "node_modules/@isaacs/balanced-match"

"@isaacs/brace-expansion@npm:5.0.0":
  locations:
    - "node_modules/@isaacs/brace-expansion"

"@isaacs/cliui@npm:8.0.2":
  locations:
    - "node_modules/@isaacs/cliui"

"@isaacs/fs-minipass@npm:4.0.1":
  locations:
    - "node_modules/@isaacs/fs-minipass"

"@jest/schemas@npm:29.6.3":
  locations:
    - "node_modules/@jest/schemas"

"@jest/types@npm:26.6.2":
  locations:
    - "node_modules/pretty-format/node_modules/@jest/types"

"@jest/types@npm:29.6.3":
  locations:
    - "node_modules/@jest/types"

"@jridgewell/gen-mapping@npm:0.3.12":
  locations:
    - "node_modules/@jridgewell/gen-mapping"

"@jridgewell/resolve-uri@npm:3.1.2":
  locations:
    - "node_modules/@jridgewell/resolve-uri"

"@jridgewell/source-map@npm:0.3.10":
  locations:
    - "node_modules/@jridgewell/source-map"

"@jridgewell/sourcemap-codec@npm:1.5.4":
  locations:
    - "node_modules/@jridgewell/sourcemap-codec"

"@jridgewell/trace-mapping@npm:0.3.29":
  locations:
    - "node_modules/@jridgewell/trace-mapping"

"@keyv/serialize@npm:1.1.0":
  locations:
    - "node_modules/@keyv/serialize"

"@leichtgewicht/ip-codec@npm:2.0.5":
  locations:
    - "node_modules/@leichtgewicht/ip-codec"

"@napi-rs/triples@npm:1.2.0":
  locations:
    - "node_modules/@napi-rs/triples"

"@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1":
  locations:
    - "node_modules/@nicolo-ribaudo/eslint-scope-5-internals"

"@nodelib/fs.scandir@npm:2.1.5":
  locations:
    - "node_modules/@nodelib/fs.scandir"

"@nodelib/fs.stat@npm:2.0.5":
  locations:
    - "node_modules/@nodelib/fs.stat"

"@nodelib/fs.walk@npm:1.2.8":
  locations:
    - "node_modules/@nodelib/fs.walk"

"@npmcli/agent@npm:3.0.0":
  locations:
    - "node_modules/@npmcli/agent"

"@npmcli/fs@npm:4.0.0":
  locations:
    - "node_modules/@npmcli/fs"

"@parcel/watcher-darwin-arm64@npm:2.5.1":
  locations:
    - "node_modules/@parcel/watcher-darwin-arm64"

"@parcel/watcher@npm:2.5.1":
  locations:
    - "node_modules/@parcel/watcher"

"@pkgjs/parseargs@npm:0.11.0":
  locations:
    - "node_modules/@pkgjs/parseargs"

"@pmmmwh/react-refresh-webpack-plugin@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:0.5.17":
  locations:
    - "node_modules/@pmmmwh/react-refresh-webpack-plugin"

"@rnx-kit/babel-preset-metro-react-native@virtual:56b47e8002a4ebbd445800a5cd3a3c4782238514eac00c9754693b11b67e5d5217641d3c29a87aa594161f0994b6c73908c3379fa4ee6277e4428bbf19cc4748#npm:1.1.8":
  locations:
    - "node_modules/@rnx-kit/babel-preset-metro-react-native"

"@rnx-kit/console@npm:1.1.0":
  locations:
    - "node_modules/@rnx-kit/console"

"@rtsao/scc@npm:1.1.0":
  locations:
    - "node_modules/@rtsao/scc"

"@sideway/address@npm:4.1.5":
  locations:
    - "node_modules/@sideway/address"

"@sideway/formula@npm:3.0.1":
  locations:
    - "node_modules/@sideway/formula"

"@sideway/pinpoint@npm:2.0.0":
  locations:
    - "node_modules/@sideway/pinpoint"

"@sinclair/typebox@npm:0.27.8":
  locations:
    - "node_modules/@sinclair/typebox"

"@sindresorhus/is@npm:0.14.0":
  locations:
    - "node_modules/package-json/node_modules/@sindresorhus/is"

"@sindresorhus/is@npm:0.7.0":
  locations:
    - "node_modules/@sindresorhus/is"

"@sindresorhus/merge-streams@npm:2.3.0":
  locations:
    - "node_modules/@sindresorhus/merge-streams"

"@stencil/core@npm:2.22.3":
  locations:
    - "node_modules/@stencil/core"

"@swc/core-darwin-arm64@npm:1.3.96":
  locations:
    - "node_modules/@swc/core-darwin-arm64"

"@swc/core@virtual:6c93d6fc8b1052804d8b1d2b5eb8bbf8aa691f033f1b8d086b4490bf7bd34dd3c8d366733bc5b4f6a2316a79b5ce76b685e4726390734083337201336cf7040a#npm:1.3.96":
  locations:
    - "node_modules/@swc/core"

"@swc/counter@npm:0.1.3":
  locations:
    - "node_modules/@swc/counter"

"@swc/register@virtual:6c93d6fc8b1052804d8b1d2b5eb8bbf8aa691f033f1b8d086b4490bf7bd34dd3c8d366733bc5b4f6a2316a79b5ce76b685e4726390734083337201336cf7040a#npm:0.1.10":
  locations:
    - "node_modules/@swc/register"

"@swc/types@npm:0.1.23":
  locations:
    - "node_modules/@swc/types"

"@szmarczak/http-timer@npm:1.1.2":
  locations:
    - "node_modules/@szmarczak/http-timer"

"@tarojs/api@virtual:909cd5e34d83c1b4f59a3027f47d4e3a96837e1fa7ca152ab7f48d57725540f101ff15abb818dc67a7bc13fcb505879105fdb71aef39bd06f908ddac3b5f7c0b#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/api"

"@tarojs/binding-darwin-arm64@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/binding-darwin-arm64"

"@tarojs/binding@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/binding"

"@tarojs/cli@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/cli"

"@tarojs/components-react@virtual:85b9a503bc575d31e42650688141eff29652f24a02e0afc5637884f84330ca65bb6019673ed1d695aabd37c42f9e30d4c1976f6b8405b54728f285be3db50dd5#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/components-react"
  aliases:
    - "virtual:fd1e40b8b0ae530ee29708b7aca0760099d8ecba15342810d98b5a0e1a1e594fd2139e4cd9acf6cb79ac6df328d98971c32043a58a1f4d1abb1a41cc07947db8#npm:4.1.4"

"@tarojs/components@virtual:85b9a503bc575d31e42650688141eff29652f24a02e0afc5637884f84330ca65bb6019673ed1d695aabd37c42f9e30d4c1976f6b8405b54728f285be3db50dd5#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/components"
  aliases:
    - "virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4"

"@tarojs/helper@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/helper"

"@tarojs/plugin-doctor-darwin-arm64@npm:0.0.13":
  locations:
    - "node_modules/@tarojs/plugin-doctor-darwin-arm64"

"@tarojs/plugin-doctor-darwin-universal@npm:0.0.13":
  locations:
    - "node_modules/@tarojs/plugin-doctor-darwin-universal"

"@tarojs/plugin-doctor@npm:0.0.13":
  locations:
    - "node_modules/@tarojs/plugin-doctor"

"@tarojs/plugin-framework-react@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-framework-react"

"@tarojs/plugin-generator@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-generator"

"@tarojs/plugin-platform-alipay@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-alipay"

"@tarojs/plugin-platform-h5@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-h5"

"@tarojs/plugin-platform-harmony-hybrid@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-harmony-hybrid"

"@tarojs/plugin-platform-jd@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-jd"

"@tarojs/plugin-platform-qq@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-qq"

"@tarojs/plugin-platform-swan@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-swan"

"@tarojs/plugin-platform-tt@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-tt"

"@tarojs/plugin-platform-weapp@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/plugin-platform-weapp"

"@tarojs/react@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/react"

"@tarojs/router@virtual:03a343053825f3c9d9cc0dc634ec64e3a48eab66200283403845352d646ce3f70009d7fada856bbb004c7257a79a5569d0c625684f68215d87164a7795c76910#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/router"

"@tarojs/runner-utils@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/runner-utils"

"@tarojs/runtime@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/runtime"

"@tarojs/service@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/service"

"@tarojs/shared@npm:4.1.4":
  locations:
    - "node_modules/@tarojs/shared"

"@tarojs/taro-h5@virtual:85b9a503bc575d31e42650688141eff29652f24a02e0afc5637884f84330ca65bb6019673ed1d695aabd37c42f9e30d4c1976f6b8405b54728f285be3db50dd5#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/taro-h5"

"@tarojs/taro-loader@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/taro-loader"

"@tarojs/taro@virtual:4c4dfe426275788c88ddc779784f13f6adf144b155f602414f6b9d5b1a40ad8a293f82fecb87d920b3d36bb2223a9b3dac897826c6565614a385d0bcfbca02e7#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/taro"
  aliases:
    - "virtual:cfd647785b3649ea2b15685a3c788d556998a329881a3e42a06c64889517507f04e8e6ea3b5a8f409360c4419a301d0c1406574b71ebc6e89b52ffad5c3da175#npm:4.1.4"
    - "virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4"

"@tarojs/webpack5-prebundle@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/webpack5-prebundle"

"@tarojs/webpack5-runner@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/@tarojs/webpack5-runner"

"@trysound/sax@npm:0.2.0":
  locations:
    - "node_modules/@trysound/sax"

"@ts-morph/common@npm:0.27.0":
  locations:
    - "node_modules/@ts-morph/common"

"@types/archy@npm:0.0.31":
  locations:
    - "node_modules/@types/archy"

"@types/body-parser@npm:1.19.6":
  locations:
    - "node_modules/@types/body-parser"

"@types/bonjour@npm:3.5.13":
  locations:
    - "node_modules/@types/bonjour"

"@types/connect-history-api-fallback@npm:1.5.4":
  locations:
    - "node_modules/@types/connect-history-api-fallback"

"@types/connect@npm:3.4.38":
  locations:
    - "node_modules/@types/connect"

"@types/conventional-commits-parser@npm:5.0.1":
  locations:
    - "node_modules/@types/conventional-commits-parser"

"@types/debug@npm:4.1.12":
  locations:
    - "node_modules/@types/debug"

"@types/eslint-scope@npm:3.7.7":
  locations:
    - "node_modules/@types/eslint-scope"

"@types/eslint@npm:9.6.1":
  locations:
    - "node_modules/@types/eslint"

"@types/estree@npm:1.0.8":
  locations:
    - "node_modules/@types/estree"

"@types/express-serve-static-core@npm:4.19.6":
  locations:
    - "node_modules/@types/express/node_modules/@types/express-serve-static-core"

"@types/express-serve-static-core@npm:5.0.7":
  locations:
    - "node_modules/@types/express-serve-static-core"

"@types/express@npm:4.17.23":
  locations:
    - "node_modules/@types/express"

"@types/express@npm:5.0.3":
  locations:
    - "node_modules/@types/serve-index/node_modules/@types/express"

"@types/fs-extra@npm:8.1.5":
  locations:
    - "node_modules/@types/fs-extra"

"@types/glob@npm:7.2.0":
  locations:
    - "node_modules/@types/glob"

"@types/html-minifier-terser@npm:6.1.0":
  locations:
    - "node_modules/@types/html-minifier-terser"

"@types/http-errors@npm:2.0.5":
  locations:
    - "node_modules/@types/http-errors"

"@types/http-proxy@npm:1.17.16":
  locations:
    - "node_modules/@types/http-proxy"

"@types/istanbul-lib-coverage@npm:2.0.6":
  locations:
    - "node_modules/@types/istanbul-lib-coverage"

"@types/istanbul-lib-report@npm:3.0.3":
  locations:
    - "node_modules/@types/istanbul-lib-report"

"@types/istanbul-reports@npm:3.0.4":
  locations:
    - "node_modules/@types/istanbul-reports"

"@types/json-schema@npm:7.0.15":
  locations:
    - "node_modules/@types/json-schema"

"@types/json5@npm:0.0.29":
  locations:
    - "node_modules/@types/json5"

"@types/keyv@npm:3.1.4":
  locations:
    - "node_modules/@types/keyv"

"@types/lodash.debounce@npm:4.0.9":
  locations:
    - "node_modules/@types/lodash.debounce"

"@types/lodash@npm:4.17.20":
  locations:
    - "node_modules/@types/lodash"

"@types/mime@npm:1.3.5":
  locations:
    - "node_modules/@types/mime"

"@types/minimatch@npm:5.1.2":
  locations:
    - "node_modules/@types/minimatch"

"@types/ms@npm:2.1.0":
  locations:
    - "node_modules/@types/ms"

"@types/node-forge@npm:1.3.13":
  locations:
    - "node_modules/@types/node-forge"

"@types/node@npm:18.19.120":
  locations:
    - "node_modules/@types/node"

"@types/node@npm:24.0.15":
  locations:
    - "node_modules/terser-webpack-plugin/node_modules/@types/node"
    - "node_modules/pretty-format/node_modules/@types/node"
    - "node_modules/jest-worker/node_modules/@types/node"
    - "node_modules/jest-util/node_modules/@types/node"
    - "node_modules/@types/ws/node_modules/@types/node"
    - "node_modules/@types/sockjs/node_modules/@types/node"
    - "node_modules/@types/serve-static/node_modules/@types/node"
    - "node_modules/@types/send/node_modules/@types/node"
    - "node_modules/@types/sass/node_modules/@types/node"
    - "node_modules/@types/responselike/node_modules/@types/node"
    - "node_modules/@types/postcss-url/node_modules/@types/node"
    - "node_modules/@types/node-forge/node_modules/@types/node"
    - "node_modules/@types/keyv/node_modules/@types/node"
    - "node_modules/@types/http-proxy/node_modules/@types/node"
    - "node_modules/@types/glob/node_modules/@types/node"
    - "node_modules/@types/fs-extra/node_modules/@types/node"
    - "node_modules/@types/express/node_modules/@types/node"
    - "node_modules/@types/express-serve-static-core/node_modules/@types/node"
    - "node_modules/@types/conventional-commits-parser/node_modules/@types/node"
    - "node_modules/@types/connect/node_modules/@types/node"
    - "node_modules/@types/connect-history-api-fallback/node_modules/@types/node"
    - "node_modules/@types/bonjour/node_modules/@types/node"
    - "node_modules/@types/body-parser/node_modules/@types/node"
    - "node_modules/@jest/types/node_modules/@types/node"

"@types/postcss-url@npm:10.0.4":
  locations:
    - "node_modules/@types/postcss-url"

"@types/prop-types@npm:15.7.15":
  locations:
    - "node_modules/@types/prop-types"

"@types/qs@npm:6.14.0":
  locations:
    - "node_modules/@types/qs"

"@types/range-parser@npm:1.2.7":
  locations:
    - "node_modules/@types/range-parser"

"@types/react@npm:18.3.23":
  locations:
    - "node_modules/@types/react"

"@types/responselike@npm:1.0.3":
  locations:
    - "node_modules/@types/responselike"

"@types/retry@npm:0.12.0":
  locations:
    - "node_modules/@types/retry"

"@types/sass@npm:1.43.1":
  locations:
    - "node_modules/@types/sass"

"@types/semver@npm:7.7.0":
  locations:
    - "node_modules/@types/semver"

"@types/send@npm:0.17.5":
  locations:
    - "node_modules/@types/send"

"@types/serve-index@npm:1.9.4":
  locations:
    - "node_modules/@types/serve-index"

"@types/serve-static@npm:1.15.8":
  locations:
    - "node_modules/@types/serve-static"

"@types/sockjs@npm:0.3.36":
  locations:
    - "node_modules/@types/sockjs"

"@types/webpack-env@npm:1.18.8":
  locations:
    - "node_modules/@types/webpack-env"

"@types/ws@npm:8.18.1":
  locations:
    - "node_modules/@types/ws"

"@types/yargs-parser@npm:21.0.3":
  locations:
    - "node_modules/@types/yargs-parser"

"@types/yargs@npm:15.0.19":
  locations:
    - "node_modules/pretty-format/node_modules/@types/yargs"

"@types/yargs@npm:17.0.33":
  locations:
    - "node_modules/@types/yargs"

"@typescript-eslint/eslint-plugin@virtual:f17df7b218a3670ebca7472c220766bd25fcf3ce525b4f4e3f6d044d127e793c5f67ca710071f2156eac6e1ea2d8ec2cac0a3e68c256ebd1921c52945227ae76#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin"

"@typescript-eslint/parser@virtual:f17df7b218a3670ebca7472c220766bd25fcf3ce525b4f4e3f6d044d127e793c5f67ca710071f2156eac6e1ea2d8ec2cac0a3e68c256ebd1921c52945227ae76#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/parser"

"@typescript-eslint/scope-manager@npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/scope-manager"

"@typescript-eslint/type-utils@virtual:ef5ce8a2d1de673415b872ed9516c507b2b800a096426c6abdcedaee20fe3c53630df3720131e09b5879b117ce8ae7c7f98dd80950d0f26dc28ca84d94a061a4#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/type-utils"

"@typescript-eslint/types@npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/types"

"@typescript-eslint/typescript-estree@virtual:34557b45a8a06d62d6b76578d6c53ed6c9f815ba8235a989541b3aa15205d3552f19e831c797d1478c01e151af781ee0a025c2b3b21efe9181556d7ac62e0e23#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree"

"@typescript-eslint/utils@virtual:ef5ce8a2d1de673415b872ed9516c507b2b800a096426c6abdcedaee20fe3c53630df3720131e09b5879b117ce8ae7c7f98dd80950d0f26dc28ca84d94a061a4#npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/utils"

"@typescript-eslint/visitor-keys@npm:6.21.0":
  locations:
    - "node_modules/@typescript-eslint/visitor-keys"

"@ungap/structured-clone@npm:1.3.0":
  locations:
    - "node_modules/@ungap/structured-clone"

"@webassemblyjs/ast@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/ast"

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/floating-point-hex-parser"

"@webassemblyjs/helper-api-error@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/helper-api-error"

"@webassemblyjs/helper-buffer@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/helper-buffer"

"@webassemblyjs/helper-numbers@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/helper-numbers"

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/helper-wasm-bytecode"

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/helper-wasm-section"

"@webassemblyjs/ieee754@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/ieee754"

"@webassemblyjs/leb128@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/leb128"

"@webassemblyjs/utf8@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/utf8"

"@webassemblyjs/wasm-edit@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-edit"

"@webassemblyjs/wasm-gen@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-gen"

"@webassemblyjs/wasm-opt@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-opt"

"@webassemblyjs/wasm-parser@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-parser"

"@webassemblyjs/wast-printer@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wast-printer"

"@xtuc/ieee754@npm:1.2.0":
  locations:
    - "node_modules/@xtuc/ieee754"

"@xtuc/long@npm:4.2.2":
  locations:
    - "node_modules/@xtuc/long"

"JSONStream@npm:1.3.5":
  locations:
    - "node_modules/JSONStream"

"abbrev@npm:3.0.1":
  locations:
    - "node_modules/abbrev"

"abortcontroller-polyfill@npm:1.7.8":
  locations:
    - "node_modules/abortcontroller-polyfill"

"accepts@npm:1.3.8":
  locations:
    - "node_modules/accepts"

"acorn-import-assertions@virtual:1b005cc486314ab2650d6dafee33b27297fc25907e0d543dacc6d9920a74d794fab5914693c8b5b84f40ec58a16155278af860b16c03011283a613169820ff32#npm:1.9.0":
  locations:
    - "node_modules/acorn-import-assertions"

"acorn-jsx@virtual:a50722a5a9326b6a5f12350c494c4db3aa0f4caeac45e3e9e5fe071da20014ecfe738fe2ebe2c9c98abae81a4ea86b42f56d776b3bd5ec37f9ad3670c242b242#npm:5.3.2":
  locations:
    - "node_modules/acorn-jsx"

"acorn-walk@npm:8.3.4":
  locations:
    - "node_modules/acorn-walk"

"acorn@npm:8.15.0":
  locations:
    - "node_modules/acorn"

"address@npm:1.2.2":
  locations:
    - "node_modules/address"

"adjust-sourcemap-loader@npm:4.0.0":
  locations:
    - "node_modules/adjust-sourcemap-loader"

"adm-zip@npm:0.5.16":
  locations:
    - "node_modules/adm-zip"

"agent-base@npm:7.1.4":
  locations:
    - "node_modules/agent-base"

"ajv-formats@virtual:7d68b1c0fde37300f56685f7bb4c28ebea1b0104d72a9753a9c1cd828a7af871eef630afc629d50afb995ee91b4816b63a5a2727399876aa4a4f0405da35dc08#npm:2.1.1":
  locations:
    - "node_modules/schema-utils/node_modules/ajv-formats"

"ajv-keywords@virtual:7d68b1c0fde37300f56685f7bb4c28ebea1b0104d72a9753a9c1cd828a7af871eef630afc629d50afb995ee91b4816b63a5a2727399876aa4a4f0405da35dc08#npm:5.1.0":
  locations:
    - "node_modules/schema-utils/node_modules/ajv-keywords"

"ajv-keywords@virtual:f84d18c473fad3c01e1cf352f81ad13de804ca40da5bf6e752464a2e78dcb097ad579b06da5ff33a55ba9957fb9c74909b99fc5e215420a3f9b5dc87ad71363b#npm:3.5.2":
  locations:
    - "node_modules/ajv-keywords"

"ajv@npm:6.12.6":
  locations:
    - "node_modules/ajv"

"ajv@npm:8.17.1":
  locations:
    - "node_modules/table/node_modules/ajv"
    - "node_modules/schema-utils/node_modules/ajv"
    - "node_modules/@commitlint/config-validator/node_modules/ajv"

"ansi-escapes@npm:4.3.2":
  locations:
    - "node_modules/ansi-escapes"

"ansi-escapes@npm:7.0.0":
  locations:
    - "node_modules/log-update/node_modules/ansi-escapes"

"ansi-html-community@npm:0.0.8":
  locations:
    - "node_modules/ansi-html-community"

"ansi-html@npm:0.0.9":
  locations:
    - "node_modules/ansi-html"

"ansi-regex@npm:5.0.1":
  locations:
    - "node_modules/ansi-regex"

"ansi-regex@npm:6.1.0":
  locations:
    - "node_modules/log-update/node_modules/ansi-regex"
    - "node_modules/listr2/node_modules/ansi-regex"
    - "node_modules/cli-truncate/node_modules/ansi-regex"
    - "node_modules/@isaacs/cliui/node_modules/ansi-regex"

"ansi-styles@npm:4.3.0":
  locations:
    - "node_modules/ansi-styles"

"ansi-styles@npm:6.2.1":
  locations:
    - "node_modules/slice-ansi/node_modules/ansi-styles"
    - "node_modules/log-update/node_modules/ansi-styles"
    - "node_modules/listr2/node_modules/ansi-styles"
    - "node_modules/@isaacs/cliui/node_modules/ansi-styles"

"any-promise@npm:1.3.0":
  locations:
    - "node_modules/any-promise"

"anymatch@npm:3.1.3":
  locations:
    - "node_modules/anymatch"

"archive-type@npm:4.0.0":
  locations:
    - "node_modules/archive-type"

"archy@npm:1.0.0":
  locations:
    - "node_modules/archy"

"argparse@npm:2.0.1":
  locations:
    - "node_modules/argparse"

"array-buffer-byte-length@npm:1.0.2":
  locations:
    - "node_modules/array-buffer-byte-length"

"array-flatten@npm:1.1.1":
  locations:
    - "node_modules/array-flatten"

"array-ify@npm:1.0.0":
  locations:
    - "node_modules/array-ify"

"array-includes@npm:3.1.9":
  locations:
    - "node_modules/array-includes"

"array-union@npm:2.1.0":
  locations:
    - "node_modules/array-union"

"array.prototype.findlast@npm:1.2.5":
  locations:
    - "node_modules/array.prototype.findlast"

"array.prototype.findlastindex@npm:1.2.6":
  locations:
    - "node_modules/array.prototype.findlastindex"

"array.prototype.flat@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flat"

"array.prototype.flatmap@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flatmap"

"array.prototype.tosorted@npm:1.1.4":
  locations:
    - "node_modules/array.prototype.tosorted"

"arraybuffer.prototype.slice@npm:1.0.4":
  locations:
    - "node_modules/arraybuffer.prototype.slice"

"astral-regex@npm:2.0.0":
  locations:
    - "node_modules/astral-regex"

"async-function@npm:1.0.0":
  locations:
    - "node_modules/async-function"

"asynckit@npm:0.4.0":
  locations:
    - "node_modules/asynckit"

"autoprefixer@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:10.4.21":
  locations:
    - "node_modules/autoprefixer"

"available-typed-arrays@npm:1.0.7":
  locations:
    - "node_modules/available-typed-arrays"

"axios@npm:1.10.0":
  locations:
    - "node_modules/axios"

"babel-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:8.2.1":
  locations:
    - "node_modules/babel-loader"

"babel-plugin-const-enum@virtual:53fdb6efa29f838bd9684b89a6746dc5c336835e0a3c3d87334e9fa21ab93c1b70885ba842b83c7cc79b0286de89a7a43670f39dda6441adcea7f645a259aa89#npm:1.2.0":
  locations:
    - "node_modules/babel-plugin-const-enum"

"babel-plugin-dynamic-import-node@npm:2.3.3":
  locations:
    - "node_modules/babel-plugin-dynamic-import-node"

"babel-plugin-polyfill-corejs2@virtual:5bd95e186f41afb7e045a5b1b0a008fbd62a8a3d0ed4da3cb3183aae7c66cbaa5ff3ae2f85d2a6e3a94b6d956ce29f02ba040f2d044fe3b34a6b269c115667e4#npm:0.4.14":
  locations:
    - "node_modules/babel-plugin-polyfill-corejs2"

"babel-plugin-polyfill-corejs3@virtual:5bd95e186f41afb7e045a5b1b0a008fbd62a8a3d0ed4da3cb3183aae7c66cbaa5ff3ae2f85d2a6e3a94b6d956ce29f02ba040f2d044fe3b34a6b269c115667e4#npm:0.13.0":
  locations:
    - "node_modules/babel-plugin-polyfill-corejs3"

"babel-plugin-polyfill-regenerator@virtual:5bd95e186f41afb7e045a5b1b0a008fbd62a8a3d0ed4da3cb3183aae7c66cbaa5ff3ae2f85d2a6e3a94b6d956ce29f02ba040f2d044fe3b34a6b269c115667e4#npm:0.6.5":
  locations:
    - "node_modules/babel-plugin-polyfill-regenerator"

"babel-plugin-transform-imports-api@npm:1.0.0":
  locations:
    - "node_modules/babel-plugin-transform-imports-api"

"babel-plugin-transform-solid-jsx@npm:4.1.4":
  locations:
    - "node_modules/babel-plugin-transform-solid-jsx"

"babel-plugin-transform-taroapi@virtual:85b9a503bc575d31e42650688141eff29652f24a02e0afc5637884f84330ca65bb6019673ed1d695aabd37c42f9e30d4c1976f6b8405b54728f285be3db50dd5#npm:4.1.4":
  locations:
    - "node_modules/babel-plugin-transform-taroapi"
  aliases:
    - "virtual:fd1e40b8b0ae530ee29708b7aca0760099d8ecba15342810d98b5a0e1a1e594fd2139e4cd9acf6cb79ac6df328d98971c32043a58a1f4d1abb1a41cc07947db8#npm:4.1.4"

"babel-preset-taro@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/babel-preset-taro"

"balanced-match@npm:1.0.2":
  locations:
    - "node_modules/balanced-match"

"balanced-match@npm:2.0.0":
  locations:
    - "node_modules/stylelint/node_modules/balanced-match"

"base64-js@npm:1.5.1":
  locations:
    - "node_modules/base64-js"

"batch@npm:0.6.1":
  locations:
    - "node_modules/batch"

"big.js@npm:5.2.2":
  locations:
    - "node_modules/big.js"

"binary-extensions@npm:2.3.0":
  locations:
    - "node_modules/binary-extensions"

"bl@npm:1.2.3":
  locations:
    - "node_modules/tar-stream/node_modules/bl"

"bl@npm:4.1.0":
  locations:
    - "node_modules/bl"

"body-parser@npm:1.20.3":
  locations:
    - "node_modules/body-parser"

"bonjour-service@npm:1.3.0":
  locations:
    - "node_modules/bonjour-service"

"boolbase@npm:1.0.0":
  locations:
    - "node_modules/boolbase"

"brace-expansion@npm:1.1.12":
  locations:
    - "node_modules/postcss-url/node_modules/brace-expansion"
    - "node_modules/minimatch/node_modules/brace-expansion"

"brace-expansion@npm:2.0.2":
  locations:
    - "node_modules/brace-expansion"

"braces@npm:3.0.3":
  locations:
    - "node_modules/braces"

"browserslist@npm:4.25.1":
  locations:
    - "node_modules/browserslist"

"buffer-alloc-unsafe@npm:1.1.0":
  locations:
    - "node_modules/buffer-alloc-unsafe"

"buffer-alloc@npm:1.2.0":
  locations:
    - "node_modules/buffer-alloc"

"buffer-crc32@npm:0.2.13":
  locations:
    - "node_modules/buffer-crc32"

"buffer-fill@npm:1.0.0":
  locations:
    - "node_modules/buffer-fill"

"buffer-from@npm:1.1.2":
  locations:
    - "node_modules/buffer-from"

"buffer@npm:5.7.1":
  locations:
    - "node_modules/buffer"

"bytes@npm:3.1.2":
  locations:
    - "node_modules/bytes"

"cacache@npm:19.0.1":
  locations:
    - "node_modules/cacache"

"cacheable-request@npm:2.1.4":
  locations:
    - "node_modules/cacheable-request"

"cacheable-request@npm:6.1.0":
  locations:
    - "node_modules/package-json/node_modules/cacheable-request"

"cacheable@npm:1.10.2":
  locations:
    - "node_modules/cacheable"

"call-bind-apply-helpers@npm:1.0.2":
  locations:
    - "node_modules/call-bind-apply-helpers"

"call-bind@npm:1.0.8":
  locations:
    - "node_modules/call-bind"

"call-bound@npm:1.0.4":
  locations:
    - "node_modules/call-bound"

"callsites@npm:3.1.0":
  locations:
    - "node_modules/callsites"

"camel-case@npm:3.0.0":
  locations:
    - "node_modules/html-minifier/node_modules/camel-case"

"camel-case@npm:4.1.2":
  locations:
    - "node_modules/camel-case"

"caniuse-api@npm:3.0.0":
  locations:
    - "node_modules/caniuse-api"

"caniuse-lite@npm:1.0.30001727":
  locations:
    - "node_modules/caniuse-lite"

"capital-case@npm:1.0.4":
  locations:
    - "node_modules/capital-case"

"caw@npm:2.0.1":
  locations:
    - "node_modules/caw"

"chalk@npm:3.0.0":
  locations:
    - "node_modules/scss-bundle/node_modules/chalk"

"chalk@npm:4.1.2":
  locations:
    - "node_modules/chalk"

"chalk@npm:5.4.1":
  locations:
    - "node_modules/lint-staged/node_modules/chalk"
    - "node_modules/@commitlint/types/node_modules/chalk"
    - "node_modules/@commitlint/load/node_modules/chalk"
    - "node_modules/@commitlint/format/node_modules/chalk"

"change-case@npm:4.1.2":
  locations:
    - "node_modules/change-case"

"chardet@npm:0.7.0":
  locations:
    - "node_modules/chardet"

"charenc@npm:0.0.2":
  locations:
    - "node_modules/charenc"

"chokidar@npm:3.6.0":
  locations:
    - "node_modules/chokidar"

"chokidar@npm:4.0.3":
  locations:
    - "node_modules/sass/node_modules/chokidar"

"chownr@npm:3.0.0":
  locations:
    - "node_modules/chownr"

"chrome-trace-event@npm:1.0.4":
  locations:
    - "node_modules/chrome-trace-event"

"ci-info@npm:3.9.0":
  locations:
    - "node_modules/ci-info"

"classnames@npm:2.5.1":
  locations:
    - "node_modules/classnames"

"clean-css@npm:4.2.4":
  locations:
    - "node_modules/clean-css"

"clean-css@npm:5.3.3":
  locations:
    - "node_modules/html-minifier-terser/node_modules/clean-css"

"cli-cursor@npm:3.1.0":
  locations:
    - "node_modules/cli-cursor"

"cli-cursor@npm:5.0.0":
  locations:
    - "node_modules/log-update/node_modules/cli-cursor"

"cli-highlight@npm:2.1.11":
  locations:
    - "node_modules/cli-highlight"

"cli-spinners@npm:2.9.2":
  locations:
    - "node_modules/cli-spinners"

"cli-truncate@npm:4.0.0":
  locations:
    - "node_modules/cli-truncate"

"cli-width@npm:3.0.0":
  locations:
    - "node_modules/cli-width"

"cliui@npm:7.0.4":
  locations:
    - "node_modules/cli-highlight/node_modules/cliui"

"cliui@npm:8.0.1":
  locations:
    - "node_modules/cliui"

"clone-deep@npm:4.0.1":
  locations:
    - "node_modules/clone-deep"

"clone-response@npm:1.0.2":
  locations:
    - "node_modules/clone-response"

"clone-response@npm:1.0.3":
  locations:
    - "node_modules/package-json/node_modules/clone-response"

"clone@npm:1.0.4":
  locations:
    - "node_modules/clone"

"code-block-writer@npm:13.0.3":
  locations:
    - "node_modules/code-block-writer"

"color-convert@npm:2.0.1":
  locations:
    - "node_modules/color-convert"

"color-name@npm:1.1.4":
  locations:
    - "node_modules/color-name"

"colord@npm:2.9.3":
  locations:
    - "node_modules/colord"

"colorette@npm:2.0.20":
  locations:
    - "node_modules/colorette"

"combined-stream@npm:1.0.8":
  locations:
    - "node_modules/combined-stream"

"commander@npm:14.0.0":
  locations:
    - "node_modules/lint-staged/node_modules/commander"

"commander@npm:2.20.3":
  locations:
    - "node_modules/commander"

"commander@npm:4.1.1":
  locations:
    - "node_modules/scss-bundle/node_modules/commander"

"commander@npm:7.2.0":
  locations:
    - "node_modules/svgo/node_modules/commander"

"commander@npm:8.3.0":
  locations:
    - "node_modules/html-minifier-terser/node_modules/commander"

"commondir@npm:1.0.1":
  locations:
    - "node_modules/commondir"

"compare-func@npm:2.0.0":
  locations:
    - "node_modules/compare-func"

"compressible@npm:2.0.18":
  locations:
    - "node_modules/compressible"

"compression@npm:1.8.1":
  locations:
    - "node_modules/compression"

"concat-map@npm:0.0.1":
  locations:
    - "node_modules/concat-map"

"config-chain@npm:1.1.13":
  locations:
    - "node_modules/config-chain"

"connect-history-api-fallback@npm:2.0.0":
  locations:
    - "node_modules/connect-history-api-fallback"

"consola@npm:2.15.3":
  locations:
    - "node_modules/consola"

"constant-case@npm:3.0.4":
  locations:
    - "node_modules/constant-case"

"content-disposition@npm:0.5.4":
  locations:
    - "node_modules/content-disposition"

"content-type@npm:1.0.5":
  locations:
    - "node_modules/content-type"

"conventional-changelog-angular@npm:7.0.0":
  locations:
    - "node_modules/conventional-changelog-angular"

"conventional-changelog-conventionalcommits@npm:7.0.2":
  locations:
    - "node_modules/conventional-changelog-conventionalcommits"

"conventional-commits-parser@npm:5.0.0":
  locations:
    - "node_modules/conventional-commits-parser"

"convert-source-map@npm:1.9.0":
  locations:
    - "node_modules/resolve-url-loader/node_modules/convert-source-map"

"convert-source-map@npm:2.0.0":
  locations:
    - "node_modules/convert-source-map"

"cookie-signature@npm:1.0.6":
  locations:
    - "node_modules/cookie-signature"

"cookie@npm:0.7.1":
  locations:
    - "node_modules/cookie"

"copy-anything@npm:2.0.6":
  locations:
    - "node_modules/copy-anything"

"copy-webpack-plugin@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:12.0.2":
  locations:
    - "node_modules/copy-webpack-plugin"

"core-js-compat@npm:3.44.0":
  locations:
    - "node_modules/core-js-compat"

"core-js-pure@npm:3.44.0":
  locations:
    - "node_modules/core-js-pure"

"core-js@npm:3.44.0":
  locations:
    - "node_modules/core-js"

"core-util-is@npm:1.0.3":
  locations:
    - "node_modules/core-util-is"

"cosmiconfig-typescript-loader@virtual:8db6360698ad96cf4b25d1172a40395172958e0549466343255618b9c4b0c60af8d85565dd602407804feddb0d412d25606d8d9faedb04655cf7d13a90395456#npm:6.1.0":
  locations:
    - "node_modules/cosmiconfig-typescript-loader"

"cosmiconfig@virtual:8db6360698ad96cf4b25d1172a40395172958e0549466343255618b9c4b0c60af8d85565dd602407804feddb0d412d25606d8d9faedb04655cf7d13a90395456#npm:9.0.0":
  locations:
    - "node_modules/cosmiconfig"

"cross-spawn@npm:7.0.6":
  locations:
    - "node_modules/cross-spawn"

"crypt@npm:0.0.2":
  locations:
    - "node_modules/crypt"

"css-declaration-sorter@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:7.2.0":
  locations:
    - "node_modules/css-declaration-sorter"

"css-functions-list@npm:3.2.3":
  locations:
    - "node_modules/css-functions-list"

"css-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:7.1.2":
  locations:
    - "node_modules/css-loader"

"css-minimizer-webpack-plugin@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:6.0.0":
  locations:
    - "node_modules/css-minimizer-webpack-plugin"

"css-select@npm:4.3.0":
  locations:
    - "node_modules/renderkid/node_modules/css-select"

"css-select@npm:5.2.2":
  locations:
    - "node_modules/css-select"

"css-tree@npm:1.0.0-alpha.29":
  locations:
    - "node_modules/miniprogram-simulate/node_modules/css-tree"

"css-tree@npm:2.2.1":
  locations:
    - "node_modules/csso/node_modules/css-tree"

"css-tree@npm:2.3.1":
  locations:
    - "node_modules/css-tree"

"css-tree@npm:3.1.0":
  locations:
    - "node_modules/stylelint/node_modules/css-tree"

"css-what@npm:6.2.2":
  locations:
    - "node_modules/css-what"

"cssesc@npm:3.0.0":
  locations:
    - "node_modules/cssesc"

"cssnano-preset-default@virtual:884b5ffe2e6b4ba303c17b34459e51b0811fcf97fcb099f1a77a727735fa0328a44f44039efbaa20cffd20ce0b65a9da3f1c5741dda6b310c506d93d8e589568#npm:6.1.2":
  locations:
    - "node_modules/cssnano-preset-default"

"cssnano-utils@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:4.0.2":
  locations:
    - "node_modules/cssnano-utils"

"cssnano@virtual:67021b688143efb89257d3ef51b64d2a9baf7e7f0cd4ad432c79efaf6a6bb72585933493f9647c15e5b4d743f610cd6f1016e3a28a9847c64b2271819e67a112#npm:6.1.2":
  locations:
    - "node_modules/cssnano"

"csso@npm:3.5.1":
  locations:
    - "node_modules/miniprogram-simulate/node_modules/csso"

"csso@npm:5.0.5":
  locations:
    - "node_modules/csso"

"cssstyle@npm:4.6.0":
  locations:
    - "node_modules/cssstyle"

"csstype@npm:3.1.3":
  locations:
    - "node_modules/csstype"

"cuint@npm:0.2.2":
  locations:
    - "node_modules/cuint"

"dargs@npm:8.1.0":
  locations:
    - "node_modules/dargs"

"data-urls@npm:5.0.0":
  locations:
    - "node_modules/data-urls"

"data-view-buffer@npm:1.0.2":
  locations:
    - "node_modules/data-view-buffer"

"data-view-byte-length@npm:1.0.2":
  locations:
    - "node_modules/data-view-byte-length"

"data-view-byte-offset@npm:1.0.1":
  locations:
    - "node_modules/data-view-byte-offset"

"debug@virtual:2a426afc4b2eef43db12a540d29c2b5476640459bfcd5c24f86bb401cf8cce97e63bd81794d206a5643057e7f662643afd5ce3dfc4d4bfd8e706006c6309c5fa#npm:3.2.7":
  locations:
    - "node_modules/eslint-plugin-import/node_modules/debug"
    - "node_modules/eslint-module-utils/node_modules/debug"
    - "node_modules/eslint-import-resolver-node/node_modules/debug"

"debug@virtual:66b3390b4eccbfa0e7f88b649ba0bbc12a2944432838d6fddbf862e3ce97448fd27286b33697fdcd278339dcbac4ff595eb84b91eeb93160166752a0fcae7007#npm:4.4.1":
  locations:
    - "node_modules/debug"

"debug@virtual:e34a5db40400b9e5b2ff4a99207e6e6259d3054982622ae91b68c81eb2631164c6d9db4ab29c0dbf7620574b8d6b678f010601eafc55a6b832550053c1b61b7b#npm:2.6.9":
  locations:
    - "node_modules/serve-index/node_modules/debug"
    - "node_modules/send/node_modules/debug"
    - "node_modules/finalhandler/node_modules/debug"
    - "node_modules/express/node_modules/debug"
    - "node_modules/compression/node_modules/debug"
    - "node_modules/body-parser/node_modules/debug"

"decimal.js@npm:10.6.0":
  locations:
    - "node_modules/decimal.js"

"decode-uri-component@npm:0.2.2":
  locations:
    - "node_modules/decode-uri-component"

"decode-uri-component@npm:0.4.1":
  locations:
    - "node_modules/query-string/node_modules/decode-uri-component"

"decompress-response@npm:3.3.0":
  locations:
    - "node_modules/decompress-response"

"decompress-tar@npm:4.1.1":
  locations:
    - "node_modules/decompress-tar"

"decompress-tarbz2@npm:4.1.1":
  locations:
    - "node_modules/decompress-tarbz2"

"decompress-targz@npm:4.1.1":
  locations:
    - "node_modules/decompress-targz"

"decompress-unzip@npm:4.0.1":
  locations:
    - "node_modules/decompress-unzip"

"decompress@npm:4.2.1":
  locations:
    - "node_modules/decompress"

"dedent@virtual:83de4da178ae80f886624ad4fef52d028488c1740b7c31c48f7d2281e8bd37f86899fcb321491b66e93c25cf78639cd1242118c159c0237d4ca873888cc11747#npm:1.6.0":
  locations:
    - "node_modules/dedent"

"deep-extend@npm:0.6.0":
  locations:
    - "node_modules/deep-extend"

"deep-is@npm:0.1.4":
  locations:
    - "node_modules/deep-is"

"deepmerge@npm:1.5.2":
  locations:
    - "node_modules/deepmerge"

"default-gateway@npm:6.0.3":
  locations:
    - "node_modules/default-gateway"

"defaults@npm:1.0.4":
  locations:
    - "node_modules/defaults"

"defer-to-connect@npm:1.1.3":
  locations:
    - "node_modules/defer-to-connect"

"define-data-property@npm:1.1.4":
  locations:
    - "node_modules/define-data-property"

"define-lazy-prop@npm:2.0.0":
  locations:
    - "node_modules/define-lazy-prop"

"define-properties@npm:1.2.1":
  locations:
    - "node_modules/define-properties"

"delayed-stream@npm:1.0.0":
  locations:
    - "node_modules/delayed-stream"

"depd@npm:1.1.2":
  locations:
    - "node_modules/serve-index/node_modules/depd"

"depd@npm:2.0.0":
  locations:
    - "node_modules/depd"

"destroy@npm:1.2.0":
  locations:
    - "node_modules/destroy"

"detect-libc@npm:1.0.3":
  locations:
    - "node_modules/detect-libc"

"detect-libc@npm:2.0.4":
  locations:
    - "node_modules/lightningcss/node_modules/detect-libc"

"detect-node@npm:2.1.0":
  locations:
    - "node_modules/detect-node"

"detect-port@npm:1.6.1":
  locations:
    - "node_modules/detect-port"

"dingtalk-jsapi@npm:2.15.6":
  locations:
    - "node_modules/dingtalk-jsapi"

"dir-glob@npm:3.0.1":
  locations:
    - "node_modules/dir-glob"

"dns-packet@npm:5.6.1":
  locations:
    - "node_modules/dns-packet"

"doctrine@npm:2.1.0":
  locations:
    - "node_modules/eslint-plugin-react/node_modules/doctrine"
    - "node_modules/eslint-plugin-import/node_modules/doctrine"

"doctrine@npm:3.0.0":
  locations:
    - "node_modules/doctrine"

"dom-converter@npm:0.2.0":
  locations:
    - "node_modules/dom-converter"

"dom-serializer@npm:1.4.1":
  locations:
    - "node_modules/domutils/node_modules/dom-serializer"

"dom-serializer@npm:2.0.0":
  locations:
    - "node_modules/dom-serializer"

"domelementtype@npm:2.3.0":
  locations:
    - "node_modules/domelementtype"

"domhandler@npm:4.3.1":
  locations:
    - "node_modules/domhandler"

"domhandler@npm:5.0.3":
  locations:
    - "node_modules/dom-serializer/node_modules/domhandler"
    - "node_modules/css-select/node_modules/domhandler"

"domutils@npm:2.8.0":
  locations:
    - "node_modules/domutils"

"domutils@npm:3.2.2":
  locations:
    - "node_modules/css-select/node_modules/domutils"

"dot-case@npm:3.0.4":
  locations:
    - "node_modules/dot-case"

"dot-prop@npm:5.3.0":
  locations:
    - "node_modules/dot-prop"

"dotenv-expand@npm:11.0.7":
  locations:
    - "node_modules/dotenv-expand"

"dotenv@npm:16.6.1":
  locations:
    - "node_modules/dotenv"

"download-git-repo@npm:3.0.2":
  locations:
    - "node_modules/download-git-repo"

"download@npm:7.1.0":
  locations:
    - "node_modules/download"

"dunder-proto@npm:1.0.1":
  locations:
    - "node_modules/dunder-proto"

"duplexer3@npm:0.1.5":
  locations:
    - "node_modules/duplexer3"

"eastasianwidth@npm:0.2.0":
  locations:
    - "node_modules/eastasianwidth"

"ee-first@npm:1.1.1":
  locations:
    - "node_modules/ee-first"

"electron-to-chromium@npm:1.5.189":
  locations:
    - "node_modules/electron-to-chromium"

"emoji-regex@npm:10.4.0":
  locations:
    - "node_modules/log-update/node_modules/emoji-regex"
    - "node_modules/listr2/node_modules/emoji-regex"
    - "node_modules/cli-truncate/node_modules/emoji-regex"

"emoji-regex@npm:8.0.0":
  locations:
    - "node_modules/emoji-regex"

"emoji-regex@npm:9.2.2":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/emoji-regex"

"emojis-list@npm:3.0.0":
  locations:
    - "node_modules/emojis-list"

"encodeurl@npm:1.0.2":
  locations:
    - "node_modules/send/node_modules/encodeurl"

"encodeurl@npm:2.0.0":
  locations:
    - "node_modules/encodeurl"

"encoding@npm:0.1.13":
  locations:
    - "node_modules/encoding"

"end-of-stream@npm:1.4.5":
  locations:
    - "node_modules/end-of-stream"

"enhanced-resolve@npm:5.18.2":
  locations:
    - "node_modules/enhanced-resolve"

"entities@npm:2.2.0":
  locations:
    - "node_modules/entities"

"entities@npm:4.5.0":
  locations:
    - "node_modules/dom-serializer/node_modules/entities"

"entities@npm:6.0.1":
  locations:
    - "node_modules/jsdom/node_modules/entities"

"env-paths@npm:2.2.1":
  locations:
    - "node_modules/env-paths"

"envinfo@npm:7.14.0":
  locations:
    - "node_modules/envinfo"

"environment@npm:1.1.0":
  locations:
    - "node_modules/environment"

"err-code@npm:2.0.3":
  locations:
    - "node_modules/err-code"

"errno@npm:0.1.8":
  locations:
    - "node_modules/errno"

"error-ex@npm:1.3.2":
  locations:
    - "node_modules/error-ex"

"error-stack-parser@npm:2.1.4":
  locations:
    - "node_modules/error-stack-parser"

"es-abstract@npm:1.24.0":
  locations:
    - "node_modules/es-abstract"

"es-define-property@npm:1.0.1":
  locations:
    - "node_modules/es-define-property"

"es-errors@npm:1.3.0":
  locations:
    - "node_modules/es-errors"

"es-iterator-helpers@npm:1.2.1":
  locations:
    - "node_modules/es-iterator-helpers"

"es-module-lexer@npm:0.10.5":
  locations:
    - "node_modules/es-module-lexer"

"es-module-lexer@npm:1.7.0":
  locations:
    - "node_modules/webpack/node_modules/es-module-lexer"

"es-object-atoms@npm:1.1.1":
  locations:
    - "node_modules/es-object-atoms"

"es-set-tostringtag@npm:2.1.0":
  locations:
    - "node_modules/es-set-tostringtag"

"es-shim-unscopables@npm:1.1.0":
  locations:
    - "node_modules/es-shim-unscopables"

"es-to-primitive@npm:1.3.0":
  locations:
    - "node_modules/es-to-primitive"

"esbuild-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:4.3.0":
  locations:
    - "node_modules/esbuild-loader"

"esbuild@npm:0.21.5":
  locations:
    - "node_modules/esbuild"

"esbuild@npm:0.25.8":
  locations:
    - "node_modules/esbuild-loader/node_modules/esbuild"

"escalade@npm:3.2.0":
  locations:
    - "node_modules/escalade"

"escape-html@npm:1.0.3":
  locations:
    - "node_modules/escape-html"

"escape-string-regexp@npm:1.0.5":
  locations:
    - "node_modules/escape-string-regexp"

"escape-string-regexp@npm:4.0.0":
  locations:
    - "node_modules/eslint/node_modules/escape-string-regexp"
    - "node_modules/@tarojs/plugin-doctor/node_modules/escape-string-regexp"

"eslint-config-taro@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.1.4":
  locations:
    - "node_modules/eslint-config-taro"

"eslint-import-resolver-node@npm:0.3.9":
  locations:
    - "node_modules/eslint-import-resolver-node"

"eslint-module-utils@virtual:5b99d60d22e4930d3536a5d570dcd2c16c2b5b187df297dcd40426a85709f5bbb3d56a44321c5e787f16a49d9af66ecb91d37efa953de8194ebc4eb5e9da18d9#npm:2.12.1":
  locations:
    - "node_modules/eslint-module-utils"

"eslint-plugin-import@virtual:f17df7b218a3670ebca7472c220766bd25fcf3ce525b4f4e3f6d044d127e793c5f67ca710071f2156eac6e1ea2d8ec2cac0a3e68c256ebd1921c52945227ae76#npm:2.32.0":
  locations:
    - "node_modules/eslint-plugin-import"

"eslint-plugin-react-hooks@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:4.6.2":
  locations:
    - "node_modules/eslint-plugin-react-hooks"

"eslint-plugin-react@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:7.37.5":
  locations:
    - "node_modules/eslint-plugin-react"

"eslint-scope@npm:5.1.1":
  locations:
    - "node_modules/webpack/node_modules/eslint-scope"
    - "node_modules/@nicolo-ribaudo/eslint-scope-5-internals/node_modules/eslint-scope"

"eslint-scope@npm:7.2.2":
  locations:
    - "node_modules/eslint-scope"

"eslint-visitor-keys@npm:2.1.0":
  locations:
    - "node_modules/@babel/eslint-parser/node_modules/eslint-visitor-keys"

"eslint-visitor-keys@npm:3.4.3":
  locations:
    - "node_modules/eslint-visitor-keys"

"eslint@npm:8.41.0":
  locations:
    - "node_modules/@tarojs/plugin-doctor/node_modules/eslint"

"eslint@npm:8.57.1":
  locations:
    - "node_modules/eslint"

"espree@npm:9.6.1":
  locations:
    - "node_modules/espree"

"esquery@npm:1.6.0":
  locations:
    - "node_modules/esquery"

"esrecurse@npm:4.3.0":
  locations:
    - "node_modules/esrecurse"

"estraverse@npm:4.3.0":
  locations:
    - "node_modules/webpack/node_modules/estraverse"
    - "node_modules/@nicolo-ribaudo/eslint-scope-5-internals/node_modules/estraverse"

"estraverse@npm:5.3.0":
  locations:
    - "node_modules/estraverse"

"esutils@npm:2.0.3":
  locations:
    - "node_modules/esutils"

"etag@npm:1.8.1":
  locations:
    - "node_modules/etag"

"eventemitter3@npm:4.0.7":
  locations:
    - "node_modules/eventemitter3"

"eventemitter3@npm:5.0.1":
  locations:
    - "node_modules/listr2/node_modules/eventemitter3"

"events@npm:3.3.0":
  locations:
    - "node_modules/events"

"execa@npm:5.1.1":
  locations:
    - "node_modules/execa"

"exponential-backoff@npm:3.1.2":
  locations:
    - "node_modules/exponential-backoff"

"expr-parser@npm:1.0.0":
  locations:
    - "node_modules/expr-parser"

"express@npm:4.21.2":
  locations:
    - "node_modules/express"

"ext-list@npm:2.2.2":
  locations:
    - "node_modules/ext-list"

"ext-name@npm:5.0.0":
  locations:
    - "node_modules/ext-name"

"external-editor@npm:3.1.0":
  locations:
    - "node_modules/external-editor"

"fast-deep-equal@npm:3.1.3":
  locations:
    - "node_modules/fast-deep-equal"

"fast-glob@npm:3.3.3":
  locations:
    - "node_modules/fast-glob"

"fast-json-stable-stringify@npm:2.1.0":
  locations:
    - "node_modules/fast-json-stable-stringify"

"fast-levenshtein@npm:2.0.6":
  locations:
    - "node_modules/fast-levenshtein"

"fast-uri@npm:3.0.6":
  locations:
    - "node_modules/fast-uri"

"fastest-levenshtein@npm:1.0.16":
  locations:
    - "node_modules/fastest-levenshtein"

"fastq@npm:1.19.1":
  locations:
    - "node_modules/fastq"

"faye-websocket@npm:0.11.4":
  locations:
    - "node_modules/faye-websocket"

"fd-slicer@npm:1.1.0":
  locations:
    - "node_modules/fd-slicer"

"fdir@virtual:d4e4bcf80e67f9de0540c123c7c4882e34dce6a8ba807a0a834f267f9132ee6bd264e69a49c6203aa89877ed3a5a5d633bfa002384881be452cc3a2d2fbcce0b#npm:6.4.6":
  locations:
    - "node_modules/tinyglobby/node_modules/fdir"

"figures@npm:3.2.0":
  locations:
    - "node_modules/figures"

"file-entry-cache@npm:10.1.1":
  locations:
    - "node_modules/stylelint/node_modules/file-entry-cache"

"file-entry-cache@npm:6.0.1":
  locations:
    - "node_modules/file-entry-cache"

"file-type@npm:3.9.0":
  locations:
    - "node_modules/decompress-unzip/node_modules/file-type"

"file-type@npm:4.4.0":
  locations:
    - "node_modules/archive-type/node_modules/file-type"

"file-type@npm:5.2.0":
  locations:
    - "node_modules/file-type"

"file-type@npm:6.2.0":
  locations:
    - "node_modules/decompress-tarbz2/node_modules/file-type"

"file-type@npm:8.1.0":
  locations:
    - "node_modules/download/node_modules/file-type"

"filename-reserved-regex@npm:2.0.0":
  locations:
    - "node_modules/filename-reserved-regex"

"filenamify@npm:2.1.0":
  locations:
    - "node_modules/filenamify"

"fill-range@npm:7.1.1":
  locations:
    - "node_modules/fill-range"

"filter-obj@npm:5.1.0":
  locations:
    - "node_modules/filter-obj"

"finalhandler@npm:1.3.1":
  locations:
    - "node_modules/finalhandler"

"find-cache-dir@npm:2.1.0":
  locations:
    - "node_modules/find-cache-dir"

"find-up@npm:3.0.0":
  locations:
    - "node_modules/pkg-dir/node_modules/find-up"

"find-up@npm:5.0.0":
  locations:
    - "node_modules/find-up"

"find-up@npm:7.0.0":
  locations:
    - "node_modules/@commitlint/top-level/node_modules/find-up"

"find-yarn-workspace-root@npm:2.0.0":
  locations:
    - "node_modules/find-yarn-workspace-root"

"flat-cache@npm:3.2.0":
  locations:
    - "node_modules/flat-cache"

"flat-cache@npm:6.1.11":
  locations:
    - "node_modules/stylelint/node_modules/flat-cache"

"flat@npm:5.0.2":
  locations:
    - "node_modules/flat"

"flatted@npm:3.3.3":
  locations:
    - "node_modules/flatted"

"follow-redirects@virtual:********************************************************************************************************************************#npm:1.15.9":
  locations:
    - "node_modules/follow-redirects"

"for-each@npm:0.3.5":
  locations:
    - "node_modules/for-each"

"foreground-child@npm:3.3.1":
  locations:
    - "node_modules/foreground-child"

"form-data@npm:4.0.4":
  locations:
    - "node_modules/form-data"

"forwarded@npm:0.2.0":
  locations:
    - "node_modules/forwarded"

"fraction.js@npm:4.3.7":
  locations:
    - "node_modules/fraction.js"

"fresh@npm:0.5.2":
  locations:
    - "node_modules/fresh"

"from2@npm:2.3.0":
  locations:
    - "node_modules/from2"

"fs-constants@npm:1.0.0":
  locations:
    - "node_modules/fs-constants"

"fs-extra@npm:11.3.0":
  locations:
    - "node_modules/fs-extra"

"fs-extra@npm:8.1.0":
  locations:
    - "node_modules/scss-bundle/node_modules/fs-extra"

"fs-minipass@npm:3.0.3":
  locations:
    - "node_modules/fs-minipass"

"fs-monkey@npm:1.1.0":
  locations:
    - "node_modules/fs-monkey"

"fs.realpath@npm:1.0.0":
  locations:
    - "node_modules/fs.realpath"

"fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1":
  locations:
    - "node_modules/fsevents"

"function-bind@npm:1.1.2":
  locations:
    - "node_modules/function-bind"

"function.prototype.name@npm:1.1.8":
  locations:
    - "node_modules/function.prototype.name"

"functions-have-names@npm:1.2.3":
  locations:
    - "node_modules/functions-have-names"

"gensync@npm:1.0.0-beta.2":
  locations:
    - "node_modules/gensync"

"get-caller-file@npm:2.0.5":
  locations:
    - "node_modules/get-caller-file"

"get-east-asian-width@npm:1.3.0":
  locations:
    - "node_modules/get-east-asian-width"

"get-intrinsic@npm:1.3.0":
  locations:
    - "node_modules/get-intrinsic"

"get-proto@npm:1.0.1":
  locations:
    - "node_modules/get-proto"

"get-proxy@npm:2.1.0":
  locations:
    - "node_modules/get-proxy"

"get-stream@npm:2.3.1":
  locations:
    - "node_modules/decompress-unzip/node_modules/get-stream"

"get-stream@npm:3.0.0":
  locations:
    - "node_modules/get-stream"

"get-stream@npm:4.1.0":
  locations:
    - "node_modules/package-json/node_modules/got/node_modules/get-stream"

"get-stream@npm:5.2.0":
  locations:
    - "node_modules/package-json/node_modules/get-stream"

"get-stream@npm:6.0.1":
  locations:
    - "node_modules/execa/node_modules/get-stream"

"get-symbol-description@npm:1.1.0":
  locations:
    - "node_modules/get-symbol-description"

"get-tsconfig@npm:4.10.1":
  locations:
    - "node_modules/get-tsconfig"

"git-clone@npm:0.1.0":
  locations:
    - "node_modules/git-clone"

"git-raw-commits@npm:4.0.0":
  locations:
    - "node_modules/git-raw-commits"

"glob-parent@npm:5.1.2":
  locations:
    - "node_modules/fast-glob/node_modules/glob-parent"
    - "node_modules/chokidar/node_modules/glob-parent"

"glob-parent@npm:6.0.2":
  locations:
    - "node_modules/glob-parent"

"glob-to-regexp@npm:0.4.1":
  locations:
    - "node_modules/glob-to-regexp"

"glob@npm:10.2.6":
  locations:
    - "node_modules/@tarojs/plugin-doctor/node_modules/glob"

"glob@npm:10.4.5":
  locations:
    - "node_modules/cacache/node_modules/glob"

"glob@npm:7.2.3":
  locations:
    - "node_modules/glob"

"global-directory@npm:4.0.1":
  locations:
    - "node_modules/global-directory"

"global-modules@npm:2.0.0":
  locations:
    - "node_modules/global-modules"

"global-prefix@npm:3.0.0":
  locations:
    - "node_modules/global-prefix"

"globals@npm:11.12.0":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/globals"

"globals@npm:13.24.0":
  locations:
    - "node_modules/globals"

"globalthis@npm:1.0.4":
  locations:
    - "node_modules/globalthis"

"globby@npm:11.1.0":
  locations:
    - "node_modules/globby"

"globby@npm:14.1.0":
  locations:
    - "node_modules/copy-webpack-plugin/node_modules/globby"

"globjoin@npm:0.1.4":
  locations:
    - "node_modules/globjoin"

"globs@npm:0.1.4":
  locations:
    - "node_modules/globs"

"gopd@npm:1.2.0":
  locations:
    - "node_modules/gopd"

"got@npm:8.3.2":
  locations:
    - "node_modules/got"

"got@npm:9.6.0":
  locations:
    - "node_modules/package-json/node_modules/got"

"graceful-fs@npm:4.2.11":
  locations:
    - "node_modules/graceful-fs"

"graphemer@npm:1.4.0":
  locations:
    - "node_modules/graphemer"

"hammerjs@npm:2.0.8":
  locations:
    - "node_modules/hammerjs"

"handle-thing@npm:2.0.1":
  locations:
    - "node_modules/handle-thing"

"has-bigints@npm:1.1.0":
  locations:
    - "node_modules/has-bigints"

"has-flag@npm:4.0.0":
  locations:
    - "node_modules/has-flag"

"has-property-descriptors@npm:1.0.2":
  locations:
    - "node_modules/has-property-descriptors"

"has-proto@npm:1.2.0":
  locations:
    - "node_modules/has-proto"

"has-symbol-support-x@npm:1.4.2":
  locations:
    - "node_modules/has-symbol-support-x"

"has-symbols@npm:1.1.0":
  locations:
    - "node_modules/has-symbols"

"has-to-string-tag-x@npm:1.4.1":
  locations:
    - "node_modules/has-to-string-tag-x"

"has-tostringtag@npm:1.0.2":
  locations:
    - "node_modules/has-tostringtag"

"hasown@npm:2.0.2":
  locations:
    - "node_modules/hasown"

"he@npm:1.2.0":
  locations:
    - "node_modules/he"

"header-case@npm:2.0.4":
  locations:
    - "node_modules/header-case"

"highlight.js@npm:10.7.3":
  locations:
    - "node_modules/highlight.js"

"history@npm:5.3.0":
  locations:
    - "node_modules/history"

"hls.js@npm:1.6.7":
  locations:
    - "node_modules/hls.js"

"hookified@npm:1.10.0":
  locations:
    - "node_modules/hookified"

"hpack.js@npm:2.1.6":
  locations:
    - "node_modules/hpack.js"

"html-encoding-sniffer@npm:4.0.0":
  locations:
    - "node_modules/html-encoding-sniffer"

"html-entities@npm:2.3.3":
  locations:
    - "node_modules/babel-plugin-transform-solid-jsx/node_modules/html-entities"

"html-entities@npm:2.6.0":
  locations:
    - "node_modules/html-entities"

"html-minifier-terser@npm:6.1.0":
  locations:
    - "node_modules/html-minifier-terser"

"html-minifier@npm:4.0.0":
  locations:
    - "node_modules/html-minifier"

"html-tags@npm:3.3.1":
  locations:
    - "node_modules/html-tags"

"html-webpack-plugin@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:5.6.3":
  locations:
    - "node_modules/html-webpack-plugin"

"htmlparser2@npm:6.1.0":
  locations:
    - "node_modules/htmlparser2"

"http-cache-semantics@npm:3.8.1":
  locations:
    - "node_modules/cacheable-request/node_modules/http-cache-semantics"

"http-cache-semantics@npm:4.2.0":
  locations:
    - "node_modules/http-cache-semantics"

"http-deceiver@npm:1.2.7":
  locations:
    - "node_modules/http-deceiver"

"http-errors@npm:1.6.3":
  locations:
    - "node_modules/serve-index/node_modules/http-errors"

"http-errors@npm:2.0.0":
  locations:
    - "node_modules/http-errors"

"http-parser-js@npm:0.5.10":
  locations:
    - "node_modules/http-parser-js"

"http-proxy-agent@npm:7.0.2":
  locations:
    - "node_modules/http-proxy-agent"

"http-proxy-middleware@virtual:9b1d99f03c1f3a504b44484c029d64228ed613d8bb927935b853e3aa6475a01f928d7959e9613a6a102a2c9d6ca62f660a37d2d1ff392e10945a8b17c7e25a00#npm:2.0.9":
  locations:
    - "node_modules/http-proxy-middleware"

"http-proxy@npm:1.18.1":
  locations:
    - "node_modules/http-proxy"

"https-proxy-agent@npm:7.0.6":
  locations:
    - "node_modules/https-proxy-agent"

"human-signals@npm:2.1.0":
  locations:
    - "node_modules/human-signals"

"husky@npm:9.1.7":
  locations:
    - "node_modules/husky"

"iconv-lite@npm:0.4.24":
  locations:
    - "node_modules/iconv-lite"

"iconv-lite@npm:0.6.3":
  locations:
    - "node_modules/whatwg-encoding/node_modules/iconv-lite"
    - "node_modules/encoding/node_modules/iconv-lite"

"ics@npm:3.8.1":
  locations:
    - "node_modules/ics"

"icss-utils@virtual:7d7dfaab0f45406fdf72a2cb29030b4667f255449af1b22e9fe02f64e9a04e11ae0d3be96b2ff43ce2ee2b9ff00d8f286ebcb06adebcce45c6d697d06abad499#npm:5.1.0":
  locations:
    - "node_modules/icss-utils"

"ieee754@npm:1.2.1":
  locations:
    - "node_modules/ieee754"

"ignore@npm:5.3.2":
  locations:
    - "node_modules/ignore"

"ignore@npm:7.0.5":
  locations:
    - "node_modules/stylelint/node_modules/ignore"
    - "node_modules/copy-webpack-plugin/node_modules/ignore"

"image-size@npm:0.5.5":
  locations:
    - "node_modules/image-size"

"immutable@npm:5.1.3":
  locations:
    - "node_modules/immutable"

"import-fresh@npm:3.3.1":
  locations:
    - "node_modules/import-fresh"

"import-meta-resolve@npm:4.1.0":
  locations:
    - "node_modules/import-meta-resolve"

"imurmurhash@npm:0.1.4":
  locations:
    - "node_modules/imurmurhash"

"inflight@npm:1.0.6":
  locations:
    - "node_modules/inflight"

"inherits@npm:2.0.3":
  locations:
    - "node_modules/serve-index/node_modules/inherits"

"inherits@npm:2.0.4":
  locations:
    - "node_modules/inherits"

"ini@npm:1.3.8":
  locations:
    - "node_modules/ini"

"ini@npm:4.1.1":
  locations:
    - "node_modules/global-directory/node_modules/ini"

"inquirer@npm:8.2.6":
  locations:
    - "node_modules/inquirer"

"internal-slot@npm:1.1.0":
  locations:
    - "node_modules/internal-slot"

"into-stream@npm:3.1.0":
  locations:
    - "node_modules/into-stream"

"ip-address@npm:9.0.5":
  locations:
    - "node_modules/ip-address"

"ipaddr.js@npm:1.9.1":
  locations:
    - "node_modules/ipaddr.js"

"ipaddr.js@npm:2.2.0":
  locations:
    - "node_modules/webpack-dev-server/node_modules/ipaddr.js"

"is-array-buffer@npm:3.0.5":
  locations:
    - "node_modules/is-array-buffer"

"is-arrayish@npm:0.2.1":
  locations:
    - "node_modules/is-arrayish"

"is-async-function@npm:2.1.1":
  locations:
    - "node_modules/is-async-function"

"is-bigint@npm:1.1.0":
  locations:
    - "node_modules/is-bigint"

"is-binary-path@npm:2.1.0":
  locations:
    - "node_modules/is-binary-path"

"is-boolean-object@npm:1.2.2":
  locations:
    - "node_modules/is-boolean-object"

"is-buffer@npm:1.1.6":
  locations:
    - "node_modules/is-buffer"

"is-callable@npm:1.2.7":
  locations:
    - "node_modules/is-callable"

"is-core-module@npm:2.16.1":
  locations:
    - "node_modules/is-core-module"

"is-data-view@npm:1.0.2":
  locations:
    - "node_modules/is-data-view"

"is-date-object@npm:1.1.0":
  locations:
    - "node_modules/is-date-object"

"is-docker@npm:2.2.1":
  locations:
    - "node_modules/is-docker"

"is-extglob@npm:2.1.1":
  locations:
    - "node_modules/is-extglob"

"is-finalizationregistry@npm:1.1.1":
  locations:
    - "node_modules/is-finalizationregistry"

"is-fullwidth-code-point@npm:3.0.0":
  locations:
    - "node_modules/is-fullwidth-code-point"

"is-fullwidth-code-point@npm:4.0.0":
  locations:
    - "node_modules/slice-ansi/node_modules/is-fullwidth-code-point"

"is-fullwidth-code-point@npm:5.0.0":
  locations:
    - "node_modules/log-update/node_modules/is-fullwidth-code-point"

"is-generator-function@npm:1.1.0":
  locations:
    - "node_modules/is-generator-function"

"is-glob@npm:4.0.3":
  locations:
    - "node_modules/is-glob"

"is-interactive@npm:1.0.0":
  locations:
    - "node_modules/is-interactive"

"is-invalid-path@npm:1.0.2":
  locations:
    - "node_modules/is-invalid-path"

"is-map@npm:2.0.3":
  locations:
    - "node_modules/is-map"

"is-mobile@npm:4.0.0":
  locations:
    - "node_modules/is-mobile"

"is-natural-number@npm:4.0.1":
  locations:
    - "node_modules/is-natural-number"

"is-negative-zero@npm:2.0.3":
  locations:
    - "node_modules/is-negative-zero"

"is-number-object@npm:1.1.1":
  locations:
    - "node_modules/is-number-object"

"is-number@npm:7.0.0":
  locations:
    - "node_modules/is-number"

"is-obj@npm:2.0.0":
  locations:
    - "node_modules/is-obj"

"is-object@npm:1.0.2":
  locations:
    - "node_modules/is-object"

"is-path-inside@npm:3.0.3":
  locations:
    - "node_modules/is-path-inside"

"is-plain-obj@npm:1.1.0":
  locations:
    - "node_modules/is-plain-obj"

"is-plain-obj@npm:3.0.0":
  locations:
    - "node_modules/http-proxy-middleware/node_modules/is-plain-obj"

"is-plain-object@npm:2.0.4":
  locations:
    - "node_modules/is-plain-object"

"is-plain-object@npm:5.0.0":
  locations:
    - "node_modules/stylelint/node_modules/is-plain-object"

"is-potential-custom-element-name@npm:1.0.1":
  locations:
    - "node_modules/is-potential-custom-element-name"

"is-regex@npm:1.2.1":
  locations:
    - "node_modules/is-regex"

"is-retry-allowed@npm:1.2.0":
  locations:
    - "node_modules/is-retry-allowed"

"is-set@npm:2.0.3":
  locations:
    - "node_modules/is-set"

"is-shared-array-buffer@npm:1.0.4":
  locations:
    - "node_modules/is-shared-array-buffer"

"is-stream@npm:1.1.0":
  locations:
    - "node_modules/is-stream"

"is-stream@npm:2.0.1":
  locations:
    - "node_modules/execa/node_modules/is-stream"

"is-string@npm:1.1.1":
  locations:
    - "node_modules/is-string"

"is-symbol@npm:1.1.1":
  locations:
    - "node_modules/is-symbol"

"is-text-path@npm:2.0.0":
  locations:
    - "node_modules/is-text-path"

"is-typed-array@npm:1.1.15":
  locations:
    - "node_modules/is-typed-array"

"is-unicode-supported@npm:0.1.0":
  locations:
    - "node_modules/is-unicode-supported"

"is-weakmap@npm:2.0.2":
  locations:
    - "node_modules/is-weakmap"

"is-weakref@npm:1.1.1":
  locations:
    - "node_modules/is-weakref"

"is-weakset@npm:2.0.4":
  locations:
    - "node_modules/is-weakset"

"is-what@npm:3.14.1":
  locations:
    - "node_modules/is-what"

"is-wsl@npm:2.2.0":
  locations:
    - "node_modules/is-wsl"

"isarray@npm:1.0.0":
  locations:
    - "node_modules/readable-stream/node_modules/isarray"

"isarray@npm:2.0.5":
  locations:
    - "node_modules/isarray"

"isexe@npm:2.0.0":
  locations:
    - "node_modules/isexe"

"isexe@npm:3.1.1":
  locations:
    - "node_modules/node-gyp/node_modules/isexe"

"isobject@npm:3.0.1":
  locations:
    - "node_modules/isobject"

"isurl@npm:1.0.0":
  locations:
    - "node_modules/isurl"

"iterator.prototype@npm:1.1.5":
  locations:
    - "node_modules/iterator.prototype"

"j-component@npm:1.4.9":
  locations:
    - "node_modules/j-component"

"jackspeak@npm:2.3.6":
  locations:
    - "node_modules/@tarojs/plugin-doctor/node_modules/jackspeak"

"jackspeak@npm:3.4.3":
  locations:
    - "node_modules/jackspeak"

"javascript-stringify@npm:2.1.0":
  locations:
    - "node_modules/javascript-stringify"

"jest-util@npm:29.7.0":
  locations:
    - "node_modules/jest-util"

"jest-worker@npm:27.5.1":
  locations:
    - "node_modules/terser-webpack-plugin/node_modules/jest-worker"

"jest-worker@npm:29.7.0":
  locations:
    - "node_modules/jest-worker"

"jiti@npm:1.21.7":
  locations:
    - "node_modules/postcss-loader/node_modules/jiti"

"jiti@npm:2.4.2":
  locations:
    - "node_modules/jiti"

"joi@npm:17.13.3":
  locations:
    - "node_modules/joi"

"js-tokens@npm:4.0.0":
  locations:
    - "node_modules/js-tokens"

"js-yaml@npm:4.1.0":
  locations:
    - "node_modules/js-yaml"

"jsbn@npm:1.1.0":
  locations:
    - "node_modules/jsbn"

"jsdom@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:24.1.3":
  locations:
    - "node_modules/jsdom"

"jsesc@npm:2.5.2":
  locations:
    - "node_modules/@tarojs/plugin-generator/node_modules/@babel/generator/node_modules/jsesc"

"jsesc@npm:3.0.2":
  locations:
    - "node_modules/regjsparser/node_modules/jsesc"

"jsesc@npm:3.1.0":
  locations:
    - "node_modules/jsesc"

"json-buffer@npm:3.0.0":
  locations:
    - "node_modules/json-buffer"

"json-buffer@npm:3.0.1":
  locations:
    - "node_modules/keyv/node_modules/json-buffer"

"json-parse-even-better-errors@npm:2.3.1":
  locations:
    - "node_modules/json-parse-even-better-errors"

"json-schema-traverse@npm:0.4.1":
  locations:
    - "node_modules/ajv/node_modules/json-schema-traverse"

"json-schema-traverse@npm:1.0.0":
  locations:
    - "node_modules/json-schema-traverse"

"json-stable-stringify-without-jsonify@npm:1.0.1":
  locations:
    - "node_modules/json-stable-stringify-without-jsonify"

"json5@npm:1.0.2":
  locations:
    - "node_modules/tsconfig-paths/node_modules/json5"
    - "node_modules/babel-loader/node_modules/json5"

"json5@npm:2.2.3":
  locations:
    - "node_modules/json5"

"jsonfile@npm:4.0.0":
  locations:
    - "node_modules/scss-bundle/node_modules/jsonfile"

"jsonfile@npm:6.1.0":
  locations:
    - "node_modules/jsonfile"

"jsonp-retry@npm:1.0.3":
  locations:
    - "node_modules/jsonp-retry"

"jsonparse@npm:1.3.1":
  locations:
    - "node_modules/jsonparse"

"jsx-ast-utils@npm:3.3.5":
  locations:
    - "node_modules/jsx-ast-utils"

"keyv@npm:3.0.0":
  locations:
    - "node_modules/cacheable-request/node_modules/keyv"

"keyv@npm:3.1.0":
  locations:
    - "node_modules/package-json/node_modules/keyv"

"keyv@npm:4.5.4":
  locations:
    - "node_modules/keyv"

"keyv@npm:5.4.0":
  locations:
    - "node_modules/cacheable/node_modules/keyv"

"kind-of@npm:6.0.3":
  locations:
    - "node_modules/kind-of"

"kleur@npm:4.1.5":
  locations:
    - "node_modules/kleur"

"known-css-properties@npm:0.37.0":
  locations:
    - "node_modules/known-css-properties"

"latest-version@npm:5.1.0":
  locations:
    - "node_modules/latest-version"

"launch-editor@npm:2.10.0":
  locations:
    - "node_modules/launch-editor"

"less-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:12.3.0":
  locations:
    - "node_modules/less-loader"

"less@npm:3.13.1":
  locations:
    - "node_modules/less"

"levn@npm:0.4.1":
  locations:
    - "node_modules/levn"

"lightningcss-darwin-arm64@npm:1.30.1":
  locations:
    - "node_modules/lightningcss-darwin-arm64"

"lightningcss@npm:1.30.1":
  locations:
    - "node_modules/lightningcss"

"lilconfig@npm:3.1.3":
  locations:
    - "node_modules/lilconfig"

"lines-and-columns@npm:1.2.4":
  locations:
    - "node_modules/lines-and-columns"

"lint-staged@npm:16.1.2":
  locations:
    - "node_modules/lint-staged"

"listr2@npm:8.3.3":
  locations:
    - "node_modules/listr2"

"loader-runner@npm:4.3.0":
  locations:
    - "node_modules/loader-runner"

"loader-utils@npm:1.4.2":
  locations:
    - "node_modules/babel-loader/node_modules/loader-utils"

"loader-utils@npm:2.0.4":
  locations:
    - "node_modules/loader-utils"

"loader-utils@npm:3.3.1":
  locations:
    - "node_modules/@tarojs/webpack5-runner/node_modules/loader-utils"

"locate-path@npm:3.0.0":
  locations:
    - "node_modules/pkg-dir/node_modules/locate-path"

"locate-path@npm:6.0.0":
  locations:
    - "node_modules/find-up/node_modules/locate-path"

"locate-path@npm:7.2.0":
  locations:
    - "node_modules/locate-path"

"lodash-es@npm:4.17.21":
  locations:
    - "node_modules/lodash-es"

"lodash.camelcase@npm:4.3.0":
  locations:
    - "node_modules/lodash.camelcase"

"lodash.clonedeep@npm:4.5.0":
  locations:
    - "node_modules/lodash.clonedeep"

"lodash.debounce@npm:4.0.8":
  locations:
    - "node_modules/lodash.debounce"

"lodash.isplainobject@npm:4.0.6":
  locations:
    - "node_modules/lodash.isplainobject"

"lodash.kebabcase@npm:4.1.1":
  locations:
    - "node_modules/lodash.kebabcase"

"lodash.memoize@npm:4.1.2":
  locations:
    - "node_modules/lodash.memoize"

"lodash.merge@npm:4.6.2":
  locations:
    - "node_modules/lodash.merge"

"lodash.mergewith@npm:4.6.2":
  locations:
    - "node_modules/lodash.mergewith"

"lodash.snakecase@npm:4.1.1":
  locations:
    - "node_modules/lodash.snakecase"

"lodash.startcase@npm:4.4.0":
  locations:
    - "node_modules/lodash.startcase"

"lodash.truncate@npm:4.4.2":
  locations:
    - "node_modules/lodash.truncate"

"lodash.uniq@npm:4.5.0":
  locations:
    - "node_modules/lodash.uniq"

"lodash.upperfirst@npm:4.3.1":
  locations:
    - "node_modules/lodash.upperfirst"

"lodash@npm:4.17.21":
  locations:
    - "node_modules/lodash"

"log-symbols@npm:4.1.0":
  locations:
    - "node_modules/log-symbols"

"log-update@npm:6.1.0":
  locations:
    - "node_modules/log-update"

"loglevel-plugin-prefix@npm:0.8.4":
  locations:
    - "node_modules/loglevel-plugin-prefix"

"loglevel@npm:1.9.2":
  locations:
    - "node_modules/loglevel"

"loose-envify@npm:1.4.0":
  locations:
    - "node_modules/loose-envify"

"lower-case@npm:1.1.4":
  locations:
    - "node_modules/html-minifier/node_modules/lower-case"

"lower-case@npm:2.0.2":
  locations:
    - "node_modules/lower-case"

"lowercase-keys@npm:1.0.0":
  locations:
    - "node_modules/cacheable-request/node_modules/lowercase-keys"

"lowercase-keys@npm:1.0.1":
  locations:
    - "node_modules/lowercase-keys"

"lowercase-keys@npm:2.0.0":
  locations:
    - "node_modules/package-json/node_modules/cacheable-request/node_modules/lowercase-keys"

"lru-cache@npm:10.4.3":
  locations:
    - "node_modules/lru-cache"

"lru-cache@npm:5.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache"

"make-dir@npm:1.3.0":
  locations:
    - "node_modules/download/node_modules/make-dir"
    - "node_modules/decompress/node_modules/make-dir"

"make-dir@npm:2.1.0":
  locations:
    - "node_modules/make-dir"

"make-dir@npm:3.1.0":
  locations:
    - "node_modules/postcss-url/node_modules/make-dir"

"make-fetch-happen@npm:14.0.3":
  locations:
    - "node_modules/make-fetch-happen"

"math-intrinsics@npm:1.1.0":
  locations:
    - "node_modules/math-intrinsics"

"mathml-tag-names@npm:2.1.3":
  locations:
    - "node_modules/mathml-tag-names"

"md5@npm:2.3.0":
  locations:
    - "node_modules/md5"

"mdn-data@npm:1.1.4":
  locations:
    - "node_modules/miniprogram-simulate/node_modules/mdn-data"

"mdn-data@npm:2.0.28":
  locations:
    - "node_modules/csso/node_modules/mdn-data"

"mdn-data@npm:2.0.30":
  locations:
    - "node_modules/mdn-data"

"mdn-data@npm:2.12.2":
  locations:
    - "node_modules/stylelint/node_modules/mdn-data"

"media-typer@npm:0.3.0":
  locations:
    - "node_modules/media-typer"

"memfs@npm:3.5.3":
  locations:
    - "node_modules/memfs"

"meow@npm:12.1.1":
  locations:
    - "node_modules/meow"

"meow@npm:13.2.0":
  locations:
    - "node_modules/stylelint/node_modules/meow"

"merge-descriptors@npm:1.0.3":
  locations:
    - "node_modules/merge-descriptors"

"merge-stream@npm:2.0.0":
  locations:
    - "node_modules/merge-stream"

"merge2@npm:1.4.1":
  locations:
    - "node_modules/merge2"

"methods@npm:1.1.2":
  locations:
    - "node_modules/methods"

"micromatch@npm:4.0.8":
  locations:
    - "node_modules/micromatch"

"mime-db@npm:1.52.0":
  locations:
    - "node_modules/mime-types/node_modules/mime-db"

"mime-db@npm:1.54.0":
  locations:
    - "node_modules/mime-db"

"mime-types@npm:2.1.35":
  locations:
    - "node_modules/mime-types"

"mime@npm:1.6.0":
  locations:
    - "node_modules/mime"

"mime@npm:2.5.2":
  locations:
    - "node_modules/postcss-url/node_modules/mime"

"mimic-fn@npm:2.1.0":
  locations:
    - "node_modules/mimic-fn"

"mimic-function@npm:5.0.1":
  locations:
    - "node_modules/mimic-function"

"mimic-response@npm:1.0.1":
  locations:
    - "node_modules/mimic-response"

"mini-css-extract-plugin@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:2.9.2":
  locations:
    - "node_modules/mini-css-extract-plugin"

"minimalistic-assert@npm:1.0.1":
  locations:
    - "node_modules/minimalistic-assert"

"minimatch@npm:10.0.3":
  locations:
    - "node_modules/@ts-morph/common/node_modules/minimatch"

"minimatch@npm:3.0.8":
  locations:
    - "node_modules/postcss-url/node_modules/minimatch"

"minimatch@npm:3.1.2":
  locations:
    - "node_modules/minimatch"

"minimatch@npm:9.0.3":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch"

"minimatch@npm:9.0.5":
  locations:
    - "node_modules/cacache/node_modules/minimatch"
    - "node_modules/@tarojs/plugin-doctor/node_modules/glob/node_modules/minimatch"

"minimist@npm:1.2.8":
  locations:
    - "node_modules/minimist"

"minipass-collect@npm:2.0.1":
  locations:
    - "node_modules/minipass-collect"

"minipass-fetch@npm:4.0.1":
  locations:
    - "node_modules/minipass-fetch"

"minipass-flush@npm:1.0.5":
  locations:
    - "node_modules/minipass-flush"

"minipass-pipeline@npm:1.2.4":
  locations:
    - "node_modules/minipass-pipeline"

"minipass-sized@npm:1.0.3":
  locations:
    - "node_modules/minipass-sized"

"minipass@npm:3.3.6":
  locations:
    - "node_modules/minipass-sized/node_modules/minipass"
    - "node_modules/minipass-pipeline/node_modules/minipass"
    - "node_modules/minipass-flush/node_modules/minipass"

"minipass@npm:6.0.2":
  locations:
    - "node_modules/@tarojs/plugin-doctor/node_modules/minipass"

"minipass@npm:7.1.2":
  locations:
    - "node_modules/minipass"

"miniprogram-api-typings@npm:3.12.3":
  locations:
    - "node_modules/miniprogram-api-typings"

"miniprogram-compiler@npm:0.2.3":
  locations:
    - "node_modules/miniprogram-compiler"

"miniprogram-exparser@npm:2.29.1":
  locations:
    - "node_modules/miniprogram-exparser"

"miniprogram-simulate@npm:1.6.1":
  locations:
    - "node_modules/miniprogram-simulate"

"minizlib@npm:3.0.2":
  locations:
    - "node_modules/minizlib"

"mkdirp@npm:3.0.1":
  locations:
    - "node_modules/mkdirp"

"mobile-detect@npm:1.4.5":
  locations:
    - "node_modules/mobile-detect"

"ms@npm:2.0.0":
  locations:
    - "node_modules/serve-index/node_modules/ms"
    - "node_modules/send/node_modules/debug/node_modules/ms"
    - "node_modules/finalhandler/node_modules/ms"
    - "node_modules/express/node_modules/ms"
    - "node_modules/compression/node_modules/ms"
    - "node_modules/body-parser/node_modules/ms"

"ms@npm:2.1.3":
  locations:
    - "node_modules/ms"

"multicast-dns@npm:7.2.5":
  locations:
    - "node_modules/multicast-dns"

"mute-stream@npm:0.0.8":
  locations:
    - "node_modules/mute-stream"

"mz@npm:2.7.0":
  locations:
    - "node_modules/mz"

"nano-spawn@npm:1.0.2":
  locations:
    - "node_modules/nano-spawn"

"nanoid@npm:3.3.11":
  locations:
    - "node_modules/nanoid"

"native-request@npm:1.1.2":
  locations:
    - "node_modules/native-request"

"natural-compare@npm:1.4.0":
  locations:
    - "node_modules/natural-compare"

"negotiator@npm:0.6.3":
  locations:
    - "node_modules/accepts/node_modules/negotiator"

"negotiator@npm:0.6.4":
  locations:
    - "node_modules/compression/node_modules/negotiator"

"negotiator@npm:1.0.0":
  locations:
    - "node_modules/negotiator"

"neo-async@npm:2.6.2":
  locations:
    - "node_modules/neo-async"

"no-case@npm:2.3.2":
  locations:
    - "node_modules/html-minifier/node_modules/no-case"

"no-case@npm:3.0.4":
  locations:
    - "node_modules/no-case"

"node-addon-api@npm:7.1.1":
  locations:
    - "node_modules/node-addon-api"

"node-forge@npm:1.3.1":
  locations:
    - "node_modules/node-forge"

"node-gyp@npm:11.2.0":
  locations:
    - "node_modules/node-gyp"

"node-releases@npm:2.0.19":
  locations:
    - "node_modules/node-releases"

"nopt@npm:8.1.0":
  locations:
    - "node_modules/nopt"

"normalize-path@npm:3.0.0":
  locations:
    - "node_modules/normalize-path"

"normalize-range@npm:0.1.2":
  locations:
    - "node_modules/normalize-range"

"normalize-url@npm:2.0.1":
  locations:
    - "node_modules/normalize-url"

"normalize-url@npm:4.5.1":
  locations:
    - "node_modules/package-json/node_modules/normalize-url"

"npm-conf@npm:1.1.3":
  locations:
    - "node_modules/npm-conf"

"npm-run-path@npm:4.0.1":
  locations:
    - "node_modules/npm-run-path"

"nth-check@npm:2.1.1":
  locations:
    - "node_modules/nth-check"

"nwsapi@npm:2.2.20":
  locations:
    - "node_modules/nwsapi"

"object-assign@npm:4.1.1":
  locations:
    - "node_modules/object-assign"

"object-inspect@npm:1.13.4":
  locations:
    - "node_modules/object-inspect"

"object-keys@npm:1.1.1":
  locations:
    - "node_modules/object-keys"

"object.assign@npm:4.1.7":
  locations:
    - "node_modules/object.assign"

"object.entries@npm:1.1.9":
  locations:
    - "node_modules/object.entries"

"object.fromentries@npm:2.0.8":
  locations:
    - "node_modules/object.fromentries"

"object.groupby@npm:1.0.3":
  locations:
    - "node_modules/object.groupby"

"object.values@npm:1.2.1":
  locations:
    - "node_modules/object.values"

"obuf@npm:1.1.2":
  locations:
    - "node_modules/obuf"

"on-finished@npm:2.4.1":
  locations:
    - "node_modules/on-finished"

"on-headers@npm:1.1.0":
  locations:
    - "node_modules/on-headers"

"once@npm:1.4.0":
  locations:
    - "node_modules/once"

"onetime@npm:5.1.2":
  locations:
    - "node_modules/onetime"

"onetime@npm:7.0.0":
  locations:
    - "node_modules/log-update/node_modules/onetime"

"open@npm:8.4.2":
  locations:
    - "node_modules/open"

"optionator@npm:0.9.4":
  locations:
    - "node_modules/optionator"

"ora@npm:5.4.1":
  locations:
    - "node_modules/ora"

"os-tmpdir@npm:1.0.2":
  locations:
    - "node_modules/os-tmpdir"

"own-keys@npm:1.0.1":
  locations:
    - "node_modules/own-keys"

"p-cancelable@npm:0.4.1":
  locations:
    - "node_modules/p-cancelable"

"p-cancelable@npm:1.1.0":
  locations:
    - "node_modules/package-json/node_modules/p-cancelable"

"p-event@npm:2.3.1":
  locations:
    - "node_modules/p-event"

"p-finally@npm:1.0.0":
  locations:
    - "node_modules/p-finally"

"p-is-promise@npm:1.1.0":
  locations:
    - "node_modules/p-is-promise"

"p-limit@npm:2.3.0":
  locations:
    - "node_modules/pkg-dir/node_modules/p-limit"

"p-limit@npm:3.1.0":
  locations:
    - "node_modules/find-up/node_modules/p-limit"

"p-limit@npm:4.0.0":
  locations:
    - "node_modules/p-limit"

"p-locate@npm:3.0.0":
  locations:
    - "node_modules/pkg-dir/node_modules/p-locate"

"p-locate@npm:5.0.0":
  locations:
    - "node_modules/find-up/node_modules/p-locate"

"p-locate@npm:6.0.0":
  locations:
    - "node_modules/p-locate"

"p-map@npm:7.0.3":
  locations:
    - "node_modules/p-map"

"p-retry@npm:4.6.2":
  locations:
    - "node_modules/p-retry"

"p-timeout@npm:2.0.1":
  locations:
    - "node_modules/p-timeout"

"p-try@npm:2.2.0":
  locations:
    - "node_modules/p-try"

"package-json-from-dist@npm:1.0.1":
  locations:
    - "node_modules/package-json-from-dist"

"package-json@npm:6.5.0":
  locations:
    - "node_modules/package-json"

"param-case@npm:2.1.1":
  locations:
    - "node_modules/html-minifier/node_modules/param-case"

"param-case@npm:3.0.4":
  locations:
    - "node_modules/param-case"

"parent-module@npm:1.0.1":
  locations:
    - "node_modules/parent-module"

"parse-json@npm:5.2.0":
  locations:
    - "node_modules/parse-json"

"parse5-htmlparser2-tree-adapter@npm:6.0.1":
  locations:
    - "node_modules/parse5-htmlparser2-tree-adapter"

"parse5@npm:5.1.1":
  locations:
    - "node_modules/cli-highlight/node_modules/parse5"

"parse5@npm:6.0.1":
  locations:
    - "node_modules/parse5"

"parse5@npm:7.3.0":
  locations:
    - "node_modules/jsdom/node_modules/parse5"

"parseurl@npm:1.3.3":
  locations:
    - "node_modules/parseurl"

"pascal-case@npm:3.1.2":
  locations:
    - "node_modules/pascal-case"

"path-browserify@npm:1.0.1":
  locations:
    - "node_modules/path-browserify"

"path-case@npm:3.0.4":
  locations:
    - "node_modules/path-case"

"path-exists@npm:3.0.0":
  locations:
    - "node_modules/pkg-dir/node_modules/path-exists"

"path-exists@npm:4.0.0":
  locations:
    - "node_modules/find-up/node_modules/path-exists"

"path-exists@npm:5.0.0":
  locations:
    - "node_modules/path-exists"

"path-is-absolute@npm:1.0.1":
  locations:
    - "node_modules/path-is-absolute"

"path-key@npm:3.1.1":
  locations:
    - "node_modules/path-key"

"path-parse@npm:1.0.7":
  locations:
    - "node_modules/path-parse"

"path-scurry@npm:1.11.1":
  locations:
    - "node_modules/path-scurry"

"path-to-regexp@npm:0.1.12":
  locations:
    - "node_modules/express/node_modules/path-to-regexp"

"path-to-regexp@npm:6.3.0":
  locations:
    - "node_modules/path-to-regexp"

"path-type@npm:4.0.0":
  locations:
    - "node_modules/dir-glob/node_modules/path-type"

"path-type@npm:6.0.0":
  locations:
    - "node_modules/path-type"

"pend@npm:1.2.0":
  locations:
    - "node_modules/pend"

"picocolors@npm:0.2.1":
  locations:
    - "node_modules/miniprogram-simulate/node_modules/picocolors"

"picocolors@npm:1.1.1":
  locations:
    - "node_modules/picocolors"

"picomatch@npm:2.3.1":
  locations:
    - "node_modules/picomatch"

"picomatch@npm:4.0.3":
  locations:
    - "node_modules/tinyglobby/node_modules/picomatch"
    - "node_modules/@tarojs/webpack5-runner/node_modules/picomatch"

"pidtree@npm:0.6.0":
  locations:
    - "node_modules/pidtree"

"pify@npm:2.3.0":
  locations:
    - "node_modules/read-cache/node_modules/pify"
    - "node_modules/decompress/node_modules/pify"
    - "node_modules/decompress-unzip/node_modules/pify"

"pify@npm:3.0.0":
  locations:
    - "node_modules/pify"
    - "node_modules/decompress/node_modules/make-dir/node_modules/pify"

"pify@npm:4.0.1":
  locations:
    - "node_modules/make-dir/node_modules/pify"
    - "node_modules/babel-loader/node_modules/pify"

"pinkie-promise@npm:2.0.1":
  locations:
    - "node_modules/pinkie-promise"

"pinkie@npm:2.0.4":
  locations:
    - "node_modules/pinkie"

"pirates@npm:4.0.7":
  locations:
    - "node_modules/pirates"

"pkg-dir@npm:3.0.0":
  locations:
    - "node_modules/pkg-dir"

"platform@npm:1.3.6":
  locations:
    - "node_modules/platform"

"possible-typed-array-names@npm:1.1.0":
  locations:
    - "node_modules/possible-typed-array-names"

"postcss-calc@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:9.0.1":
  locations:
    - "node_modules/postcss-calc"

"postcss-colormin@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.1.0":
  locations:
    - "node_modules/postcss-colormin"

"postcss-convert-values@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.1.0":
  locations:
    - "node_modules/postcss-convert-values"

"postcss-discard-comments@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-discard-comments"

"postcss-discard-duplicates@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.3":
  locations:
    - "node_modules/postcss-discard-duplicates"

"postcss-discard-empty@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.3":
  locations:
    - "node_modules/postcss-discard-empty"

"postcss-discard-overridden@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-discard-overridden"

"postcss-html-transform@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:4.1.4":
  locations:
    - "node_modules/postcss-html-transform"

"postcss-import@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:16.1.1":
  locations:
    - "node_modules/postcss-import"

"postcss-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:8.1.1":
  locations:
    - "node_modules/postcss-loader"

"postcss-merge-longhand@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.5":
  locations:
    - "node_modules/postcss-merge-longhand"

"postcss-merge-rules@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.1.1":
  locations:
    - "node_modules/postcss-merge-rules"

"postcss-minify-font-values@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.1.0":
  locations:
    - "node_modules/postcss-minify-font-values"

"postcss-minify-gradients@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.3":
  locations:
    - "node_modules/postcss-minify-gradients"

"postcss-minify-params@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.1.0":
  locations:
    - "node_modules/postcss-minify-params"

"postcss-minify-selectors@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.4":
  locations:
    - "node_modules/postcss-minify-selectors"

"postcss-modules-extract-imports@virtual:7d7dfaab0f45406fdf72a2cb29030b4667f255449af1b22e9fe02f64e9a04e11ae0d3be96b2ff43ce2ee2b9ff00d8f286ebcb06adebcce45c6d697d06abad499#npm:3.1.0":
  locations:
    - "node_modules/postcss-modules-extract-imports"

"postcss-modules-local-by-default@virtual:7d7dfaab0f45406fdf72a2cb29030b4667f255449af1b22e9fe02f64e9a04e11ae0d3be96b2ff43ce2ee2b9ff00d8f286ebcb06adebcce45c6d697d06abad499#npm:4.2.0":
  locations:
    - "node_modules/postcss-modules-local-by-default"

"postcss-modules-scope@virtual:7d7dfaab0f45406fdf72a2cb29030b4667f255449af1b22e9fe02f64e9a04e11ae0d3be96b2ff43ce2ee2b9ff00d8f286ebcb06adebcce45c6d697d06abad499#npm:3.2.1":
  locations:
    - "node_modules/postcss-modules-scope"

"postcss-modules-values@virtual:7d7dfaab0f45406fdf72a2cb29030b4667f255449af1b22e9fe02f64e9a04e11ae0d3be96b2ff43ce2ee2b9ff00d8f286ebcb06adebcce45c6d697d06abad499#npm:4.0.0":
  locations:
    - "node_modules/postcss-modules-values"

"postcss-normalize-charset@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-charset"

"postcss-normalize-display-values@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-display-values"

"postcss-normalize-positions@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-positions"

"postcss-normalize-repeat-style@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-repeat-style"

"postcss-normalize-string@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-string"

"postcss-normalize-timing-functions@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-timing-functions"

"postcss-normalize-unicode@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.1.0":
  locations:
    - "node_modules/postcss-normalize-unicode"

"postcss-normalize-url@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-url"

"postcss-normalize-whitespace@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-normalize-whitespace"

"postcss-ordered-values@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-ordered-values"

"postcss-plugin-constparse@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:4.1.4":
  locations:
    - "node_modules/postcss-plugin-constparse"

"postcss-pxtransform@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:4.1.4":
  locations:
    - "node_modules/postcss-pxtransform"

"postcss-reduce-initial@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.1.0":
  locations:
    - "node_modules/postcss-reduce-initial"

"postcss-reduce-transforms@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.2":
  locations:
    - "node_modules/postcss-reduce-transforms"

"postcss-resolve-nested-selector@npm:0.1.6":
  locations:
    - "node_modules/postcss-resolve-nested-selector"

"postcss-safe-parser@virtual:e1dfb8a09cde37d074aa7a8d2f6ecd2796b7856d149dea1cd15a4a241c4e9eca60fba7381a4b09c69f3c4d10d83e54139dd2241e17a7687512511520ed208d2e#npm:7.0.1":
  locations:
    - "node_modules/postcss-safe-parser"

"postcss-selector-parser@npm:6.1.2":
  locations:
    - "node_modules/postcss-selector-parser"

"postcss-selector-parser@npm:7.1.0":
  locations:
    - "node_modules/stylelint/node_modules/postcss-selector-parser"
    - "node_modules/postcss-modules-scope/node_modules/postcss-selector-parser"
    - "node_modules/postcss-modules-local-by-default/node_modules/postcss-selector-parser"

"postcss-svgo@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.3":
  locations:
    - "node_modules/postcss-svgo"

"postcss-unique-selectors@virtual:3e1e9718569efdab340136e6b50adf914391a1379514df1c809275891a0376cbac967f5601aa4d737be62872b09963280973993755b23dfe2af9d4f273d7d321#npm:6.0.4":
  locations:
    - "node_modules/postcss-unique-selectors"

"postcss-url@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:10.1.3":
  locations:
    - "node_modules/postcss-url"

"postcss-value-parser@npm:4.2.0":
  locations:
    - "node_modules/postcss-value-parser"

"postcss@npm:7.0.39":
  locations:
    - "node_modules/miniprogram-simulate/node_modules/postcss"

"postcss@npm:8.5.6":
  locations:
    - "node_modules/postcss"

"prelude-ls@npm:1.2.1":
  locations:
    - "node_modules/prelude-ls"

"prepend-http@npm:2.0.0":
  locations:
    - "node_modules/prepend-http"

"pretty-bytes@npm:5.6.0":
  locations:
    - "node_modules/pretty-bytes"

"pretty-error@npm:4.0.0":
  locations:
    - "node_modules/pretty-error"

"pretty-format@npm:26.6.2":
  locations:
    - "node_modules/pretty-format"

"pretty-time@npm:1.1.0":
  locations:
    - "node_modules/pretty-time"

"proc-log@npm:5.0.0":
  locations:
    - "node_modules/proc-log"

"process-nextick-args@npm:2.0.1":
  locations:
    - "node_modules/process-nextick-args"

"promise-polyfill@npm:7.1.2":
  locations:
    - "node_modules/promise-polyfill"

"promise-retry@npm:2.0.1":
  locations:
    - "node_modules/promise-retry"

"prop-types@npm:15.8.1":
  locations:
    - "node_modules/prop-types"

"property-expr@npm:2.0.6":
  locations:
    - "node_modules/property-expr"

"proto-list@npm:1.2.4":
  locations:
    - "node_modules/proto-list"

"proxy-addr@npm:2.0.7":
  locations:
    - "node_modules/proxy-addr"

"proxy-from-env@npm:1.1.0":
  locations:
    - "node_modules/proxy-from-env"

"prr@npm:1.0.1":
  locations:
    - "node_modules/prr"

"psl@npm:1.15.0":
  locations:
    - "node_modules/psl"

"pump@npm:3.0.3":
  locations:
    - "node_modules/pump"

"punycode@npm:2.3.1":
  locations:
    - "node_modules/punycode"

"qs@npm:6.13.0":
  locations:
    - "node_modules/qs"

"query-string@npm:5.1.1":
  locations:
    - "node_modules/normalize-url/node_modules/query-string"

"query-string@npm:9.2.2":
  locations:
    - "node_modules/query-string"

"querystringify@npm:2.2.0":
  locations:
    - "node_modules/querystringify"

"queue-microtask@npm:1.2.3":
  locations:
    - "node_modules/queue-microtask"

"randombytes@npm:2.1.0":
  locations:
    - "node_modules/randombytes"

"range-parser@npm:1.2.1":
  locations:
    - "node_modules/range-parser"

"raw-body@npm:2.5.2":
  locations:
    - "node_modules/raw-body"

"rc@npm:1.2.8":
  locations:
    - "node_modules/rc"

"react-dom@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:18.3.1":
  locations:
    - "node_modules/react-dom"

"react-is@npm:16.13.1":
  locations:
    - "node_modules/prop-types/node_modules/react-is"

"react-is@npm:17.0.2":
  locations:
    - "node_modules/react-is"

"react-reconciler@virtual:494233daea2a49c7070787be58f4c028402ab9b808b66c91c8bf573453dfbb952b1b51aecd48099df6422942a0abdea508bfe12212bef35b15e1ae6c9e5ec49f#npm:0.29.0":
  locations:
    - "node_modules/react-reconciler"

"react-refresh@npm:0.14.2":
  locations:
    - "node_modules/react-refresh"

"react@npm:18.3.1":
  locations:
    - "node_modules/react"

"read-cache@npm:1.0.0":
  locations:
    - "node_modules/read-cache"

"readable-stream@npm:2.3.8":
  locations:
    - "node_modules/readable-stream"

"readable-stream@npm:3.6.2":
  locations:
    - "node_modules/spdy-transport/node_modules/readable-stream"
    - "node_modules/bl/node_modules/readable-stream"

"readdirp@npm:3.6.0":
  locations:
    - "node_modules/readdirp"

"readdirp@npm:4.1.2":
  locations:
    - "node_modules/sass/node_modules/readdirp"

"reflect.getprototypeof@npm:1.0.10":
  locations:
    - "node_modules/reflect.getprototypeof"

"regenerate-unicode-properties@npm:10.2.0":
  locations:
    - "node_modules/regenerate-unicode-properties"

"regenerate@npm:1.4.2":
  locations:
    - "node_modules/regenerate"

"regenerator-runtime@npm:0.11.1":
  locations:
    - "node_modules/regenerator-runtime"

"regex-parser@npm:2.3.1":
  locations:
    - "node_modules/regex-parser"

"regexp.prototype.flags@npm:1.5.4":
  locations:
    - "node_modules/regexp.prototype.flags"

"regexpu-core@npm:6.2.0":
  locations:
    - "node_modules/regexpu-core"

"registry-auth-token@npm:4.2.2":
  locations:
    - "node_modules/registry-auth-token"

"registry-url@npm:5.1.0":
  locations:
    - "node_modules/registry-url"

"regjsgen@npm:0.8.0":
  locations:
    - "node_modules/regjsgen"

"regjsparser@npm:0.12.0":
  locations:
    - "node_modules/regjsparser"

"relateurl@npm:0.2.7":
  locations:
    - "node_modules/relateurl"

"renderkid@npm:3.0.0":
  locations:
    - "node_modules/renderkid"

"require-directory@npm:2.1.1":
  locations:
    - "node_modules/require-directory"

"require-from-string@npm:2.0.2":
  locations:
    - "node_modules/require-from-string"

"requires-port@npm:1.0.0":
  locations:
    - "node_modules/requires-port"

"resolve-from@npm:4.0.0":
  locations:
    - "node_modules/import-fresh/node_modules/resolve-from"

"resolve-from@npm:5.0.0":
  locations:
    - "node_modules/resolve-from"

"resolve-pathname@npm:3.0.0":
  locations:
    - "node_modules/resolve-pathname"

"resolve-pkg-maps@npm:1.0.0":
  locations:
    - "node_modules/resolve-pkg-maps"

"resolve-url-loader@npm:5.0.0":
  locations:
    - "node_modules/resolve-url-loader"

"resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d":
  locations:
    - "node_modules/resolve"

"resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d":
  locations:
    - "node_modules/eslint-plugin-react/node_modules/resolve"

"responselike@npm:1.0.2":
  locations:
    - "node_modules/responselike"

"restore-cursor@npm:3.1.0":
  locations:
    - "node_modules/restore-cursor"

"restore-cursor@npm:5.1.0":
  locations:
    - "node_modules/log-update/node_modules/restore-cursor"

"retry@npm:0.12.0":
  locations:
    - "node_modules/retry"

"retry@npm:0.13.1":
  locations:
    - "node_modules/p-retry/node_modules/retry"

"reusify@npm:1.1.0":
  locations:
    - "node_modules/reusify"

"rfdc@npm:1.4.1":
  locations:
    - "node_modules/rfdc"

"rimraf@npm:3.0.2":
  locations:
    - "node_modules/rimraf"

"rollup@npm:3.29.5":
  locations:
    - "node_modules/rollup"

"rrweb-cssom@npm:0.7.1":
  locations:
    - "node_modules/jsdom/node_modules/rrweb-cssom"

"rrweb-cssom@npm:0.8.0":
  locations:
    - "node_modules/rrweb-cssom"

"run-async@npm:2.4.1":
  locations:
    - "node_modules/run-async"

"run-parallel@npm:1.2.0":
  locations:
    - "node_modules/run-parallel"

"runes2@npm:1.1.4":
  locations:
    - "node_modules/runes2"

"rxjs@npm:7.8.2":
  locations:
    - "node_modules/rxjs"

"safe-array-concat@npm:1.1.3":
  locations:
    - "node_modules/safe-array-concat"

"safe-buffer@npm:5.1.2":
  locations:
    - "node_modules/readable-stream/node_modules/safe-buffer"

"safe-buffer@npm:5.2.1":
  locations:
    - "node_modules/safe-buffer"

"safe-push-apply@npm:1.0.0":
  locations:
    - "node_modules/safe-push-apply"

"safe-regex-test@npm:1.1.0":
  locations:
    - "node_modules/safe-regex-test"

"safer-buffer@npm:2.1.2":
  locations:
    - "node_modules/safer-buffer"

"sass-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:14.2.1":
  locations:
    - "node_modules/sass-loader"

"sass@npm:1.89.2":
  locations:
    - "node_modules/sass"

"sax@npm:1.2.4":
  locations:
    - "node_modules/sax"

"saxes@npm:6.0.0":
  locations:
    - "node_modules/saxes"

"scheduler@npm:0.23.2":
  locations:
    - "node_modules/scheduler"

"schema-utils@npm:2.7.1":
  locations:
    - "node_modules/babel-loader/node_modules/schema-utils"

"schema-utils@npm:3.3.0":
  locations:
    - "node_modules/webpack/node_modules/schema-utils"

"schema-utils@npm:4.3.2":
  locations:
    - "node_modules/schema-utils"

"scss-bundle@npm:3.1.2":
  locations:
    - "node_modules/scss-bundle"

"seek-bzip@npm:1.0.6":
  locations:
    - "node_modules/seek-bzip"

"select-hose@npm:2.0.0":
  locations:
    - "node_modules/select-hose"

"selfsigned@npm:2.4.1":
  locations:
    - "node_modules/selfsigned"

"semver@npm:5.7.2":
  locations:
    - "node_modules/make-dir/node_modules/semver"

"semver@npm:6.3.1":
  locations:
    - "node_modules/semver"

"semver@npm:7.7.2":
  locations:
    - "node_modules/postcss-loader/node_modules/semver"
    - "node_modules/node-gyp/node_modules/semver"
    - "node_modules/css-loader/node_modules/semver"
    - "node_modules/@typescript-eslint/utils/node_modules/semver"
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/semver"
    - "node_modules/@typescript-eslint/eslint-plugin/node_modules/semver"
    - "node_modules/@tarojs/cli/node_modules/semver"
    - "node_modules/@npmcli/fs/node_modules/semver"
    - "node_modules/@commitlint/is-ignored/node_modules/semver"

"send@npm:0.19.0":
  locations:
    - "node_modules/send"

"sentence-case@npm:3.0.4":
  locations:
    - "node_modules/sentence-case"

"serialize-javascript@npm:6.0.2":
  locations:
    - "node_modules/serialize-javascript"

"serve-index@npm:1.9.1":
  locations:
    - "node_modules/serve-index"

"serve-static@npm:1.16.2":
  locations:
    - "node_modules/serve-static"

"set-function-length@npm:1.2.2":
  locations:
    - "node_modules/set-function-length"

"set-function-name@npm:2.0.2":
  locations:
    - "node_modules/set-function-name"

"set-proto@npm:1.0.0":
  locations:
    - "node_modules/set-proto"

"setprototypeof@npm:1.1.0":
  locations:
    - "node_modules/serve-index/node_modules/setprototypeof"

"setprototypeof@npm:1.2.0":
  locations:
    - "node_modules/setprototypeof"

"shallow-clone@npm:3.0.1":
  locations:
    - "node_modules/shallow-clone"

"shebang-command@npm:2.0.0":
  locations:
    - "node_modules/shebang-command"

"shebang-regex@npm:3.0.0":
  locations:
    - "node_modules/shebang-regex"

"shell-quote@npm:1.8.3":
  locations:
    - "node_modules/shell-quote"

"side-channel-list@npm:1.0.0":
  locations:
    - "node_modules/side-channel-list"

"side-channel-map@npm:1.0.1":
  locations:
    - "node_modules/side-channel-map"

"side-channel-weakmap@npm:1.0.2":
  locations:
    - "node_modules/side-channel-weakmap"

"side-channel@npm:1.1.0":
  locations:
    - "node_modules/side-channel"

"signal-exit@npm:3.0.7":
  locations:
    - "node_modules/restore-cursor/node_modules/signal-exit"
    - "node_modules/execa/node_modules/signal-exit"

"signal-exit@npm:4.1.0":
  locations:
    - "node_modules/signal-exit"

"slash@npm:3.0.0":
  locations:
    - "node_modules/globby/node_modules/slash"

"slash@npm:5.1.0":
  locations:
    - "node_modules/slash"

"slice-ansi@npm:4.0.0":
  locations:
    - "node_modules/table/node_modules/slice-ansi"

"slice-ansi@npm:5.0.0":
  locations:
    - "node_modules/slice-ansi"

"slice-ansi@npm:7.1.0":
  locations:
    - "node_modules/log-update/node_modules/slice-ansi"

"smart-buffer@npm:4.2.0":
  locations:
    - "node_modules/smart-buffer"

"snake-case@npm:3.0.4":
  locations:
    - "node_modules/snake-case"

"sockjs@npm:0.3.24":
  locations:
    - "node_modules/sockjs"

"socks-proxy-agent@npm:8.0.5":
  locations:
    - "node_modules/socks-proxy-agent"

"socks@npm:2.8.6":
  locations:
    - "node_modules/socks"

"sort-keys-length@npm:1.0.1":
  locations:
    - "node_modules/sort-keys-length"

"sort-keys@npm:1.1.2":
  locations:
    - "node_modules/sort-keys"

"sort-keys@npm:2.0.0":
  locations:
    - "node_modules/normalize-url/node_modules/sort-keys"

"source-list-map@npm:2.0.1":
  locations:
    - "node_modules/source-list-map"

"source-map-js@npm:1.2.1":
  locations:
    - "node_modules/source-map-js"

"source-map-support@npm:0.5.21":
  locations:
    - "node_modules/source-map-support"

"source-map@npm:0.5.7":
  locations:
    - "node_modules/miniprogram-simulate/node_modules/css-tree/node_modules/source-map"

"source-map@npm:0.6.1":
  locations:
    - "node_modules/source-map"

"source-map@npm:0.7.4":
  locations:
    - "node_modules/@pmmmwh/react-refresh-webpack-plugin/node_modules/source-map"

"spdy-transport@npm:3.0.0":
  locations:
    - "node_modules/spdy-transport"

"spdy@npm:4.0.2":
  locations:
    - "node_modules/spdy"

"split-on-first@npm:3.0.0":
  locations:
    - "node_modules/split-on-first"

"split2@npm:4.2.0":
  locations:
    - "node_modules/split2"

"sprintf-js@npm:1.1.3":
  locations:
    - "node_modules/sprintf-js"

"ssri@npm:12.0.0":
  locations:
    - "node_modules/ssri"

"stackframe@npm:1.3.4":
  locations:
    - "node_modules/stackframe"

"statuses@npm:1.5.0":
  locations:
    - "node_modules/serve-index/node_modules/statuses"

"statuses@npm:2.0.1":
  locations:
    - "node_modules/statuses"

"std-env@npm:3.9.0":
  locations:
    - "node_modules/std-env"

"stop-iteration-iterator@npm:1.1.0":
  locations:
    - "node_modules/stop-iteration-iterator"

"strict-uri-encode@npm:1.1.0":
  locations:
    - "node_modules/strict-uri-encode"

"string-argv@npm:0.3.2":
  locations:
    - "node_modules/string-argv"

"string-width@npm:4.2.3":
  locations:
    - "node_modules/string-width-cjs"
    - "node_modules/string-width"

"string-width@npm:5.1.2":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/string-width"

"string-width@npm:7.2.0":
  locations:
    - "node_modules/log-update/node_modules/string-width"
    - "node_modules/listr2/node_modules/string-width"
    - "node_modules/cli-truncate/node_modules/string-width"

"string.fromcodepoint@npm:0.2.1":
  locations:
    - "node_modules/string.fromcodepoint"

"string.prototype.matchall@npm:4.0.12":
  locations:
    - "node_modules/string.prototype.matchall"

"string.prototype.repeat@npm:1.0.0":
  locations:
    - "node_modules/string.prototype.repeat"

"string.prototype.trim@npm:1.2.10":
  locations:
    - "node_modules/string.prototype.trim"

"string.prototype.trimend@npm:1.0.9":
  locations:
    - "node_modules/string.prototype.trimend"

"string.prototype.trimstart@npm:1.0.8":
  locations:
    - "node_modules/string.prototype.trimstart"

"string_decoder@npm:1.1.1":
  locations:
    - "node_modules/readable-stream/node_modules/string_decoder"

"string_decoder@npm:1.3.0":
  locations:
    - "node_modules/string_decoder"

"strip-ansi@npm:6.0.1":
  locations:
    - "node_modules/strip-ansi-cjs"
    - "node_modules/strip-ansi"

"strip-ansi@npm:7.1.0":
  locations:
    - "node_modules/log-update/node_modules/strip-ansi"
    - "node_modules/listr2/node_modules/strip-ansi"
    - "node_modules/cli-truncate/node_modules/strip-ansi"
    - "node_modules/@isaacs/cliui/node_modules/strip-ansi"

"strip-bom@npm:3.0.0":
  locations:
    - "node_modules/strip-bom"

"strip-dirs@npm:2.1.0":
  locations:
    - "node_modules/strip-dirs"

"strip-final-newline@npm:2.0.0":
  locations:
    - "node_modules/strip-final-newline"

"strip-json-comments@npm:2.0.1":
  locations:
    - "node_modules/rc/node_modules/strip-json-comments"

"strip-json-comments@npm:3.1.1":
  locations:
    - "node_modules/strip-json-comments"

"strip-outer@npm:1.0.1":
  locations:
    - "node_modules/strip-outer"

"style-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:3.3.4":
  locations:
    - "node_modules/style-loader"

"stylehacks@virtual:d25796dc11e22ecc50506bf9f5998d1d59a017d7880493950c506796d7fc6123adb37ac1c166208589aebdcedf0d2d45a6493aa801ad8a6249d377d0aa5d0b76#npm:6.1.1":
  locations:
    - "node_modules/stylehacks"

"stylelint-config-recommended@virtual:e88aa0a96d9b3f472bbdddfd6880cc2ad9b644e6d3d1a356b536e91cb761b7101f41e32828b3d38c7a38a9eafde3b5836a8c8b87a002f45c193a79bb67f6e0a7#npm:16.0.0":
  locations:
    - "node_modules/stylelint-config-recommended"

"stylelint-config-standard@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:38.0.0":
  locations:
    - "node_modules/stylelint-config-standard"

"stylelint@npm:16.22.0":
  locations:
    - "node_modules/stylelint"

"stylus-loader@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:8.1.1":
  locations:
    - "node_modules/stylus-loader"

"supports-color@npm:7.2.0":
  locations:
    - "node_modules/supports-color"

"supports-color@npm:8.1.1":
  locations:
    - "node_modules/terser-webpack-plugin/node_modules/supports-color"
    - "node_modules/jest-worker/node_modules/supports-color"

"supports-hyperlinks@npm:3.2.0":
  locations:
    - "node_modules/supports-hyperlinks"

"supports-preserve-symlinks-flag@npm:1.0.0":
  locations:
    - "node_modules/supports-preserve-symlinks-flag"

"svg-tags@npm:1.0.0":
  locations:
    - "node_modules/svg-tags"

"svgo@npm:3.3.2":
  locations:
    - "node_modules/svgo"

"swiper@npm:11.1.15":
  locations:
    - "node_modules/swiper"

"symbol-tree@npm:3.2.4":
  locations:
    - "node_modules/symbol-tree"

"table@npm:6.9.0":
  locations:
    - "node_modules/table"

"tapable@npm:2.2.2":
  locations:
    - "node_modules/tapable"

"tar-stream@npm:1.6.2":
  locations:
    - "node_modules/tar-stream"

"tar@npm:7.4.3":
  locations:
    - "node_modules/tar"

"terser-webpack-plugin@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:5.3.14":
  locations:
    - "node_modules/terser-webpack-plugin"
  aliases:
    - "virtual:1b005cc486314ab2650d6dafee33b27297fc25907e0d543dacc6d9920a74d794fab5914693c8b5b84f40ec58a16155278af860b16c03011283a613169820ff32#npm:5.3.14"

"terser@npm:5.43.1":
  locations:
    - "node_modules/terser"

"text-extensions@npm:2.4.0":
  locations:
    - "node_modules/text-extensions"

"text-table@npm:0.2.0":
  locations:
    - "node_modules/text-table"

"thenify-all@npm:1.6.0":
  locations:
    - "node_modules/thenify-all"

"thenify@npm:3.3.1":
  locations:
    - "node_modules/thenify"

"through@npm:2.3.8":
  locations:
    - "node_modules/through"

"thunky@npm:1.1.0":
  locations:
    - "node_modules/thunky"

"timed-out@npm:4.0.1":
  locations:
    - "node_modules/timed-out"

"tiny-case@npm:1.0.3":
  locations:
    - "node_modules/tiny-case"

"tinyexec@npm:1.0.1":
  locations:
    - "node_modules/tinyexec"

"tinyglobby@npm:0.2.14":
  locations:
    - "node_modules/tinyglobby"

"tmp@npm:0.0.33":
  locations:
    - "node_modules/tmp"

"to-buffer@npm:1.2.1":
  locations:
    - "node_modules/to-buffer"

"to-fast-properties@npm:2.0.0":
  locations:
    - "node_modules/to-fast-properties"

"to-readable-stream@npm:1.0.0":
  locations:
    - "node_modules/to-readable-stream"

"to-regex-range@npm:5.0.1":
  locations:
    - "node_modules/to-regex-range"

"toidentifier@npm:1.0.1":
  locations:
    - "node_modules/toidentifier"

"toposort@npm:2.0.2":
  locations:
    - "node_modules/toposort"

"tough-cookie@npm:4.1.4":
  locations:
    - "node_modules/tough-cookie"

"tr46@npm:5.1.1":
  locations:
    - "node_modules/tr46"

"trim-repeated@npm:1.0.0":
  locations:
    - "node_modules/trim-repeated"

"ts-api-utils@virtual:ef5ce8a2d1de673415b872ed9516c507b2b800a096426c6abdcedaee20fe3c53630df3720131e09b5879b117ce8ae7c7f98dd80950d0f26dc28ca84d94a061a4#npm:1.4.3":
  locations:
    - "node_modules/ts-api-utils"

"ts-morph@npm:26.0.0":
  locations:
    - "node_modules/ts-morph"

"tsconfig-paths-webpack-plugin@npm:4.2.0":
  locations:
    - "node_modules/tsconfig-paths-webpack-plugin"

"tsconfig-paths@npm:3.15.0":
  locations:
    - "node_modules/tsconfig-paths"

"tsconfig-paths@npm:4.2.0":
  locations:
    - "node_modules/tsconfig-paths-webpack-plugin/node_modules/tsconfig-paths"

"tslib@npm:1.14.1":
  locations:
    - "node_modules/scss-bundle/node_modules/tslib"
    - "node_modules/less/node_modules/tslib"

"tslib@npm:2.8.1":
  locations:
    - "node_modules/tslib"

"tunnel-agent@npm:0.6.0":
  locations:
    - "node_modules/tunnel-agent"

"type-check@npm:0.4.0":
  locations:
    - "node_modules/type-check"

"type-fest@npm:0.20.2":
  locations:
    - "node_modules/globals/node_modules/type-fest"

"type-fest@npm:0.21.3":
  locations:
    - "node_modules/type-fest"

"type-fest@npm:2.19.0":
  locations:
    - "node_modules/yup/node_modules/type-fest"

"type-is@npm:1.6.18":
  locations:
    - "node_modules/type-is"

"typed-array-buffer@npm:1.0.3":
  locations:
    - "node_modules/typed-array-buffer"

"typed-array-byte-length@npm:1.0.3":
  locations:
    - "node_modules/typed-array-byte-length"

"typed-array-byte-offset@npm:1.0.4":
  locations:
    - "node_modules/typed-array-byte-offset"

"typed-array-length@npm:1.0.7":
  locations:
    - "node_modules/typed-array-length"

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5":
  locations:
    - "node_modules/typescript"

"uglify-js@npm:3.19.3":
  locations:
    - "node_modules/uglify-js"

"unbox-primitive@npm:1.1.0":
  locations:
    - "node_modules/unbox-primitive"

"unbzip2-stream@npm:1.4.3":
  locations:
    - "node_modules/unbzip2-stream"

"undici-types@npm:5.26.5":
  locations:
    - "node_modules/@types/node/node_modules/undici-types"

"undici-types@npm:7.8.0":
  locations:
    - "node_modules/undici-types"

"unescape-js@npm:1.1.4":
  locations:
    - "node_modules/unescape-js"

"unicode-canonical-property-names-ecmascript@npm:2.0.1":
  locations:
    - "node_modules/unicode-canonical-property-names-ecmascript"

"unicode-match-property-ecmascript@npm:2.0.0":
  locations:
    - "node_modules/unicode-match-property-ecmascript"

"unicode-match-property-value-ecmascript@npm:2.2.0":
  locations:
    - "node_modules/unicode-match-property-value-ecmascript"

"unicode-property-aliases-ecmascript@npm:2.1.0":
  locations:
    - "node_modules/unicode-property-aliases-ecmascript"

"unicorn-magic@npm:0.1.0":
  locations:
    - "node_modules/unicorn-magic"

"unicorn-magic@npm:0.3.0":
  locations:
    - "node_modules/copy-webpack-plugin/node_modules/unicorn-magic"

"unique-filename@npm:4.0.0":
  locations:
    - "node_modules/unique-filename"

"unique-slug@npm:5.0.0":
  locations:
    - "node_modules/unique-slug"

"universal-router@npm:9.2.1":
  locations:
    - "node_modules/universal-router"

"universalify@npm:0.1.2":
  locations:
    - "node_modules/scss-bundle/node_modules/universalify"

"universalify@npm:0.2.0":
  locations:
    - "node_modules/tough-cookie/node_modules/universalify"

"universalify@npm:2.0.1":
  locations:
    - "node_modules/universalify"

"unpipe@npm:1.0.0":
  locations:
    - "node_modules/unpipe"

"update-browserslist-db@virtual:7df10d33cd6842659a3529d46decd4f1eeb5ec25fc4c848cff54ea69abd11a20a55277c57a073bbb3a702942d2ae57b9433c8450dcbffbc4f38ee3eb9668c39d#npm:1.1.3":
  locations:
    - "node_modules/update-browserslist-db"

"upper-case-first@npm:2.0.2":
  locations:
    - "node_modules/upper-case-first"

"upper-case@npm:1.1.3":
  locations:
    - "node_modules/html-minifier/node_modules/upper-case"

"upper-case@npm:2.0.2":
  locations:
    - "node_modules/upper-case"

"uri-js@npm:4.4.1":
  locations:
    - "node_modules/uri-js"

"url-parse-lax@npm:3.0.0":
  locations:
    - "node_modules/url-parse-lax"

"url-parse@npm:1.5.10":
  locations:
    - "node_modules/url-parse"

"url-to-options@npm:1.0.1":
  locations:
    - "node_modules/url-to-options"

"util-deprecate@npm:1.0.2":
  locations:
    - "node_modules/util-deprecate"

"utila@npm:0.4.0":
  locations:
    - "node_modules/utila"

"utils-merge@npm:1.0.1":
  locations:
    - "node_modules/utils-merge"

"uuid@npm:8.3.2":
  locations:
    - "node_modules/uuid"

"validate-html-nesting@npm:1.2.3":
  locations:
    - "node_modules/validate-html-nesting"

"validate-npm-package-name@npm:5.0.1":
  locations:
    - "node_modules/validate-npm-package-name"

"vary@npm:1.1.2":
  locations:
    - "node_modules/vary"

"vm2@npm:3.9.19":
  locations:
    - "node_modules/vm2"

"w3c-xmlserializer@npm:5.0.0":
  locations:
    - "node_modules/w3c-xmlserializer"

"watchpack@npm:2.4.4":
  locations:
    - "node_modules/watchpack"

"wbuf@npm:1.7.3":
  locations:
    - "node_modules/wbuf"

"wcwidth@npm:1.0.1":
  locations:
    - "node_modules/wcwidth"

"webidl-conversions@npm:7.0.0":
  locations:
    - "node_modules/webidl-conversions"

"webpack-chain@npm:6.5.1":
  locations:
    - "node_modules/webpack-chain"

"webpack-dev-middleware@virtual:9b1d99f03c1f3a504b44484c029d64228ed613d8bb927935b853e3aa6475a01f928d7959e9613a6a102a2c9d6ca62f660a37d2d1ff392e10945a8b17c7e25a00#npm:5.3.4":
  locations:
    - "node_modules/webpack-dev-middleware"

"webpack-dev-server@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:4.15.2":
  locations:
    - "node_modules/webpack-dev-server"

"webpack-format-messages@npm:3.0.1":
  locations:
    - "node_modules/webpack-format-messages"

"webpack-merge@npm:5.10.0":
  locations:
    - "node_modules/webpack-merge"

"webpack-sources@npm:1.4.3":
  locations:
    - "node_modules/esbuild-loader/node_modules/webpack-sources"

"webpack-sources@npm:3.3.3":
  locations:
    - "node_modules/webpack-sources"

"webpack-virtual-modules@npm:0.6.2":
  locations:
    - "node_modules/webpack-virtual-modules"

"webpack@virtual:e03d5906d3bdf1756ede21aad74181cd3293232b7c75eee289fbf8441210b94dd4fce6a1ad06591eb2b0b8dd2ff1210d21cfd80f0a9894b9178a4d31ca598f5d#npm:5.91.0":
  locations:
    - "node_modules/webpack"

"webpackbar@virtual:1a7617ade1bb774e54efb694faa77b2d77e21274ff6566916ab3a5c9a0539e97157e7880732d10d58eccb4887c362b4b64487a040e9fe74d0eb00e46cffc52a3#npm:5.0.2":
  locations:
    - "node_modules/webpackbar"

"websocket-driver@npm:0.7.4":
  locations:
    - "node_modules/websocket-driver"

"websocket-extensions@npm:0.1.4":
  locations:
    - "node_modules/websocket-extensions"

"whatwg-encoding@npm:3.1.1":
  locations:
    - "node_modules/whatwg-encoding"

"whatwg-fetch@npm:3.6.20":
  locations:
    - "node_modules/whatwg-fetch"

"whatwg-mimetype@npm:4.0.0":
  locations:
    - "node_modules/whatwg-mimetype"

"whatwg-url@npm:14.2.0":
  locations:
    - "node_modules/whatwg-url"

"which-boxed-primitive@npm:1.1.1":
  locations:
    - "node_modules/which-boxed-primitive"

"which-builtin-type@npm:1.2.1":
  locations:
    - "node_modules/which-builtin-type"

"which-collection@npm:1.0.2":
  locations:
    - "node_modules/which-collection"

"which-typed-array@npm:1.1.19":
  locations:
    - "node_modules/which-typed-array"

"which@npm:1.3.1":
  locations:
    - "node_modules/global-prefix/node_modules/which"

"which@npm:2.0.2":
  locations:
    - "node_modules/which"

"which@npm:5.0.0":
  locations:
    - "node_modules/node-gyp/node_modules/which"

"wildcard@npm:2.0.1":
  locations:
    - "node_modules/wildcard"

"word-pronunciation-game@workspace:.":
  locations:
    - ""
  bin:
    "node_modules/@commitlint/is-ignored":
      "semver": "semver/bin/semver.js"
    "node_modules/@tarojs/cli":
      "semver": "semver/bin/semver.js"
    "node_modules/@tarojs/plugin-generator/node_modules/@babel/generator":
      "jsesc": "jsesc/bin/jsesc"
    "node_modules/@tarojs/plugin-generator/node_modules/@babel/traverse":
      "parser": "@babel/parser/bin/babel-parser.js"
    "node_modules/@tarojs/plugin-generator":
      "parser": "@babel/parser/bin/babel-parser.js"
    "node_modules/@tarojs/plugin-doctor":
      "eslint": "eslint/bin/eslint.js"
      "glob": "glob/dist/cjs/src/bin.js"
    "node_modules/postcss-loader":
      "semver": "semver/bin/semver.js"
      "jiti": "jiti/bin/jiti.js"
    "node_modules/node-gyp":
      "semver": "semver/bin/semver.js"
      "node-which": "which/bin/which.js"
    "node_modules/css-loader":
      "semver": "semver/bin/semver.js"
    "node_modules/@typescript-eslint/utils":
      "semver": "semver/bin/semver.js"
    "node_modules/@typescript-eslint/typescript-estree":
      "semver": "semver/bin/semver.js"
    "node_modules/@typescript-eslint/eslint-plugin":
      "semver": "semver/bin/semver.js"
    "node_modules/@npmcli/fs":
      "semver": "semver/bin/semver.js"
    "node_modules/eslint-plugin-react":
      "resolve": "resolve/bin/resolve"
    "node_modules/cacache":
      "glob": "glob/dist/esm/bin.mjs"
    "node_modules/babel-loader":
      "json5": "json5/lib/cli.js"
    "node_modules/make-dir":
      "semver": "semver/bin/semver"
    "node_modules/tsconfig-paths":
      "json5": "json5/lib/cli.js"
    "node_modules/esbuild-loader":
      "esbuild": "esbuild/bin/esbuild"
    "node_modules/postcss-url":
      "mime": "mime/cli.js"
    "node_modules/global-prefix":
      "which": "which/bin/which"
    "node_modules/regjsparser":
      "jsesc": "jsesc/bin/jsesc"
    ".":
      "parser": "@babel/parser/bin/babel-parser.js"
      "commitlint": "@commitlint/cli/cli.js"
      "taro": "@tarojs/cli/bin/taro"
      "node-gyp": "node-gyp/bin/node-gyp.js"
      "eslint": "eslint/bin/eslint.js"
      "husky": "husky/bin.js"
      "lint-staged": "lint-staged/bin/lint-staged.js"
      "sass": "sass/sass.js"
      "stylelint": "stylelint/bin/stylelint.mjs"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "webpack": "webpack/bin/webpack.js"
      "json5": "json5/lib/cli.js"
      "semver": "semver/bin/semver.js"
      "acorn": "acorn/bin/acorn"
      "browserslist": "browserslist/cli.js"
      "ansi-html": "ansi-html-community/bin/ansi-html"
      "highlight": "cli-highlight/bin/highlight"
      "envinfo": "envinfo/dist/cli.js"
      "stencil": "@stencil/core/bin/stencil"
      "swc-node": "@swc/register/bin/swc-node"
      "esbuild": "esbuild/bin/esbuild"
      "resolve": "resolve/bin/resolve"
      "loose-envify": "loose-envify/cli.js"
      "jsesc": "jsesc/bin/jsesc"
      "nanoid": "nanoid/bin/nanoid.cjs"
      "autoprefixer": "autoprefixer/bin/autoprefixer"
      "detect": "detect-port/bin/detect-port.js"
      "detect-port": "detect-port/bin/detect-port.js"
      "html-minifier": "html-minifier/cli.js"
      "vm2": "vm2/bin/vm2"
      "webpack-dev-server": "webpack-dev-server/bin/webpack-dev-server.js"
      "js-yaml": "js-yaml/bin/js-yaml.js"
      "pidtree": "pidtree/bin/pidtree.js"
      "yaml": "yaml/bin.mjs"
      "cssesc": "cssesc/bin/cssesc"
      "git-raw-commits": "git-raw-commits/cli.mjs"
      "update-browserslist-db": "update-browserslist-db/cli.js"
      "terser": "terser/bin/terser"
      "rimraf": "rimraf/bin.js"
      "node-which": "which/bin/node-which"
      "rollup": "rollup/dist/bin/rollup"
      "scss-bundle": "scss-bundle/dist/cli/main.js"
      "lessc": "less/bin/lessc"
      "flat": "flat/cli.js"
      "detect-libc": "detect-libc/bin/detect-libc.js"
      "he": "he/bin/he"
      "uglifyjs": "uglify-js/bin/uglifyjs"
      "html-minifier-terser": "html-minifier-terser/cli.js"
      "conventional-commits-parser": "conventional-commits-parser/cli.mjs"
      "jiti": "jiti/lib/jiti-cli.mjs"
      "nopt": "nopt/bin/nopt.js"
      "errno": "errno/cli.js"
      "image-size": "image-size/bin/image-size.js"
      "mime": "mime/cli.js"
      "multicast-dns": "multicast-dns/cli.js"
      "is-docker": "is-docker/cli.js"
      "uuid": "uuid/dist/bin/uuid"
      "JSONStream": "JSONStream/bin.js"
      "rc": "rc/cli.js"
      "mkdirp": "mkdirp/dist/cjs/src/bin.js"
      "seek-bunzip": "seek-bzip/bin/seek-bunzip"
      "seek-table": "seek-bzip/bin/seek-bzip-table"
      "svgo": "svgo/bin/svgo"
      "regjsparser": "regjsparser/bin/parser"

"word-wrap@npm:1.2.5":
  locations:
    - "node_modules/word-wrap"

"wrap-ansi@npm:6.2.0":
  locations:
    - "node_modules/inquirer/node_modules/wrap-ansi"

"wrap-ansi@npm:7.0.0":
  locations:
    - "node_modules/wrap-ansi-cjs"
    - "node_modules/wrap-ansi"

"wrap-ansi@npm:8.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/wrap-ansi"

"wrap-ansi@npm:9.0.0":
  locations:
    - "node_modules/log-update/node_modules/wrap-ansi"
    - "node_modules/listr2/node_modules/wrap-ansi"

"wrappy@npm:1.0.2":
  locations:
    - "node_modules/wrappy"

"write-file-atomic@npm:5.0.1":
  locations:
    - "node_modules/write-file-atomic"

"ws@virtual:2d9cb15e9bf16496dbe2f06043b86c911ca09908131c8842e509969130359c61a05d182fb487004e8a564dd5f61d6f7317748c2e43921424f4f3531746ab1503#npm:8.18.3":
  locations:
    - "node_modules/ws"

"xml-name-validator@npm:5.0.0":
  locations:
    - "node_modules/xml-name-validator"

"xmlchars@npm:2.2.0":
  locations:
    - "node_modules/xmlchars"

"xtend@npm:4.0.2":
  locations:
    - "node_modules/xtend"

"xxhashjs@npm:0.2.2":
  locations:
    - "node_modules/xxhashjs"

"y18n@npm:5.0.8":
  locations:
    - "node_modules/y18n"

"yallist@npm:3.1.1":
  locations:
    - "node_modules/yallist"

"yallist@npm:4.0.0":
  locations:
    - "node_modules/minipass-sized/node_modules/yallist"
    - "node_modules/minipass-pipeline/node_modules/yallist"
    - "node_modules/minipass-flush/node_modules/yallist"

"yallist@npm:5.0.0":
  locations:
    - "node_modules/tar/node_modules/yallist"

"yaml@npm:2.8.0":
  locations:
    - "node_modules/yaml"

"yargs-parser@npm:20.2.9":
  locations:
    - "node_modules/cli-highlight/node_modules/yargs-parser"

"yargs-parser@npm:21.1.1":
  locations:
    - "node_modules/yargs-parser"

"yargs@npm:16.2.0":
  locations:
    - "node_modules/cli-highlight/node_modules/yargs"

"yargs@npm:17.7.2":
  locations:
    - "node_modules/yargs"

"yauzl@npm:2.10.0":
  locations:
    - "node_modules/yauzl"

"yocto-queue@npm:0.1.0":
  locations:
    - "node_modules/find-up/node_modules/yocto-queue"

"yocto-queue@npm:1.2.1":
  locations:
    - "node_modules/yocto-queue"

"yup@npm:1.6.1":
  locations:
    - "node_modules/yup"
