{"name": "timed-out", "version": "4.0.1", "description": "Emit `ETIMEDOUT` or `ESOCKETTIMEDOUT` when ClientRequest is hanged", "license": "MIT", "repository": "floatdrop/timed-out", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && mocha"}, "files": ["index.js"], "keywords": ["http", "https", "get", "got", "url", "uri", "request", "util", "utility", "simple"], "devDependencies": {"mocha": "*", "xo": "^0.16.0"}}