{"name": "@tarojs/helper", "version": "4.1.4", "description": "<PERSON><PERSON>", "author": "O2Team", "license": "MIT", "main": "index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/NervJS/taro.git"}, "files": ["index.js", "dist", "types", "swc"], "keywords": ["taro"], "bugs": {"url": "https://github.com/NervJS/taro/issues"}, "engines": {"node": ">= 18"}, "homepage": "https://github.com/NervJS/taro#readme", "dependencies": {"@babel/core": "^7.24.4", "@babel/generator": "^7.24.4", "@babel/parser": "^7.24.4", "@babel/traverse": "^7.24.1", "@babel/types": "^7.24.0", "@swc/core": "1.3.96", "@swc/register": "0.1.10", "ansi-escapes": "^4.3.2", "chalk": "^4.1.2", "chokidar": "^3.6.0", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "esbuild": "~0.21.0", "find-yarn-workspace-root": "^2.0.0", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "require-from-string": "^2.0.2", "resolve": "^1.22.8", "supports-hyperlinks": "^3.0.0"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.6.8", "@types/babel__traverse": "^7.20.5", "@tarojs/taro": "4.1.4"}, "scripts": {"prod": "pnpm run build", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "pnpm run clean", "build": "tsc", "postbuild": "pnpm run swc:backup", "dev": "tsc -w", "test": "jest --collectCoverage", "test:ci": "jest --ci -i --coverage --silent", "artifacts": "node scripts/artifacts.js", "swc:backup": "node scripts/backup.js"}}