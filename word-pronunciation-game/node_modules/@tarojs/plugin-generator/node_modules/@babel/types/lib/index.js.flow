// NOTE: This file is autogenerated. Do not modify.
// See packages/babel-types/scripts/generators/flow.js for script used.

declare class BabelNodeComment {
  value: string;
  start: number;
  end: number;
  loc: BabelNodeSourceLocation;
}

declare class BabelNodeCommentBlock extends BabelNodeComment {
  type: "CommentBlock";
}

declare class BabelNodeCommentLine extends BabelNodeComment {
  type: "CommentLine";
}

declare class BabelNodeSourceLocation {
  start: {
    line: number;
    column: number;
  };

  end: {
    line: number;
    column: number;
  };
}

declare class BabelNode {
  leadingComments?: Array<BabelNodeComment>;
  innerComments?: Array<BabelNodeComment>;
  trailingComments?: Array<BabelNodeComment>;
  start: ?number;
  end: ?number;
  loc: ?BabelNodeSourceLocation;
  extra?: { [string]: mixed };
}

declare class BabelNodeArrayExpression extends BabelNode {
  type: "ArrayExpression";
  elements?: Array<null | BabelNodeExpression | BabelNodeSpreadElement>;
}

declare class BabelNodeAssignmentExpression extends BabelNode {
  type: "AssignmentExpression";
  operator: string;
  left: BabelNodeLVal | BabelNodeOptionalMemberExpression;
  right: BabelNodeExpression;
}

declare class BabelNodeBinaryExpression extends BabelNode {
  type: "BinaryExpression";
  operator: "+" | "-" | "/" | "%" | "*" | "**" | "&" | "|" | ">>" | ">>>" | "<<" | "^" | "==" | "===" | "!=" | "!==" | "in" | "instanceof" | ">" | "<" | ">=" | "<=" | "|>";
  left: BabelNodeExpression | BabelNodePrivateName;
  right: BabelNodeExpression;
}

declare class BabelNodeInterpreterDirective extends BabelNode {
  type: "InterpreterDirective";
  value: string;
}

declare class BabelNodeDirective extends BabelNode {
  type: "Directive";
  value: BabelNodeDirectiveLiteral;
}

declare class BabelNodeDirectiveLiteral extends BabelNode {
  type: "DirectiveLiteral";
  value: string;
}

declare class BabelNodeBlockStatement extends BabelNode {
  type: "BlockStatement";
  body: Array<BabelNodeStatement>;
  directives?: Array<BabelNodeDirective>;
}

declare class BabelNodeBreakStatement extends BabelNode {
  type: "BreakStatement";
  label?: BabelNodeIdentifier;
}

declare class BabelNodeCallExpression extends BabelNode {
  type: "CallExpression";
  callee: BabelNodeExpression | BabelNodeSuper | BabelNodeV8IntrinsicIdentifier;
  arguments: Array<BabelNodeExpression | BabelNodeSpreadElement | BabelNodeJSXNamespacedName | BabelNodeArgumentPlaceholder>;
  optional?: true | false;
  typeArguments?: BabelNodeTypeParameterInstantiation;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeCatchClause extends BabelNode {
  type: "CatchClause";
  param?: BabelNodeIdentifier | BabelNodeArrayPattern | BabelNodeObjectPattern;
  body: BabelNodeBlockStatement;
}

declare class BabelNodeConditionalExpression extends BabelNode {
  type: "ConditionalExpression";
  test: BabelNodeExpression;
  consequent: BabelNodeExpression;
  alternate: BabelNodeExpression;
}

declare class BabelNodeContinueStatement extends BabelNode {
  type: "ContinueStatement";
  label?: BabelNodeIdentifier;
}

declare class BabelNodeDebuggerStatement extends BabelNode {
  type: "DebuggerStatement";
}

declare class BabelNodeDoWhileStatement extends BabelNode {
  type: "DoWhileStatement";
  test: BabelNodeExpression;
  body: BabelNodeStatement;
}

declare class BabelNodeEmptyStatement extends BabelNode {
  type: "EmptyStatement";
}

declare class BabelNodeExpressionStatement extends BabelNode {
  type: "ExpressionStatement";
  expression: BabelNodeExpression;
}

declare class BabelNodeFile extends BabelNode {
  type: "File";
  program: BabelNodeProgram;
  comments?: Array<BabelNodeCommentBlock | BabelNodeCommentLine>;
  tokens?: Array<any>;
}

declare class BabelNodeForInStatement extends BabelNode {
  type: "ForInStatement";
  left: BabelNodeVariableDeclaration | BabelNodeLVal;
  right: BabelNodeExpression;
  body: BabelNodeStatement;
}

declare class BabelNodeForStatement extends BabelNode {
  type: "ForStatement";
  init?: BabelNodeVariableDeclaration | BabelNodeExpression;
  test?: BabelNodeExpression;
  update?: BabelNodeExpression;
  body: BabelNodeStatement;
}

declare class BabelNodeFunctionDeclaration extends BabelNode {
  type: "FunctionDeclaration";
  id?: BabelNodeIdentifier;
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>;
  body: BabelNodeBlockStatement;
  generator?: boolean;
  async?: boolean;
  declare?: boolean;
  predicate?: BabelNodeDeclaredPredicate | BabelNodeInferredPredicate;
  returnType?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodeFunctionExpression extends BabelNode {
  type: "FunctionExpression";
  id?: BabelNodeIdentifier;
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>;
  body: BabelNodeBlockStatement;
  generator?: boolean;
  async?: boolean;
  predicate?: BabelNodeDeclaredPredicate | BabelNodeInferredPredicate;
  returnType?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodeIdentifier extends BabelNode {
  type: "Identifier";
  name: string;
  decorators?: Array<BabelNodeDecorator>;
  optional?: boolean;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
}

declare class BabelNodeIfStatement extends BabelNode {
  type: "IfStatement";
  test: BabelNodeExpression;
  consequent: BabelNodeStatement;
  alternate?: BabelNodeStatement;
}

declare class BabelNodeLabeledStatement extends BabelNode {
  type: "LabeledStatement";
  label: BabelNodeIdentifier;
  body: BabelNodeStatement;
}

declare class BabelNodeStringLiteral extends BabelNode {
  type: "StringLiteral";
  value: string;
}

declare class BabelNodeNumericLiteral extends BabelNode {
  type: "NumericLiteral";
  value: number;
}

declare class BabelNodeNullLiteral extends BabelNode {
  type: "NullLiteral";
}

declare class BabelNodeBooleanLiteral extends BabelNode {
  type: "BooleanLiteral";
  value: boolean;
}

declare class BabelNodeRegExpLiteral extends BabelNode {
  type: "RegExpLiteral";
  pattern: string;
  flags?: string;
}

declare class BabelNodeLogicalExpression extends BabelNode {
  type: "LogicalExpression";
  operator: "||" | "&&" | "??";
  left: BabelNodeExpression;
  right: BabelNodeExpression;
}

declare class BabelNodeMemberExpression extends BabelNode {
  type: "MemberExpression";
  object: BabelNodeExpression | BabelNodeSuper;
  property: BabelNodeExpression | BabelNodeIdentifier | BabelNodePrivateName;
  computed?: boolean;
  optional?: true | false;
}

declare class BabelNodeNewExpression extends BabelNode {
  type: "NewExpression";
  callee: BabelNodeExpression | BabelNodeSuper | BabelNodeV8IntrinsicIdentifier;
  arguments: Array<BabelNodeExpression | BabelNodeSpreadElement | BabelNodeJSXNamespacedName | BabelNodeArgumentPlaceholder>;
  optional?: true | false;
  typeArguments?: BabelNodeTypeParameterInstantiation;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeProgram extends BabelNode {
  type: "Program";
  body: Array<BabelNodeStatement>;
  directives?: Array<BabelNodeDirective>;
  sourceType?: "script" | "module";
  interpreter?: BabelNodeInterpreterDirective;
}

declare class BabelNodeObjectExpression extends BabelNode {
  type: "ObjectExpression";
  properties: Array<BabelNodeObjectMethod | BabelNodeObjectProperty | BabelNodeSpreadElement>;
}

declare class BabelNodeObjectMethod extends BabelNode {
  type: "ObjectMethod";
  kind?: "method" | "get" | "set";
  key: BabelNodeExpression | BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral;
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>;
  body: BabelNodeBlockStatement;
  computed?: boolean;
  generator?: boolean;
  async?: boolean;
  decorators?: Array<BabelNodeDecorator>;
  returnType?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodeObjectProperty extends BabelNode {
  type: "ObjectProperty";
  key: BabelNodeExpression | BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeDecimalLiteral | BabelNodePrivateName;
  value: BabelNodeExpression | BabelNodePatternLike;
  computed?: boolean;
  shorthand?: boolean;
  decorators?: Array<BabelNodeDecorator>;
}

declare class BabelNodeRestElement extends BabelNode {
  type: "RestElement";
  argument: BabelNodeLVal;
  decorators?: Array<BabelNodeDecorator>;
  optional?: boolean;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
}

declare class BabelNodeReturnStatement extends BabelNode {
  type: "ReturnStatement";
  argument?: BabelNodeExpression;
}

declare class BabelNodeSequenceExpression extends BabelNode {
  type: "SequenceExpression";
  expressions: Array<BabelNodeExpression>;
}

declare class BabelNodeParenthesizedExpression extends BabelNode {
  type: "ParenthesizedExpression";
  expression: BabelNodeExpression;
}

declare class BabelNodeSwitchCase extends BabelNode {
  type: "SwitchCase";
  test?: BabelNodeExpression;
  consequent: Array<BabelNodeStatement>;
}

declare class BabelNodeSwitchStatement extends BabelNode {
  type: "SwitchStatement";
  discriminant: BabelNodeExpression;
  cases: Array<BabelNodeSwitchCase>;
}

declare class BabelNodeThisExpression extends BabelNode {
  type: "ThisExpression";
}

declare class BabelNodeThrowStatement extends BabelNode {
  type: "ThrowStatement";
  argument: BabelNodeExpression;
}

declare class BabelNodeTryStatement extends BabelNode {
  type: "TryStatement";
  block: BabelNodeBlockStatement;
  handler?: BabelNodeCatchClause;
  finalizer?: BabelNodeBlockStatement;
}

declare class BabelNodeUnaryExpression extends BabelNode {
  type: "UnaryExpression";
  operator: "void" | "throw" | "delete" | "!" | "+" | "-" | "~" | "typeof";
  argument: BabelNodeExpression;
  prefix?: boolean;
}

declare class BabelNodeUpdateExpression extends BabelNode {
  type: "UpdateExpression";
  operator: "++" | "--";
  argument: BabelNodeExpression;
  prefix?: boolean;
}

declare class BabelNodeVariableDeclaration extends BabelNode {
  type: "VariableDeclaration";
  kind: "var" | "let" | "const" | "using" | "await using";
  declarations: Array<BabelNodeVariableDeclarator>;
  declare?: boolean;
}

declare class BabelNodeVariableDeclarator extends BabelNode {
  type: "VariableDeclarator";
  id: BabelNodeLVal;
  init?: BabelNodeExpression;
  definite?: boolean;
}

declare class BabelNodeWhileStatement extends BabelNode {
  type: "WhileStatement";
  test: BabelNodeExpression;
  body: BabelNodeStatement;
}

declare class BabelNodeWithStatement extends BabelNode {
  type: "WithStatement";
  object: BabelNodeExpression;
  body: BabelNodeStatement;
}

declare class BabelNodeAssignmentPattern extends BabelNode {
  type: "AssignmentPattern";
  left: BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeArrayPattern | BabelNodeMemberExpression | BabelNodeTSAsExpression | BabelNodeTSSatisfiesExpression | BabelNodeTSTypeAssertion | BabelNodeTSNonNullExpression;
  right: BabelNodeExpression;
  decorators?: Array<BabelNodeDecorator>;
  optional?: boolean;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
}

declare class BabelNodeArrayPattern extends BabelNode {
  type: "ArrayPattern";
  elements: Array<null | BabelNodePatternLike | BabelNodeLVal>;
  decorators?: Array<BabelNodeDecorator>;
  optional?: boolean;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
}

declare class BabelNodeArrowFunctionExpression extends BabelNode {
  type: "ArrowFunctionExpression";
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>;
  body: BabelNodeBlockStatement | BabelNodeExpression;
  async?: boolean;
  expression: boolean;
  generator?: boolean;
  predicate?: BabelNodeDeclaredPredicate | BabelNodeInferredPredicate;
  returnType?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodeClassBody extends BabelNode {
  type: "ClassBody";
  body: Array<BabelNodeClassMethod | BabelNodeClassPrivateMethod | BabelNodeClassProperty | BabelNodeClassPrivateProperty | BabelNodeClassAccessorProperty | BabelNodeTSDeclareMethod | BabelNodeTSIndexSignature | BabelNodeStaticBlock>;
}

declare class BabelNodeClassExpression extends BabelNode {
  type: "ClassExpression";
  id?: BabelNodeIdentifier;
  superClass?: BabelNodeExpression;
  body: BabelNodeClassBody;
  decorators?: Array<BabelNodeDecorator>;
  mixins?: BabelNodeInterfaceExtends;
  superTypeParameters?: BabelNodeTypeParameterInstantiation | BabelNodeTSTypeParameterInstantiation;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodeClassDeclaration extends BabelNode {
  type: "ClassDeclaration";
  id?: BabelNodeIdentifier;
  superClass?: BabelNodeExpression;
  body: BabelNodeClassBody;
  decorators?: Array<BabelNodeDecorator>;
  abstract?: boolean;
  declare?: boolean;
  mixins?: BabelNodeInterfaceExtends;
  superTypeParameters?: BabelNodeTypeParameterInstantiation | BabelNodeTSTypeParameterInstantiation;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodeExportAllDeclaration extends BabelNode {
  type: "ExportAllDeclaration";
  source: BabelNodeStringLiteral;
  assertions?: Array<BabelNodeImportAttribute>;
  attributes?: Array<BabelNodeImportAttribute>;
  exportKind?: "type" | "value";
}

declare class BabelNodeExportDefaultDeclaration extends BabelNode {
  type: "ExportDefaultDeclaration";
  declaration: BabelNodeTSDeclareFunction | BabelNodeFunctionDeclaration | BabelNodeClassDeclaration | BabelNodeExpression;
  exportKind?: "value";
}

declare class BabelNodeExportNamedDeclaration extends BabelNode {
  type: "ExportNamedDeclaration";
  declaration?: BabelNodeDeclaration;
  specifiers?: Array<BabelNodeExportSpecifier | BabelNodeExportDefaultSpecifier | BabelNodeExportNamespaceSpecifier>;
  source?: BabelNodeStringLiteral;
  assertions?: Array<BabelNodeImportAttribute>;
  attributes?: Array<BabelNodeImportAttribute>;
  exportKind?: "type" | "value";
}

declare class BabelNodeExportSpecifier extends BabelNode {
  type: "ExportSpecifier";
  local: BabelNodeIdentifier;
  exported: BabelNodeIdentifier | BabelNodeStringLiteral;
  exportKind?: "type" | "value";
}

declare class BabelNodeForOfStatement extends BabelNode {
  type: "ForOfStatement";
  left: BabelNodeVariableDeclaration | BabelNodeLVal;
  right: BabelNodeExpression;
  body: BabelNodeStatement;
}

declare class BabelNodeImportDeclaration extends BabelNode {
  type: "ImportDeclaration";
  specifiers: Array<BabelNodeImportSpecifier | BabelNodeImportDefaultSpecifier | BabelNodeImportNamespaceSpecifier>;
  source: BabelNodeStringLiteral;
  assertions?: Array<BabelNodeImportAttribute>;
  attributes?: Array<BabelNodeImportAttribute>;
  importKind?: "type" | "typeof" | "value";
  module?: boolean;
  phase?: "source" | "defer";
}

declare class BabelNodeImportDefaultSpecifier extends BabelNode {
  type: "ImportDefaultSpecifier";
  local: BabelNodeIdentifier;
}

declare class BabelNodeImportNamespaceSpecifier extends BabelNode {
  type: "ImportNamespaceSpecifier";
  local: BabelNodeIdentifier;
}

declare class BabelNodeImportSpecifier extends BabelNode {
  type: "ImportSpecifier";
  local: BabelNodeIdentifier;
  imported: BabelNodeIdentifier | BabelNodeStringLiteral;
  importKind?: "type" | "typeof" | "value";
}

declare class BabelNodeImportExpression extends BabelNode {
  type: "ImportExpression";
  source: BabelNodeExpression;
  options?: BabelNodeExpression;
  phase?: "source" | "defer";
}

declare class BabelNodeMetaProperty extends BabelNode {
  type: "MetaProperty";
  meta: BabelNodeIdentifier;
  property: BabelNodeIdentifier;
}

declare class BabelNodeClassMethod extends BabelNode {
  type: "ClassMethod";
  kind?: "get" | "set" | "method" | "constructor";
  key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression;
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement | BabelNodeTSParameterProperty>;
  body: BabelNodeBlockStatement;
  computed?: boolean;
  generator?: boolean;
  async?: boolean;
  abstract?: boolean;
  access?: "public" | "private" | "protected";
  accessibility?: "public" | "private" | "protected";
  decorators?: Array<BabelNodeDecorator>;
  optional?: boolean;
  override?: boolean;
  returnType?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodeObjectPattern extends BabelNode {
  type: "ObjectPattern";
  properties: Array<BabelNodeRestElement | BabelNodeObjectProperty>;
  decorators?: Array<BabelNodeDecorator>;
  optional?: boolean;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
}

declare class BabelNodeSpreadElement extends BabelNode {
  type: "SpreadElement";
  argument: BabelNodeExpression;
}

declare class BabelNodeSuper extends BabelNode {
  type: "Super";
}

declare class BabelNodeTaggedTemplateExpression extends BabelNode {
  type: "TaggedTemplateExpression";
  tag: BabelNodeExpression;
  quasi: BabelNodeTemplateLiteral;
  typeParameters?: BabelNodeTypeParameterInstantiation | BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeTemplateElement extends BabelNode {
  type: "TemplateElement";
  value: { raw: string, cooked?: string };
  tail?: boolean;
}

declare class BabelNodeTemplateLiteral extends BabelNode {
  type: "TemplateLiteral";
  quasis: Array<BabelNodeTemplateElement>;
  expressions: Array<BabelNodeExpression | BabelNodeTSType>;
}

declare class BabelNodeYieldExpression extends BabelNode {
  type: "YieldExpression";
  argument?: BabelNodeExpression;
  delegate?: boolean;
}

declare class BabelNodeAwaitExpression extends BabelNode {
  type: "AwaitExpression";
  argument: BabelNodeExpression;
}

declare class BabelNodeImport extends BabelNode {
  type: "Import";
}

declare class BabelNodeBigIntLiteral extends BabelNode {
  type: "BigIntLiteral";
  value: string;
}

declare class BabelNodeExportNamespaceSpecifier extends BabelNode {
  type: "ExportNamespaceSpecifier";
  exported: BabelNodeIdentifier;
}

declare class BabelNodeOptionalMemberExpression extends BabelNode {
  type: "OptionalMemberExpression";
  object: BabelNodeExpression;
  property: BabelNodeExpression | BabelNodeIdentifier;
  computed?: boolean;
  optional: boolean;
}

declare class BabelNodeOptionalCallExpression extends BabelNode {
  type: "OptionalCallExpression";
  callee: BabelNodeExpression;
  arguments: Array<BabelNodeExpression | BabelNodeSpreadElement | BabelNodeJSXNamespacedName | BabelNodeArgumentPlaceholder>;
  optional: boolean;
  typeArguments?: BabelNodeTypeParameterInstantiation;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeClassProperty extends BabelNode {
  type: "ClassProperty";
  key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression;
  value?: BabelNodeExpression;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  decorators?: Array<BabelNodeDecorator>;
  computed?: boolean;
  abstract?: boolean;
  accessibility?: "public" | "private" | "protected";
  declare?: boolean;
  definite?: boolean;
  optional?: boolean;
  override?: boolean;
  readonly?: boolean;
  variance?: BabelNodeVariance;
}

declare class BabelNodeClassAccessorProperty extends BabelNode {
  type: "ClassAccessorProperty";
  key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression | BabelNodePrivateName;
  value?: BabelNodeExpression;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  decorators?: Array<BabelNodeDecorator>;
  computed?: boolean;
  abstract?: boolean;
  accessibility?: "public" | "private" | "protected";
  declare?: boolean;
  definite?: boolean;
  optional?: boolean;
  override?: boolean;
  readonly?: boolean;
  variance?: BabelNodeVariance;
}

declare class BabelNodeClassPrivateProperty extends BabelNode {
  type: "ClassPrivateProperty";
  key: BabelNodePrivateName;
  value?: BabelNodeExpression;
  decorators?: Array<BabelNodeDecorator>;
  definite?: boolean;
  readonly?: boolean;
  typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  variance?: BabelNodeVariance;
}

declare class BabelNodeClassPrivateMethod extends BabelNode {
  type: "ClassPrivateMethod";
  kind?: "get" | "set" | "method";
  key: BabelNodePrivateName;
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement | BabelNodeTSParameterProperty>;
  body: BabelNodeBlockStatement;
  abstract?: boolean;
  access?: "public" | "private" | "protected";
  accessibility?: "public" | "private" | "protected";
  async?: boolean;
  computed?: boolean;
  decorators?: Array<BabelNodeDecorator>;
  generator?: boolean;
  optional?: boolean;
  override?: boolean;
  returnType?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop;
  typeParameters?: BabelNodeTypeParameterDeclaration | BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
}

declare class BabelNodePrivateName extends BabelNode {
  type: "PrivateName";
  id: BabelNodeIdentifier;
}

declare class BabelNodeStaticBlock extends BabelNode {
  type: "StaticBlock";
  body: Array<BabelNodeStatement>;
}

declare class BabelNodeAnyTypeAnnotation extends BabelNode {
  type: "AnyTypeAnnotation";
}

declare class BabelNodeArrayTypeAnnotation extends BabelNode {
  type: "ArrayTypeAnnotation";
  elementType: BabelNodeFlowType;
}

declare class BabelNodeBooleanTypeAnnotation extends BabelNode {
  type: "BooleanTypeAnnotation";
}

declare class BabelNodeBooleanLiteralTypeAnnotation extends BabelNode {
  type: "BooleanLiteralTypeAnnotation";
  value: boolean;
}

declare class BabelNodeNullLiteralTypeAnnotation extends BabelNode {
  type: "NullLiteralTypeAnnotation";
}

declare class BabelNodeClassImplements extends BabelNode {
  type: "ClassImplements";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterInstantiation;
}

declare class BabelNodeDeclareClass extends BabelNode {
  type: "DeclareClass";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterDeclaration;
  body: BabelNodeObjectTypeAnnotation;
  mixins?: Array<BabelNodeInterfaceExtends>;
}

declare class BabelNodeDeclareFunction extends BabelNode {
  type: "DeclareFunction";
  id: BabelNodeIdentifier;
  predicate?: BabelNodeDeclaredPredicate;
}

declare class BabelNodeDeclareInterface extends BabelNode {
  type: "DeclareInterface";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterDeclaration;
  body: BabelNodeObjectTypeAnnotation;
}

declare class BabelNodeDeclareModule extends BabelNode {
  type: "DeclareModule";
  id: BabelNodeIdentifier | BabelNodeStringLiteral;
  body: BabelNodeBlockStatement;
  kind?: "CommonJS" | "ES";
}

declare class BabelNodeDeclareModuleExports extends BabelNode {
  type: "DeclareModuleExports";
  typeAnnotation: BabelNodeTypeAnnotation;
}

declare class BabelNodeDeclareTypeAlias extends BabelNode {
  type: "DeclareTypeAlias";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterDeclaration;
  right: BabelNodeFlowType;
}

declare class BabelNodeDeclareOpaqueType extends BabelNode {
  type: "DeclareOpaqueType";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterDeclaration;
  supertype?: BabelNodeFlowType;
  impltype?: BabelNodeFlowType;
}

declare class BabelNodeDeclareVariable extends BabelNode {
  type: "DeclareVariable";
  id: BabelNodeIdentifier;
}

declare class BabelNodeDeclareExportDeclaration extends BabelNode {
  type: "DeclareExportDeclaration";
  declaration?: BabelNodeFlow;
  specifiers?: Array<BabelNodeExportSpecifier | BabelNodeExportNamespaceSpecifier>;
  source?: BabelNodeStringLiteral;
}

declare class BabelNodeDeclareExportAllDeclaration extends BabelNode {
  type: "DeclareExportAllDeclaration";
  source: BabelNodeStringLiteral;
  exportKind?: "type" | "value";
}

declare class BabelNodeDeclaredPredicate extends BabelNode {
  type: "DeclaredPredicate";
  value: BabelNodeFlow;
}

declare class BabelNodeExistsTypeAnnotation extends BabelNode {
  type: "ExistsTypeAnnotation";
}

declare class BabelNodeFunctionTypeAnnotation extends BabelNode {
  type: "FunctionTypeAnnotation";
  typeParameters?: BabelNodeTypeParameterDeclaration;
  params: Array<BabelNodeFunctionTypeParam>;
  rest?: BabelNodeFunctionTypeParam;
  returnType: BabelNodeFlowType;
}

declare class BabelNodeFunctionTypeParam extends BabelNode {
  type: "FunctionTypeParam";
  name?: BabelNodeIdentifier;
  typeAnnotation: BabelNodeFlowType;
  optional?: boolean;
}

declare class BabelNodeGenericTypeAnnotation extends BabelNode {
  type: "GenericTypeAnnotation";
  id: BabelNodeIdentifier | BabelNodeQualifiedTypeIdentifier;
  typeParameters?: BabelNodeTypeParameterInstantiation;
}

declare class BabelNodeInferredPredicate extends BabelNode {
  type: "InferredPredicate";
}

declare class BabelNodeInterfaceExtends extends BabelNode {
  type: "InterfaceExtends";
  id: BabelNodeIdentifier | BabelNodeQualifiedTypeIdentifier;
  typeParameters?: BabelNodeTypeParameterInstantiation;
}

declare class BabelNodeInterfaceDeclaration extends BabelNode {
  type: "InterfaceDeclaration";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterDeclaration;
  body: BabelNodeObjectTypeAnnotation;
}

declare class BabelNodeInterfaceTypeAnnotation extends BabelNode {
  type: "InterfaceTypeAnnotation";
  body: BabelNodeObjectTypeAnnotation;
}

declare class BabelNodeIntersectionTypeAnnotation extends BabelNode {
  type: "IntersectionTypeAnnotation";
  types: Array<BabelNodeFlowType>;
}

declare class BabelNodeMixedTypeAnnotation extends BabelNode {
  type: "MixedTypeAnnotation";
}

declare class BabelNodeEmptyTypeAnnotation extends BabelNode {
  type: "EmptyTypeAnnotation";
}

declare class BabelNodeNullableTypeAnnotation extends BabelNode {
  type: "NullableTypeAnnotation";
  typeAnnotation: BabelNodeFlowType;
}

declare class BabelNodeNumberLiteralTypeAnnotation extends BabelNode {
  type: "NumberLiteralTypeAnnotation";
  value: number;
}

declare class BabelNodeNumberTypeAnnotation extends BabelNode {
  type: "NumberTypeAnnotation";
}

declare class BabelNodeObjectTypeAnnotation extends BabelNode {
  type: "ObjectTypeAnnotation";
  properties: Array<BabelNodeObjectTypeProperty | BabelNodeObjectTypeSpreadProperty>;
  indexers?: Array<BabelNodeObjectTypeIndexer>;
  callProperties?: Array<BabelNodeObjectTypeCallProperty>;
  internalSlots?: Array<BabelNodeObjectTypeInternalSlot>;
  exact?: boolean;
  inexact?: boolean;
}

declare class BabelNodeObjectTypeInternalSlot extends BabelNode {
  type: "ObjectTypeInternalSlot";
  id: BabelNodeIdentifier;
  value: BabelNodeFlowType;
  optional: boolean;
  method: boolean;
}

declare class BabelNodeObjectTypeCallProperty extends BabelNode {
  type: "ObjectTypeCallProperty";
  value: BabelNodeFlowType;
}

declare class BabelNodeObjectTypeIndexer extends BabelNode {
  type: "ObjectTypeIndexer";
  id?: BabelNodeIdentifier;
  key: BabelNodeFlowType;
  value: BabelNodeFlowType;
  variance?: BabelNodeVariance;
}

declare class BabelNodeObjectTypeProperty extends BabelNode {
  type: "ObjectTypeProperty";
  key: BabelNodeIdentifier | BabelNodeStringLiteral;
  value: BabelNodeFlowType;
  variance?: BabelNodeVariance;
  kind: "init" | "get" | "set";
  method: boolean;
  optional: boolean;
  proto: boolean;
}

declare class BabelNodeObjectTypeSpreadProperty extends BabelNode {
  type: "ObjectTypeSpreadProperty";
  argument: BabelNodeFlowType;
}

declare class BabelNodeOpaqueType extends BabelNode {
  type: "OpaqueType";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterDeclaration;
  supertype?: BabelNodeFlowType;
  impltype: BabelNodeFlowType;
}

declare class BabelNodeQualifiedTypeIdentifier extends BabelNode {
  type: "QualifiedTypeIdentifier";
  id: BabelNodeIdentifier;
  qualification: BabelNodeIdentifier | BabelNodeQualifiedTypeIdentifier;
}

declare class BabelNodeStringLiteralTypeAnnotation extends BabelNode {
  type: "StringLiteralTypeAnnotation";
  value: string;
}

declare class BabelNodeStringTypeAnnotation extends BabelNode {
  type: "StringTypeAnnotation";
}

declare class BabelNodeSymbolTypeAnnotation extends BabelNode {
  type: "SymbolTypeAnnotation";
}

declare class BabelNodeThisTypeAnnotation extends BabelNode {
  type: "ThisTypeAnnotation";
}

declare class BabelNodeTupleTypeAnnotation extends BabelNode {
  type: "TupleTypeAnnotation";
  types: Array<BabelNodeFlowType>;
}

declare class BabelNodeTypeofTypeAnnotation extends BabelNode {
  type: "TypeofTypeAnnotation";
  argument: BabelNodeFlowType;
}

declare class BabelNodeTypeAlias extends BabelNode {
  type: "TypeAlias";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTypeParameterDeclaration;
  right: BabelNodeFlowType;
}

declare class BabelNodeTypeAnnotation extends BabelNode {
  type: "TypeAnnotation";
  typeAnnotation: BabelNodeFlowType;
}

declare class BabelNodeTypeCastExpression extends BabelNode {
  type: "TypeCastExpression";
  expression: BabelNodeExpression;
  typeAnnotation: BabelNodeTypeAnnotation;
}

declare class BabelNodeTypeParameter extends BabelNode {
  type: "TypeParameter";
  bound?: BabelNodeTypeAnnotation;
  variance?: BabelNodeVariance;
  name: string;
}

declare class BabelNodeTypeParameterDeclaration extends BabelNode {
  type: "TypeParameterDeclaration";
  params: Array<BabelNodeTypeParameter>;
}

declare class BabelNodeTypeParameterInstantiation extends BabelNode {
  type: "TypeParameterInstantiation";
  params: Array<BabelNodeFlowType>;
}

declare class BabelNodeUnionTypeAnnotation extends BabelNode {
  type: "UnionTypeAnnotation";
  types: Array<BabelNodeFlowType>;
}

declare class BabelNodeVariance extends BabelNode {
  type: "Variance";
  kind: "minus" | "plus";
}

declare class BabelNodeVoidTypeAnnotation extends BabelNode {
  type: "VoidTypeAnnotation";
}

declare class BabelNodeEnumDeclaration extends BabelNode {
  type: "EnumDeclaration";
  id: BabelNodeIdentifier;
  body: BabelNodeEnumBooleanBody | BabelNodeEnumNumberBody | BabelNodeEnumStringBody | BabelNodeEnumSymbolBody;
}

declare class BabelNodeEnumBooleanBody extends BabelNode {
  type: "EnumBooleanBody";
  members: Array<BabelNodeEnumBooleanMember>;
  explicitType: boolean;
  hasUnknownMembers: boolean;
}

declare class BabelNodeEnumNumberBody extends BabelNode {
  type: "EnumNumberBody";
  members: Array<BabelNodeEnumNumberMember>;
  explicitType: boolean;
  hasUnknownMembers: boolean;
}

declare class BabelNodeEnumStringBody extends BabelNode {
  type: "EnumStringBody";
  members: Array<BabelNodeEnumStringMember | BabelNodeEnumDefaultedMember>;
  explicitType: boolean;
  hasUnknownMembers: boolean;
}

declare class BabelNodeEnumSymbolBody extends BabelNode {
  type: "EnumSymbolBody";
  members: Array<BabelNodeEnumDefaultedMember>;
  hasUnknownMembers: boolean;
}

declare class BabelNodeEnumBooleanMember extends BabelNode {
  type: "EnumBooleanMember";
  id: BabelNodeIdentifier;
  init: BabelNodeBooleanLiteral;
}

declare class BabelNodeEnumNumberMember extends BabelNode {
  type: "EnumNumberMember";
  id: BabelNodeIdentifier;
  init: BabelNodeNumericLiteral;
}

declare class BabelNodeEnumStringMember extends BabelNode {
  type: "EnumStringMember";
  id: BabelNodeIdentifier;
  init: BabelNodeStringLiteral;
}

declare class BabelNodeEnumDefaultedMember extends BabelNode {
  type: "EnumDefaultedMember";
  id: BabelNodeIdentifier;
}

declare class BabelNodeIndexedAccessType extends BabelNode {
  type: "IndexedAccessType";
  objectType: BabelNodeFlowType;
  indexType: BabelNodeFlowType;
}

declare class BabelNodeOptionalIndexedAccessType extends BabelNode {
  type: "OptionalIndexedAccessType";
  objectType: BabelNodeFlowType;
  indexType: BabelNodeFlowType;
  optional: boolean;
}

declare class BabelNodeJSXAttribute extends BabelNode {
  type: "JSXAttribute";
  name: BabelNodeJSXIdentifier | BabelNodeJSXNamespacedName;
  value?: BabelNodeJSXElement | BabelNodeJSXFragment | BabelNodeStringLiteral | BabelNodeJSXExpressionContainer;
}

declare class BabelNodeJSXClosingElement extends BabelNode {
  type: "JSXClosingElement";
  name: BabelNodeJSXIdentifier | BabelNodeJSXMemberExpression | BabelNodeJSXNamespacedName;
}

declare class BabelNodeJSXElement extends BabelNode {
  type: "JSXElement";
  openingElement: BabelNodeJSXOpeningElement;
  closingElement?: BabelNodeJSXClosingElement;
  children: Array<BabelNodeJSXText | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXElement | BabelNodeJSXFragment>;
  selfClosing?: boolean;
}

declare class BabelNodeJSXEmptyExpression extends BabelNode {
  type: "JSXEmptyExpression";
}

declare class BabelNodeJSXExpressionContainer extends BabelNode {
  type: "JSXExpressionContainer";
  expression: BabelNodeExpression | BabelNodeJSXEmptyExpression;
}

declare class BabelNodeJSXSpreadChild extends BabelNode {
  type: "JSXSpreadChild";
  expression: BabelNodeExpression;
}

declare class BabelNodeJSXIdentifier extends BabelNode {
  type: "JSXIdentifier";
  name: string;
}

declare class BabelNodeJSXMemberExpression extends BabelNode {
  type: "JSXMemberExpression";
  object: BabelNodeJSXMemberExpression | BabelNodeJSXIdentifier;
  property: BabelNodeJSXIdentifier;
}

declare class BabelNodeJSXNamespacedName extends BabelNode {
  type: "JSXNamespacedName";
  namespace: BabelNodeJSXIdentifier;
  name: BabelNodeJSXIdentifier;
}

declare class BabelNodeJSXOpeningElement extends BabelNode {
  type: "JSXOpeningElement";
  name: BabelNodeJSXIdentifier | BabelNodeJSXMemberExpression | BabelNodeJSXNamespacedName;
  attributes: Array<BabelNodeJSXAttribute | BabelNodeJSXSpreadAttribute>;
  selfClosing?: boolean;
  typeParameters?: BabelNodeTypeParameterInstantiation | BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeJSXSpreadAttribute extends BabelNode {
  type: "JSXSpreadAttribute";
  argument: BabelNodeExpression;
}

declare class BabelNodeJSXText extends BabelNode {
  type: "JSXText";
  value: string;
}

declare class BabelNodeJSXFragment extends BabelNode {
  type: "JSXFragment";
  openingFragment: BabelNodeJSXOpeningFragment;
  closingFragment: BabelNodeJSXClosingFragment;
  children: Array<BabelNodeJSXText | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXElement | BabelNodeJSXFragment>;
}

declare class BabelNodeJSXOpeningFragment extends BabelNode {
  type: "JSXOpeningFragment";
}

declare class BabelNodeJSXClosingFragment extends BabelNode {
  type: "JSXClosingFragment";
}

declare class BabelNodeNoop extends BabelNode {
  type: "Noop";
}

declare class BabelNodePlaceholder extends BabelNode {
  type: "Placeholder";
  expectedNode: "Identifier" | "StringLiteral" | "Expression" | "Statement" | "Declaration" | "BlockStatement" | "ClassBody" | "Pattern";
  name: BabelNodeIdentifier;
}

declare class BabelNodeV8IntrinsicIdentifier extends BabelNode {
  type: "V8IntrinsicIdentifier";
  name: string;
}

declare class BabelNodeArgumentPlaceholder extends BabelNode {
  type: "ArgumentPlaceholder";
}

declare class BabelNodeBindExpression extends BabelNode {
  type: "BindExpression";
  object: BabelNodeExpression;
  callee: BabelNodeExpression;
}

declare class BabelNodeImportAttribute extends BabelNode {
  type: "ImportAttribute";
  key: BabelNodeIdentifier | BabelNodeStringLiteral;
  value: BabelNodeStringLiteral;
}

declare class BabelNodeDecorator extends BabelNode {
  type: "Decorator";
  expression: BabelNodeExpression;
}

declare class BabelNodeDoExpression extends BabelNode {
  type: "DoExpression";
  body: BabelNodeBlockStatement;
  async?: boolean;
}

declare class BabelNodeExportDefaultSpecifier extends BabelNode {
  type: "ExportDefaultSpecifier";
  exported: BabelNodeIdentifier;
}

declare class BabelNodeRecordExpression extends BabelNode {
  type: "RecordExpression";
  properties: Array<BabelNodeObjectProperty | BabelNodeSpreadElement>;
}

declare class BabelNodeTupleExpression extends BabelNode {
  type: "TupleExpression";
  elements?: Array<BabelNodeExpression | BabelNodeSpreadElement>;
}

declare class BabelNodeDecimalLiteral extends BabelNode {
  type: "DecimalLiteral";
  value: string;
}

declare class BabelNodeModuleExpression extends BabelNode {
  type: "ModuleExpression";
  body: BabelNodeProgram;
}

declare class BabelNodeTopicReference extends BabelNode {
  type: "TopicReference";
}

declare class BabelNodePipelineTopicExpression extends BabelNode {
  type: "PipelineTopicExpression";
  expression: BabelNodeExpression;
}

declare class BabelNodePipelineBareFunction extends BabelNode {
  type: "PipelineBareFunction";
  callee: BabelNodeExpression;
}

declare class BabelNodePipelinePrimaryTopicReference extends BabelNode {
  type: "PipelinePrimaryTopicReference";
}

declare class BabelNodeTSParameterProperty extends BabelNode {
  type: "TSParameterProperty";
  parameter: BabelNodeIdentifier | BabelNodeAssignmentPattern;
  accessibility?: "public" | "private" | "protected";
  decorators?: Array<BabelNodeDecorator>;
  override?: boolean;
  readonly?: boolean;
}

declare class BabelNodeTSDeclareFunction extends BabelNode {
  type: "TSDeclareFunction";
  id?: BabelNodeIdentifier;
  typeParameters?: BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>;
  returnType?: BabelNodeTSTypeAnnotation | BabelNodeNoop;
  async?: boolean;
  declare?: boolean;
  generator?: boolean;
}

declare class BabelNodeTSDeclareMethod extends BabelNode {
  type: "TSDeclareMethod";
  decorators?: Array<BabelNodeDecorator>;
  key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression;
  typeParameters?: BabelNodeTSTypeParameterDeclaration | BabelNodeNoop;
  params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement | BabelNodeTSParameterProperty>;
  returnType?: BabelNodeTSTypeAnnotation | BabelNodeNoop;
  abstract?: boolean;
  access?: "public" | "private" | "protected";
  accessibility?: "public" | "private" | "protected";
  async?: boolean;
  computed?: boolean;
  generator?: boolean;
  kind?: "get" | "set" | "method" | "constructor";
  optional?: boolean;
  override?: boolean;
}

declare class BabelNodeTSQualifiedName extends BabelNode {
  type: "TSQualifiedName";
  left: BabelNodeTSEntityName;
  right: BabelNodeIdentifier;
}

declare class BabelNodeTSCallSignatureDeclaration extends BabelNode {
  type: "TSCallSignatureDeclaration";
  typeParameters?: BabelNodeTSTypeParameterDeclaration;
  parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
}

declare class BabelNodeTSConstructSignatureDeclaration extends BabelNode {
  type: "TSConstructSignatureDeclaration";
  typeParameters?: BabelNodeTSTypeParameterDeclaration;
  parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
}

declare class BabelNodeTSPropertySignature extends BabelNode {
  type: "TSPropertySignature";
  key: BabelNodeExpression;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
  computed?: boolean;
  kind: "get" | "set";
  optional?: boolean;
  readonly?: boolean;
}

declare class BabelNodeTSMethodSignature extends BabelNode {
  type: "TSMethodSignature";
  key: BabelNodeExpression;
  typeParameters?: BabelNodeTSTypeParameterDeclaration;
  parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
  computed?: boolean;
  kind: "method" | "get" | "set";
  optional?: boolean;
}

declare class BabelNodeTSIndexSignature extends BabelNode {
  type: "TSIndexSignature";
  parameters: Array<BabelNodeIdentifier>;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
  readonly?: boolean;
}

declare class BabelNodeTSAnyKeyword extends BabelNode {
  type: "TSAnyKeyword";
}

declare class BabelNodeTSBooleanKeyword extends BabelNode {
  type: "TSBooleanKeyword";
}

declare class BabelNodeTSBigIntKeyword extends BabelNode {
  type: "TSBigIntKeyword";
}

declare class BabelNodeTSIntrinsicKeyword extends BabelNode {
  type: "TSIntrinsicKeyword";
}

declare class BabelNodeTSNeverKeyword extends BabelNode {
  type: "TSNeverKeyword";
}

declare class BabelNodeTSNullKeyword extends BabelNode {
  type: "TSNullKeyword";
}

declare class BabelNodeTSNumberKeyword extends BabelNode {
  type: "TSNumberKeyword";
}

declare class BabelNodeTSObjectKeyword extends BabelNode {
  type: "TSObjectKeyword";
}

declare class BabelNodeTSStringKeyword extends BabelNode {
  type: "TSStringKeyword";
}

declare class BabelNodeTSSymbolKeyword extends BabelNode {
  type: "TSSymbolKeyword";
}

declare class BabelNodeTSUndefinedKeyword extends BabelNode {
  type: "TSUndefinedKeyword";
}

declare class BabelNodeTSUnknownKeyword extends BabelNode {
  type: "TSUnknownKeyword";
}

declare class BabelNodeTSVoidKeyword extends BabelNode {
  type: "TSVoidKeyword";
}

declare class BabelNodeTSThisType extends BabelNode {
  type: "TSThisType";
}

declare class BabelNodeTSFunctionType extends BabelNode {
  type: "TSFunctionType";
  typeParameters?: BabelNodeTSTypeParameterDeclaration;
  parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
}

declare class BabelNodeTSConstructorType extends BabelNode {
  type: "TSConstructorType";
  typeParameters?: BabelNodeTSTypeParameterDeclaration;
  parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
  abstract?: boolean;
}

declare class BabelNodeTSTypeReference extends BabelNode {
  type: "TSTypeReference";
  typeName: BabelNodeTSEntityName;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeTSTypePredicate extends BabelNode {
  type: "TSTypePredicate";
  parameterName: BabelNodeIdentifier | BabelNodeTSThisType;
  typeAnnotation?: BabelNodeTSTypeAnnotation;
  asserts?: boolean;
}

declare class BabelNodeTSTypeQuery extends BabelNode {
  type: "TSTypeQuery";
  exprName: BabelNodeTSEntityName | BabelNodeTSImportType;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeTSTypeLiteral extends BabelNode {
  type: "TSTypeLiteral";
  members: Array<BabelNodeTSTypeElement>;
}

declare class BabelNodeTSArrayType extends BabelNode {
  type: "TSArrayType";
  elementType: BabelNodeTSType;
}

declare class BabelNodeTSTupleType extends BabelNode {
  type: "TSTupleType";
  elementTypes: Array<BabelNodeTSType | BabelNodeTSNamedTupleMember>;
}

declare class BabelNodeTSOptionalType extends BabelNode {
  type: "TSOptionalType";
  typeAnnotation: BabelNodeTSType;
}

declare class BabelNodeTSRestType extends BabelNode {
  type: "TSRestType";
  typeAnnotation: BabelNodeTSType;
}

declare class BabelNodeTSNamedTupleMember extends BabelNode {
  type: "TSNamedTupleMember";
  label: BabelNodeIdentifier;
  elementType: BabelNodeTSType;
  optional?: boolean;
}

declare class BabelNodeTSUnionType extends BabelNode {
  type: "TSUnionType";
  types: Array<BabelNodeTSType>;
}

declare class BabelNodeTSIntersectionType extends BabelNode {
  type: "TSIntersectionType";
  types: Array<BabelNodeTSType>;
}

declare class BabelNodeTSConditionalType extends BabelNode {
  type: "TSConditionalType";
  checkType: BabelNodeTSType;
  extendsType: BabelNodeTSType;
  trueType: BabelNodeTSType;
  falseType: BabelNodeTSType;
}

declare class BabelNodeTSInferType extends BabelNode {
  type: "TSInferType";
  typeParameter: BabelNodeTSTypeParameter;
}

declare class BabelNodeTSParenthesizedType extends BabelNode {
  type: "TSParenthesizedType";
  typeAnnotation: BabelNodeTSType;
}

declare class BabelNodeTSTypeOperator extends BabelNode {
  type: "TSTypeOperator";
  typeAnnotation: BabelNodeTSType;
  operator: string;
}

declare class BabelNodeTSIndexedAccessType extends BabelNode {
  type: "TSIndexedAccessType";
  objectType: BabelNodeTSType;
  indexType: BabelNodeTSType;
}

declare class BabelNodeTSMappedType extends BabelNode {
  type: "TSMappedType";
  typeParameter: BabelNodeTSTypeParameter;
  typeAnnotation?: BabelNodeTSType;
  nameType?: BabelNodeTSType;
  optional?: true | false | "+" | "-";
  readonly?: true | false | "+" | "-";
}

declare class BabelNodeTSLiteralType extends BabelNode {
  type: "TSLiteralType";
  literal: BabelNodeNumericLiteral | BabelNodeStringLiteral | BabelNodeBooleanLiteral | BabelNodeBigIntLiteral | BabelNodeTemplateLiteral | BabelNodeUnaryExpression;
}

declare class BabelNodeTSExpressionWithTypeArguments extends BabelNode {
  type: "TSExpressionWithTypeArguments";
  expression: BabelNodeTSEntityName;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeTSInterfaceDeclaration extends BabelNode {
  type: "TSInterfaceDeclaration";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTSTypeParameterDeclaration;
  body: BabelNodeTSInterfaceBody;
  declare?: boolean;
}

declare class BabelNodeTSInterfaceBody extends BabelNode {
  type: "TSInterfaceBody";
  body: Array<BabelNodeTSTypeElement>;
}

declare class BabelNodeTSTypeAliasDeclaration extends BabelNode {
  type: "TSTypeAliasDeclaration";
  id: BabelNodeIdentifier;
  typeParameters?: BabelNodeTSTypeParameterDeclaration;
  typeAnnotation: BabelNodeTSType;
  declare?: boolean;
}

declare class BabelNodeTSInstantiationExpression extends BabelNode {
  type: "TSInstantiationExpression";
  expression: BabelNodeExpression;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
}

declare class BabelNodeTSAsExpression extends BabelNode {
  type: "TSAsExpression";
  expression: BabelNodeExpression;
  typeAnnotation: BabelNodeTSType;
}

declare class BabelNodeTSSatisfiesExpression extends BabelNode {
  type: "TSSatisfiesExpression";
  expression: BabelNodeExpression;
  typeAnnotation: BabelNodeTSType;
}

declare class BabelNodeTSTypeAssertion extends BabelNode {
  type: "TSTypeAssertion";
  typeAnnotation: BabelNodeTSType;
  expression: BabelNodeExpression;
}

declare class BabelNodeTSEnumDeclaration extends BabelNode {
  type: "TSEnumDeclaration";
  id: BabelNodeIdentifier;
  members: Array<BabelNodeTSEnumMember>;
  declare?: boolean;
  initializer?: BabelNodeExpression;
}

declare class BabelNodeTSEnumMember extends BabelNode {
  type: "TSEnumMember";
  id: BabelNodeIdentifier | BabelNodeStringLiteral;
  initializer?: BabelNodeExpression;
}

declare class BabelNodeTSModuleDeclaration extends BabelNode {
  type: "TSModuleDeclaration";
  id: BabelNodeIdentifier | BabelNodeStringLiteral;
  body: BabelNodeTSModuleBlock | BabelNodeTSModuleDeclaration;
  declare?: boolean;
  global?: boolean;
}

declare class BabelNodeTSModuleBlock extends BabelNode {
  type: "TSModuleBlock";
  body: Array<BabelNodeStatement>;
}

declare class BabelNodeTSImportType extends BabelNode {
  type: "TSImportType";
  argument: BabelNodeStringLiteral;
  qualifier?: BabelNodeTSEntityName;
  typeParameters?: BabelNodeTSTypeParameterInstantiation;
  options?: BabelNodeExpression;
}

declare class BabelNodeTSImportEqualsDeclaration extends BabelNode {
  type: "TSImportEqualsDeclaration";
  id: BabelNodeIdentifier;
  moduleReference: BabelNodeTSEntityName | BabelNodeTSExternalModuleReference;
  importKind?: "type" | "value";
  isExport: boolean;
}

declare class BabelNodeTSExternalModuleReference extends BabelNode {
  type: "TSExternalModuleReference";
  expression: BabelNodeStringLiteral;
}

declare class BabelNodeTSNonNullExpression extends BabelNode {
  type: "TSNonNullExpression";
  expression: BabelNodeExpression;
}

declare class BabelNodeTSExportAssignment extends BabelNode {
  type: "TSExportAssignment";
  expression: BabelNodeExpression;
}

declare class BabelNodeTSNamespaceExportDeclaration extends BabelNode {
  type: "TSNamespaceExportDeclaration";
  id: BabelNodeIdentifier;
}

declare class BabelNodeTSTypeAnnotation extends BabelNode {
  type: "TSTypeAnnotation";
  typeAnnotation: BabelNodeTSType;
}

declare class BabelNodeTSTypeParameterInstantiation extends BabelNode {
  type: "TSTypeParameterInstantiation";
  params: Array<BabelNodeTSType>;
}

declare class BabelNodeTSTypeParameterDeclaration extends BabelNode {
  type: "TSTypeParameterDeclaration";
  params: Array<BabelNodeTSTypeParameter>;
}

declare class BabelNodeTSTypeParameter extends BabelNode {
  type: "TSTypeParameter";
  constraint?: BabelNodeTSType;
  name: string;
  out?: boolean;
}

type BabelNodeStandardized = BabelNodeArrayExpression | BabelNodeAssignmentExpression | BabelNodeBinaryExpression | BabelNodeInterpreterDirective | BabelNodeDirective | BabelNodeDirectiveLiteral | BabelNodeBlockStatement | BabelNodeBreakStatement | BabelNodeCallExpression | BabelNodeCatchClause | BabelNodeConditionalExpression | BabelNodeContinueStatement | BabelNodeDebuggerStatement | BabelNodeDoWhileStatement | BabelNodeEmptyStatement | BabelNodeExpressionStatement | BabelNodeFile | BabelNodeForInStatement | BabelNodeForStatement | BabelNodeFunctionDeclaration | BabelNodeFunctionExpression | BabelNodeIdentifier | BabelNodeIfStatement | BabelNodeLabeledStatement | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeNullLiteral | BabelNodeBooleanLiteral | BabelNodeRegExpLiteral | BabelNodeLogicalExpression | BabelNodeMemberExpression | BabelNodeNewExpression | BabelNodeProgram | BabelNodeObjectExpression | BabelNodeObjectMethod | BabelNodeObjectProperty | BabelNodeRestElement | BabelNodeReturnStatement | BabelNodeSequenceExpression | BabelNodeParenthesizedExpression | BabelNodeSwitchCase | BabelNodeSwitchStatement | BabelNodeThisExpression | BabelNodeThrowStatement | BabelNodeTryStatement | BabelNodeUnaryExpression | BabelNodeUpdateExpression | BabelNodeVariableDeclaration | BabelNodeVariableDeclarator | BabelNodeWhileStatement | BabelNodeWithStatement | BabelNodeAssignmentPattern | BabelNodeArrayPattern | BabelNodeArrowFunctionExpression | BabelNodeClassBody | BabelNodeClassExpression | BabelNodeClassDeclaration | BabelNodeExportAllDeclaration | BabelNodeExportDefaultDeclaration | BabelNodeExportNamedDeclaration | BabelNodeExportSpecifier | BabelNodeForOfStatement | BabelNodeImportDeclaration | BabelNodeImportDefaultSpecifier | BabelNodeImportNamespaceSpecifier | BabelNodeImportSpecifier | BabelNodeImportExpression | BabelNodeMetaProperty | BabelNodeClassMethod | BabelNodeObjectPattern | BabelNodeSpreadElement | BabelNodeSuper | BabelNodeTaggedTemplateExpression | BabelNodeTemplateElement | BabelNodeTemplateLiteral | BabelNodeYieldExpression | BabelNodeAwaitExpression | BabelNodeImport | BabelNodeBigIntLiteral | BabelNodeExportNamespaceSpecifier | BabelNodeOptionalMemberExpression | BabelNodeOptionalCallExpression | BabelNodeClassProperty | BabelNodeClassAccessorProperty | BabelNodeClassPrivateProperty | BabelNodeClassPrivateMethod | BabelNodePrivateName | BabelNodeStaticBlock;
type BabelNodeExpression = BabelNodeArrayExpression | BabelNodeAssignmentExpression | BabelNodeBinaryExpression | BabelNodeCallExpression | BabelNodeConditionalExpression | BabelNodeFunctionExpression | BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeNullLiteral | BabelNodeBooleanLiteral | BabelNodeRegExpLiteral | BabelNodeLogicalExpression | BabelNodeMemberExpression | BabelNodeNewExpression | BabelNodeObjectExpression | BabelNodeSequenceExpression | BabelNodeParenthesizedExpression | BabelNodeThisExpression | BabelNodeUnaryExpression | BabelNodeUpdateExpression | BabelNodeArrowFunctionExpression | BabelNodeClassExpression | BabelNodeImportExpression | BabelNodeMetaProperty | BabelNodeSuper | BabelNodeTaggedTemplateExpression | BabelNodeTemplateLiteral | BabelNodeYieldExpression | BabelNodeAwaitExpression | BabelNodeImport | BabelNodeBigIntLiteral | BabelNodeOptionalMemberExpression | BabelNodeOptionalCallExpression | BabelNodeTypeCastExpression | BabelNodeJSXElement | BabelNodeJSXFragment | BabelNodeBindExpression | BabelNodeDoExpression | BabelNodeRecordExpression | BabelNodeTupleExpression | BabelNodeDecimalLiteral | BabelNodeModuleExpression | BabelNodeTopicReference | BabelNodePipelineTopicExpression | BabelNodePipelineBareFunction | BabelNodePipelinePrimaryTopicReference | BabelNodeTSInstantiationExpression | BabelNodeTSAsExpression | BabelNodeTSSatisfiesExpression | BabelNodeTSTypeAssertion | BabelNodeTSNonNullExpression;
type BabelNodeBinary = BabelNodeBinaryExpression | BabelNodeLogicalExpression;
type BabelNodeScopable = BabelNodeBlockStatement | BabelNodeCatchClause | BabelNodeDoWhileStatement | BabelNodeForInStatement | BabelNodeForStatement | BabelNodeFunctionDeclaration | BabelNodeFunctionExpression | BabelNodeProgram | BabelNodeObjectMethod | BabelNodeSwitchStatement | BabelNodeWhileStatement | BabelNodeArrowFunctionExpression | BabelNodeClassExpression | BabelNodeClassDeclaration | BabelNodeForOfStatement | BabelNodeClassMethod | BabelNodeClassPrivateMethod | BabelNodeStaticBlock | BabelNodeTSModuleBlock;
type BabelNodeBlockParent = BabelNodeBlockStatement | BabelNodeCatchClause | BabelNodeDoWhileStatement | BabelNodeForInStatement | BabelNodeForStatement | BabelNodeFunctionDeclaration | BabelNodeFunctionExpression | BabelNodeProgram | BabelNodeObjectMethod | BabelNodeSwitchStatement | BabelNodeWhileStatement | BabelNodeArrowFunctionExpression | BabelNodeForOfStatement | BabelNodeClassMethod | BabelNodeClassPrivateMethod | BabelNodeStaticBlock | BabelNodeTSModuleBlock;
type BabelNodeBlock = BabelNodeBlockStatement | BabelNodeProgram | BabelNodeTSModuleBlock;
type BabelNodeStatement = BabelNodeBlockStatement | BabelNodeBreakStatement | BabelNodeContinueStatement | BabelNodeDebuggerStatement | BabelNodeDoWhileStatement | BabelNodeEmptyStatement | BabelNodeExpressionStatement | BabelNodeForInStatement | BabelNodeForStatement | BabelNodeFunctionDeclaration | BabelNodeIfStatement | BabelNodeLabeledStatement | BabelNodeReturnStatement | BabelNodeSwitchStatement | BabelNodeThrowStatement | BabelNodeTryStatement | BabelNodeVariableDeclaration | BabelNodeWhileStatement | BabelNodeWithStatement | BabelNodeClassDeclaration | BabelNodeExportAllDeclaration | BabelNodeExportDefaultDeclaration | BabelNodeExportNamedDeclaration | BabelNodeForOfStatement | BabelNodeImportDeclaration | BabelNodeDeclareClass | BabelNodeDeclareFunction | BabelNodeDeclareInterface | BabelNodeDeclareModule | BabelNodeDeclareModuleExports | BabelNodeDeclareTypeAlias | BabelNodeDeclareOpaqueType | BabelNodeDeclareVariable | BabelNodeDeclareExportDeclaration | BabelNodeDeclareExportAllDeclaration | BabelNodeInterfaceDeclaration | BabelNodeOpaqueType | BabelNodeTypeAlias | BabelNodeEnumDeclaration | BabelNodeTSDeclareFunction | BabelNodeTSInterfaceDeclaration | BabelNodeTSTypeAliasDeclaration | BabelNodeTSEnumDeclaration | BabelNodeTSModuleDeclaration | BabelNodeTSImportEqualsDeclaration | BabelNodeTSExportAssignment | BabelNodeTSNamespaceExportDeclaration;
type BabelNodeTerminatorless = BabelNodeBreakStatement | BabelNodeContinueStatement | BabelNodeReturnStatement | BabelNodeThrowStatement | BabelNodeYieldExpression | BabelNodeAwaitExpression;
type BabelNodeCompletionStatement = BabelNodeBreakStatement | BabelNodeContinueStatement | BabelNodeReturnStatement | BabelNodeThrowStatement;
type BabelNodeConditional = BabelNodeConditionalExpression | BabelNodeIfStatement;
type BabelNodeLoop = BabelNodeDoWhileStatement | BabelNodeForInStatement | BabelNodeForStatement | BabelNodeWhileStatement | BabelNodeForOfStatement;
type BabelNodeWhile = BabelNodeDoWhileStatement | BabelNodeWhileStatement;
type BabelNodeExpressionWrapper = BabelNodeExpressionStatement | BabelNodeParenthesizedExpression | BabelNodeTypeCastExpression;
type BabelNodeFor = BabelNodeForInStatement | BabelNodeForStatement | BabelNodeForOfStatement;
type BabelNodeForXStatement = BabelNodeForInStatement | BabelNodeForOfStatement;
type BabelNodeFunction = BabelNodeFunctionDeclaration | BabelNodeFunctionExpression | BabelNodeObjectMethod | BabelNodeArrowFunctionExpression | BabelNodeClassMethod | BabelNodeClassPrivateMethod;
type BabelNodeFunctionParent = BabelNodeFunctionDeclaration | BabelNodeFunctionExpression | BabelNodeObjectMethod | BabelNodeArrowFunctionExpression | BabelNodeClassMethod | BabelNodeClassPrivateMethod | BabelNodeStaticBlock | BabelNodeTSModuleBlock;
type BabelNodePureish = BabelNodeFunctionDeclaration | BabelNodeFunctionExpression | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeNullLiteral | BabelNodeBooleanLiteral | BabelNodeRegExpLiteral | BabelNodeArrowFunctionExpression | BabelNodeBigIntLiteral | BabelNodeDecimalLiteral;
type BabelNodeDeclaration = BabelNodeFunctionDeclaration | BabelNodeVariableDeclaration | BabelNodeClassDeclaration | BabelNodeExportAllDeclaration | BabelNodeExportDefaultDeclaration | BabelNodeExportNamedDeclaration | BabelNodeImportDeclaration | BabelNodeDeclareClass | BabelNodeDeclareFunction | BabelNodeDeclareInterface | BabelNodeDeclareModule | BabelNodeDeclareModuleExports | BabelNodeDeclareTypeAlias | BabelNodeDeclareOpaqueType | BabelNodeDeclareVariable | BabelNodeDeclareExportDeclaration | BabelNodeDeclareExportAllDeclaration | BabelNodeInterfaceDeclaration | BabelNodeOpaqueType | BabelNodeTypeAlias | BabelNodeEnumDeclaration | BabelNodeTSDeclareFunction | BabelNodeTSInterfaceDeclaration | BabelNodeTSTypeAliasDeclaration | BabelNodeTSEnumDeclaration | BabelNodeTSModuleDeclaration;
type BabelNodePatternLike = BabelNodeIdentifier | BabelNodeRestElement | BabelNodeAssignmentPattern | BabelNodeArrayPattern | BabelNodeObjectPattern | BabelNodeTSAsExpression | BabelNodeTSSatisfiesExpression | BabelNodeTSTypeAssertion | BabelNodeTSNonNullExpression;
type BabelNodeLVal = BabelNodeIdentifier | BabelNodeMemberExpression | BabelNodeRestElement | BabelNodeAssignmentPattern | BabelNodeArrayPattern | BabelNodeObjectPattern | BabelNodeTSParameterProperty | BabelNodeTSAsExpression | BabelNodeTSSatisfiesExpression | BabelNodeTSTypeAssertion | BabelNodeTSNonNullExpression;
type BabelNodeTSEntityName = BabelNodeIdentifier | BabelNodeTSQualifiedName;
type BabelNodeLiteral = BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeNullLiteral | BabelNodeBooleanLiteral | BabelNodeRegExpLiteral | BabelNodeTemplateLiteral | BabelNodeBigIntLiteral | BabelNodeDecimalLiteral;
type BabelNodeImmutable = BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeNullLiteral | BabelNodeBooleanLiteral | BabelNodeBigIntLiteral | BabelNodeJSXAttribute | BabelNodeJSXClosingElement | BabelNodeJSXElement | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXOpeningElement | BabelNodeJSXText | BabelNodeJSXFragment | BabelNodeJSXOpeningFragment | BabelNodeJSXClosingFragment | BabelNodeDecimalLiteral;
type BabelNodeUserWhitespacable = BabelNodeObjectMethod | BabelNodeObjectProperty | BabelNodeObjectTypeInternalSlot | BabelNodeObjectTypeCallProperty | BabelNodeObjectTypeIndexer | BabelNodeObjectTypeProperty | BabelNodeObjectTypeSpreadProperty;
type BabelNodeMethod = BabelNodeObjectMethod | BabelNodeClassMethod | BabelNodeClassPrivateMethod;
type BabelNodeObjectMember = BabelNodeObjectMethod | BabelNodeObjectProperty;
type BabelNodeProperty = BabelNodeObjectProperty | BabelNodeClassProperty | BabelNodeClassAccessorProperty | BabelNodeClassPrivateProperty;
type BabelNodeUnaryLike = BabelNodeUnaryExpression | BabelNodeSpreadElement;
type BabelNodePattern = BabelNodeAssignmentPattern | BabelNodeArrayPattern | BabelNodeObjectPattern;
type BabelNodeClass = BabelNodeClassExpression | BabelNodeClassDeclaration;
type BabelNodeImportOrExportDeclaration = BabelNodeExportAllDeclaration | BabelNodeExportDefaultDeclaration | BabelNodeExportNamedDeclaration | BabelNodeImportDeclaration;
type BabelNodeExportDeclaration = BabelNodeExportAllDeclaration | BabelNodeExportDefaultDeclaration | BabelNodeExportNamedDeclaration;
type BabelNodeModuleSpecifier = BabelNodeExportSpecifier | BabelNodeImportDefaultSpecifier | BabelNodeImportNamespaceSpecifier | BabelNodeImportSpecifier | BabelNodeExportNamespaceSpecifier | BabelNodeExportDefaultSpecifier;
type BabelNodeAccessor = BabelNodeClassAccessorProperty;
type BabelNodePrivate = BabelNodeClassPrivateProperty | BabelNodeClassPrivateMethod | BabelNodePrivateName;
type BabelNodeFlow = BabelNodeAnyTypeAnnotation | BabelNodeArrayTypeAnnotation | BabelNodeBooleanTypeAnnotation | BabelNodeBooleanLiteralTypeAnnotation | BabelNodeNullLiteralTypeAnnotation | BabelNodeClassImplements | BabelNodeDeclareClass | BabelNodeDeclareFunction | BabelNodeDeclareInterface | BabelNodeDeclareModule | BabelNodeDeclareModuleExports | BabelNodeDeclareTypeAlias | BabelNodeDeclareOpaqueType | BabelNodeDeclareVariable | BabelNodeDeclareExportDeclaration | BabelNodeDeclareExportAllDeclaration | BabelNodeDeclaredPredicate | BabelNodeExistsTypeAnnotation | BabelNodeFunctionTypeAnnotation | BabelNodeFunctionTypeParam | BabelNodeGenericTypeAnnotation | BabelNodeInferredPredicate | BabelNodeInterfaceExtends | BabelNodeInterfaceDeclaration | BabelNodeInterfaceTypeAnnotation | BabelNodeIntersectionTypeAnnotation | BabelNodeMixedTypeAnnotation | BabelNodeEmptyTypeAnnotation | BabelNodeNullableTypeAnnotation | BabelNodeNumberLiteralTypeAnnotation | BabelNodeNumberTypeAnnotation | BabelNodeObjectTypeAnnotation | BabelNodeObjectTypeInternalSlot | BabelNodeObjectTypeCallProperty | BabelNodeObjectTypeIndexer | BabelNodeObjectTypeProperty | BabelNodeObjectTypeSpreadProperty | BabelNodeOpaqueType | BabelNodeQualifiedTypeIdentifier | BabelNodeStringLiteralTypeAnnotation | BabelNodeStringTypeAnnotation | BabelNodeSymbolTypeAnnotation | BabelNodeThisTypeAnnotation | BabelNodeTupleTypeAnnotation | BabelNodeTypeofTypeAnnotation | BabelNodeTypeAlias | BabelNodeTypeAnnotation | BabelNodeTypeCastExpression | BabelNodeTypeParameter | BabelNodeTypeParameterDeclaration | BabelNodeTypeParameterInstantiation | BabelNodeUnionTypeAnnotation | BabelNodeVariance | BabelNodeVoidTypeAnnotation | BabelNodeEnumDeclaration | BabelNodeEnumBooleanBody | BabelNodeEnumNumberBody | BabelNodeEnumStringBody | BabelNodeEnumSymbolBody | BabelNodeEnumBooleanMember | BabelNodeEnumNumberMember | BabelNodeEnumStringMember | BabelNodeEnumDefaultedMember | BabelNodeIndexedAccessType | BabelNodeOptionalIndexedAccessType;
type BabelNodeFlowType = BabelNodeAnyTypeAnnotation | BabelNodeArrayTypeAnnotation | BabelNodeBooleanTypeAnnotation | BabelNodeBooleanLiteralTypeAnnotation | BabelNodeNullLiteralTypeAnnotation | BabelNodeExistsTypeAnnotation | BabelNodeFunctionTypeAnnotation | BabelNodeGenericTypeAnnotation | BabelNodeInterfaceTypeAnnotation | BabelNodeIntersectionTypeAnnotation | BabelNodeMixedTypeAnnotation | BabelNodeEmptyTypeAnnotation | BabelNodeNullableTypeAnnotation | BabelNodeNumberLiteralTypeAnnotation | BabelNodeNumberTypeAnnotation | BabelNodeObjectTypeAnnotation | BabelNodeStringLiteralTypeAnnotation | BabelNodeStringTypeAnnotation | BabelNodeSymbolTypeAnnotation | BabelNodeThisTypeAnnotation | BabelNodeTupleTypeAnnotation | BabelNodeTypeofTypeAnnotation | BabelNodeUnionTypeAnnotation | BabelNodeVoidTypeAnnotation | BabelNodeIndexedAccessType | BabelNodeOptionalIndexedAccessType;
type BabelNodeFlowBaseAnnotation = BabelNodeAnyTypeAnnotation | BabelNodeBooleanTypeAnnotation | BabelNodeNullLiteralTypeAnnotation | BabelNodeMixedTypeAnnotation | BabelNodeEmptyTypeAnnotation | BabelNodeNumberTypeAnnotation | BabelNodeStringTypeAnnotation | BabelNodeSymbolTypeAnnotation | BabelNodeThisTypeAnnotation | BabelNodeVoidTypeAnnotation;
type BabelNodeFlowDeclaration = BabelNodeDeclareClass | BabelNodeDeclareFunction | BabelNodeDeclareInterface | BabelNodeDeclareModule | BabelNodeDeclareModuleExports | BabelNodeDeclareTypeAlias | BabelNodeDeclareOpaqueType | BabelNodeDeclareVariable | BabelNodeDeclareExportDeclaration | BabelNodeDeclareExportAllDeclaration | BabelNodeInterfaceDeclaration | BabelNodeOpaqueType | BabelNodeTypeAlias;
type BabelNodeFlowPredicate = BabelNodeDeclaredPredicate | BabelNodeInferredPredicate;
type BabelNodeEnumBody = BabelNodeEnumBooleanBody | BabelNodeEnumNumberBody | BabelNodeEnumStringBody | BabelNodeEnumSymbolBody;
type BabelNodeEnumMember = BabelNodeEnumBooleanMember | BabelNodeEnumNumberMember | BabelNodeEnumStringMember | BabelNodeEnumDefaultedMember;
type BabelNodeJSX = BabelNodeJSXAttribute | BabelNodeJSXClosingElement | BabelNodeJSXElement | BabelNodeJSXEmptyExpression | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXIdentifier | BabelNodeJSXMemberExpression | BabelNodeJSXNamespacedName | BabelNodeJSXOpeningElement | BabelNodeJSXSpreadAttribute | BabelNodeJSXText | BabelNodeJSXFragment | BabelNodeJSXOpeningFragment | BabelNodeJSXClosingFragment;
type BabelNodeMiscellaneous = BabelNodeNoop | BabelNodePlaceholder | BabelNodeV8IntrinsicIdentifier;
type BabelNodeTypeScript = BabelNodeTSParameterProperty | BabelNodeTSDeclareFunction | BabelNodeTSDeclareMethod | BabelNodeTSQualifiedName | BabelNodeTSCallSignatureDeclaration | BabelNodeTSConstructSignatureDeclaration | BabelNodeTSPropertySignature | BabelNodeTSMethodSignature | BabelNodeTSIndexSignature | BabelNodeTSAnyKeyword | BabelNodeTSBooleanKeyword | BabelNodeTSBigIntKeyword | BabelNodeTSIntrinsicKeyword | BabelNodeTSNeverKeyword | BabelNodeTSNullKeyword | BabelNodeTSNumberKeyword | BabelNodeTSObjectKeyword | BabelNodeTSStringKeyword | BabelNodeTSSymbolKeyword | BabelNodeTSUndefinedKeyword | BabelNodeTSUnknownKeyword | BabelNodeTSVoidKeyword | BabelNodeTSThisType | BabelNodeTSFunctionType | BabelNodeTSConstructorType | BabelNodeTSTypeReference | BabelNodeTSTypePredicate | BabelNodeTSTypeQuery | BabelNodeTSTypeLiteral | BabelNodeTSArrayType | BabelNodeTSTupleType | BabelNodeTSOptionalType | BabelNodeTSRestType | BabelNodeTSNamedTupleMember | BabelNodeTSUnionType | BabelNodeTSIntersectionType | BabelNodeTSConditionalType | BabelNodeTSInferType | BabelNodeTSParenthesizedType | BabelNodeTSTypeOperator | BabelNodeTSIndexedAccessType | BabelNodeTSMappedType | BabelNodeTSLiteralType | BabelNodeTSExpressionWithTypeArguments | BabelNodeTSInterfaceDeclaration | BabelNodeTSInterfaceBody | BabelNodeTSTypeAliasDeclaration | BabelNodeTSInstantiationExpression | BabelNodeTSAsExpression | BabelNodeTSSatisfiesExpression | BabelNodeTSTypeAssertion | BabelNodeTSEnumDeclaration | BabelNodeTSEnumMember | BabelNodeTSModuleDeclaration | BabelNodeTSModuleBlock | BabelNodeTSImportType | BabelNodeTSImportEqualsDeclaration | BabelNodeTSExternalModuleReference | BabelNodeTSNonNullExpression | BabelNodeTSExportAssignment | BabelNodeTSNamespaceExportDeclaration | BabelNodeTSTypeAnnotation | BabelNodeTSTypeParameterInstantiation | BabelNodeTSTypeParameterDeclaration | BabelNodeTSTypeParameter;
type BabelNodeTSTypeElement = BabelNodeTSCallSignatureDeclaration | BabelNodeTSConstructSignatureDeclaration | BabelNodeTSPropertySignature | BabelNodeTSMethodSignature | BabelNodeTSIndexSignature;
type BabelNodeTSType = BabelNodeTSAnyKeyword | BabelNodeTSBooleanKeyword | BabelNodeTSBigIntKeyword | BabelNodeTSIntrinsicKeyword | BabelNodeTSNeverKeyword | BabelNodeTSNullKeyword | BabelNodeTSNumberKeyword | BabelNodeTSObjectKeyword | BabelNodeTSStringKeyword | BabelNodeTSSymbolKeyword | BabelNodeTSUndefinedKeyword | BabelNodeTSUnknownKeyword | BabelNodeTSVoidKeyword | BabelNodeTSThisType | BabelNodeTSFunctionType | BabelNodeTSConstructorType | BabelNodeTSTypeReference | BabelNodeTSTypePredicate | BabelNodeTSTypeQuery | BabelNodeTSTypeLiteral | BabelNodeTSArrayType | BabelNodeTSTupleType | BabelNodeTSOptionalType | BabelNodeTSRestType | BabelNodeTSUnionType | BabelNodeTSIntersectionType | BabelNodeTSConditionalType | BabelNodeTSInferType | BabelNodeTSParenthesizedType | BabelNodeTSTypeOperator | BabelNodeTSIndexedAccessType | BabelNodeTSMappedType | BabelNodeTSLiteralType | BabelNodeTSExpressionWithTypeArguments | BabelNodeTSImportType;
type BabelNodeTSBaseType = BabelNodeTSAnyKeyword | BabelNodeTSBooleanKeyword | BabelNodeTSBigIntKeyword | BabelNodeTSIntrinsicKeyword | BabelNodeTSNeverKeyword | BabelNodeTSNullKeyword | BabelNodeTSNumberKeyword | BabelNodeTSObjectKeyword | BabelNodeTSStringKeyword | BabelNodeTSSymbolKeyword | BabelNodeTSUndefinedKeyword | BabelNodeTSUnknownKeyword | BabelNodeTSVoidKeyword | BabelNodeTSThisType | BabelNodeTSLiteralType;
type BabelNodeModuleDeclaration = BabelNodeExportAllDeclaration | BabelNodeExportDefaultDeclaration | BabelNodeExportNamedDeclaration | BabelNodeImportDeclaration;

declare module "@babel/types" {
  declare export function arrayExpression(elements?: Array<null | BabelNodeExpression | BabelNodeSpreadElement>): BabelNodeArrayExpression;
  declare export function assignmentExpression(operator: string, left: BabelNodeLVal | BabelNodeOptionalMemberExpression, right: BabelNodeExpression): BabelNodeAssignmentExpression;
  declare export function binaryExpression(operator: "+" | "-" | "/" | "%" | "*" | "**" | "&" | "|" | ">>" | ">>>" | "<<" | "^" | "==" | "===" | "!=" | "!==" | "in" | "instanceof" | ">" | "<" | ">=" | "<=" | "|>", left: BabelNodeExpression | BabelNodePrivateName, right: BabelNodeExpression): BabelNodeBinaryExpression;
  declare export function interpreterDirective(value: string): BabelNodeInterpreterDirective;
  declare export function directive(value: BabelNodeDirectiveLiteral): BabelNodeDirective;
  declare export function directiveLiteral(value: string): BabelNodeDirectiveLiteral;
  declare export function blockStatement(body: Array<BabelNodeStatement>, directives?: Array<BabelNodeDirective>): BabelNodeBlockStatement;
  declare export function breakStatement(label?: BabelNodeIdentifier): BabelNodeBreakStatement;
  declare export function callExpression(callee: BabelNodeExpression | BabelNodeSuper | BabelNodeV8IntrinsicIdentifier, _arguments: Array<BabelNodeExpression | BabelNodeSpreadElement | BabelNodeJSXNamespacedName | BabelNodeArgumentPlaceholder>): BabelNodeCallExpression;
  declare export function catchClause(param?: BabelNodeIdentifier | BabelNodeArrayPattern | BabelNodeObjectPattern, body: BabelNodeBlockStatement): BabelNodeCatchClause;
  declare export function conditionalExpression(test: BabelNodeExpression, consequent: BabelNodeExpression, alternate: BabelNodeExpression): BabelNodeConditionalExpression;
  declare export function continueStatement(label?: BabelNodeIdentifier): BabelNodeContinueStatement;
  declare export function debuggerStatement(): BabelNodeDebuggerStatement;
  declare export function doWhileStatement(test: BabelNodeExpression, body: BabelNodeStatement): BabelNodeDoWhileStatement;
  declare export function emptyStatement(): BabelNodeEmptyStatement;
  declare export function expressionStatement(expression: BabelNodeExpression): BabelNodeExpressionStatement;
  declare export function file(program: BabelNodeProgram, comments?: Array<BabelNodeCommentBlock | BabelNodeCommentLine>, tokens?: Array<any>): BabelNodeFile;
  declare export function forInStatement(left: BabelNodeVariableDeclaration | BabelNodeLVal, right: BabelNodeExpression, body: BabelNodeStatement): BabelNodeForInStatement;
  declare export function forStatement(init?: BabelNodeVariableDeclaration | BabelNodeExpression, test?: BabelNodeExpression, update?: BabelNodeExpression, body: BabelNodeStatement): BabelNodeForStatement;
  declare export function functionDeclaration(id?: BabelNodeIdentifier, params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>, body: BabelNodeBlockStatement, generator?: boolean, async?: boolean): BabelNodeFunctionDeclaration;
  declare export function functionExpression(id?: BabelNodeIdentifier, params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>, body: BabelNodeBlockStatement, generator?: boolean, async?: boolean): BabelNodeFunctionExpression;
  declare export function identifier(name: string): BabelNodeIdentifier;
  declare export function ifStatement(test: BabelNodeExpression, consequent: BabelNodeStatement, alternate?: BabelNodeStatement): BabelNodeIfStatement;
  declare export function labeledStatement(label: BabelNodeIdentifier, body: BabelNodeStatement): BabelNodeLabeledStatement;
  declare export function stringLiteral(value: string): BabelNodeStringLiteral;
  declare export function numericLiteral(value: number): BabelNodeNumericLiteral;
  declare export function nullLiteral(): BabelNodeNullLiteral;
  declare export function booleanLiteral(value: boolean): BabelNodeBooleanLiteral;
  declare export function regExpLiteral(pattern: string, flags?: string): BabelNodeRegExpLiteral;
  declare export function logicalExpression(operator: "||" | "&&" | "??", left: BabelNodeExpression, right: BabelNodeExpression): BabelNodeLogicalExpression;
  declare export function memberExpression(object: BabelNodeExpression | BabelNodeSuper, property: BabelNodeExpression | BabelNodeIdentifier | BabelNodePrivateName, computed?: boolean, optional?: true | false): BabelNodeMemberExpression;
  declare export function newExpression(callee: BabelNodeExpression | BabelNodeSuper | BabelNodeV8IntrinsicIdentifier, _arguments: Array<BabelNodeExpression | BabelNodeSpreadElement | BabelNodeJSXNamespacedName | BabelNodeArgumentPlaceholder>): BabelNodeNewExpression;
  declare export function program(body: Array<BabelNodeStatement>, directives?: Array<BabelNodeDirective>, sourceType?: "script" | "module", interpreter?: BabelNodeInterpreterDirective): BabelNodeProgram;
  declare export function objectExpression(properties: Array<BabelNodeObjectMethod | BabelNodeObjectProperty | BabelNodeSpreadElement>): BabelNodeObjectExpression;
  declare export function objectMethod(kind?: "method" | "get" | "set", key: BabelNodeExpression | BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral, params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>, body: BabelNodeBlockStatement, computed?: boolean, generator?: boolean, async?: boolean): BabelNodeObjectMethod;
  declare export function objectProperty(key: BabelNodeExpression | BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeDecimalLiteral | BabelNodePrivateName, value: BabelNodeExpression | BabelNodePatternLike, computed?: boolean, shorthand?: boolean, decorators?: Array<BabelNodeDecorator>): BabelNodeObjectProperty;
  declare export function restElement(argument: BabelNodeLVal): BabelNodeRestElement;
  declare export function returnStatement(argument?: BabelNodeExpression): BabelNodeReturnStatement;
  declare export function sequenceExpression(expressions: Array<BabelNodeExpression>): BabelNodeSequenceExpression;
  declare export function parenthesizedExpression(expression: BabelNodeExpression): BabelNodeParenthesizedExpression;
  declare export function switchCase(test?: BabelNodeExpression, consequent: Array<BabelNodeStatement>): BabelNodeSwitchCase;
  declare export function switchStatement(discriminant: BabelNodeExpression, cases: Array<BabelNodeSwitchCase>): BabelNodeSwitchStatement;
  declare export function thisExpression(): BabelNodeThisExpression;
  declare export function throwStatement(argument: BabelNodeExpression): BabelNodeThrowStatement;
  declare export function tryStatement(block: BabelNodeBlockStatement, handler?: BabelNodeCatchClause, finalizer?: BabelNodeBlockStatement): BabelNodeTryStatement;
  declare export function unaryExpression(operator: "void" | "throw" | "delete" | "!" | "+" | "-" | "~" | "typeof", argument: BabelNodeExpression, prefix?: boolean): BabelNodeUnaryExpression;
  declare export function updateExpression(operator: "++" | "--", argument: BabelNodeExpression, prefix?: boolean): BabelNodeUpdateExpression;
  declare export function variableDeclaration(kind: "var" | "let" | "const" | "using" | "await using", declarations: Array<BabelNodeVariableDeclarator>): BabelNodeVariableDeclaration;
  declare export function variableDeclarator(id: BabelNodeLVal, init?: BabelNodeExpression): BabelNodeVariableDeclarator;
  declare export function whileStatement(test: BabelNodeExpression, body: BabelNodeStatement): BabelNodeWhileStatement;
  declare export function withStatement(object: BabelNodeExpression, body: BabelNodeStatement): BabelNodeWithStatement;
  declare export function assignmentPattern(left: BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeArrayPattern | BabelNodeMemberExpression | BabelNodeTSAsExpression | BabelNodeTSSatisfiesExpression | BabelNodeTSTypeAssertion | BabelNodeTSNonNullExpression, right: BabelNodeExpression): BabelNodeAssignmentPattern;
  declare export function arrayPattern(elements: Array<null | BabelNodePatternLike | BabelNodeLVal>): BabelNodeArrayPattern;
  declare export function arrowFunctionExpression(params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>, body: BabelNodeBlockStatement | BabelNodeExpression, async?: boolean): BabelNodeArrowFunctionExpression;
  declare export function classBody(body: Array<BabelNodeClassMethod | BabelNodeClassPrivateMethod | BabelNodeClassProperty | BabelNodeClassPrivateProperty | BabelNodeClassAccessorProperty | BabelNodeTSDeclareMethod | BabelNodeTSIndexSignature | BabelNodeStaticBlock>): BabelNodeClassBody;
  declare export function classExpression(id?: BabelNodeIdentifier, superClass?: BabelNodeExpression, body: BabelNodeClassBody, decorators?: Array<BabelNodeDecorator>): BabelNodeClassExpression;
  declare export function classDeclaration(id?: BabelNodeIdentifier, superClass?: BabelNodeExpression, body: BabelNodeClassBody, decorators?: Array<BabelNodeDecorator>): BabelNodeClassDeclaration;
  declare export function exportAllDeclaration(source: BabelNodeStringLiteral): BabelNodeExportAllDeclaration;
  declare export function exportDefaultDeclaration(declaration: BabelNodeTSDeclareFunction | BabelNodeFunctionDeclaration | BabelNodeClassDeclaration | BabelNodeExpression): BabelNodeExportDefaultDeclaration;
  declare export function exportNamedDeclaration(declaration?: BabelNodeDeclaration, specifiers?: Array<BabelNodeExportSpecifier | BabelNodeExportDefaultSpecifier | BabelNodeExportNamespaceSpecifier>, source?: BabelNodeStringLiteral): BabelNodeExportNamedDeclaration;
  declare export function exportSpecifier(local: BabelNodeIdentifier, exported: BabelNodeIdentifier | BabelNodeStringLiteral): BabelNodeExportSpecifier;
  declare export function forOfStatement(left: BabelNodeVariableDeclaration | BabelNodeLVal, right: BabelNodeExpression, body: BabelNodeStatement, _await?: boolean): BabelNodeForOfStatement;
  declare export function importDeclaration(specifiers: Array<BabelNodeImportSpecifier | BabelNodeImportDefaultSpecifier | BabelNodeImportNamespaceSpecifier>, source: BabelNodeStringLiteral): BabelNodeImportDeclaration;
  declare export function importDefaultSpecifier(local: BabelNodeIdentifier): BabelNodeImportDefaultSpecifier;
  declare export function importNamespaceSpecifier(local: BabelNodeIdentifier): BabelNodeImportNamespaceSpecifier;
  declare export function importSpecifier(local: BabelNodeIdentifier, imported: BabelNodeIdentifier | BabelNodeStringLiteral): BabelNodeImportSpecifier;
  declare export function importExpression(source: BabelNodeExpression, options?: BabelNodeExpression): BabelNodeImportExpression;
  declare export function metaProperty(meta: BabelNodeIdentifier, property: BabelNodeIdentifier): BabelNodeMetaProperty;
  declare export function classMethod(kind?: "get" | "set" | "method" | "constructor", key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression, params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement | BabelNodeTSParameterProperty>, body: BabelNodeBlockStatement, computed?: boolean, _static?: boolean, generator?: boolean, async?: boolean): BabelNodeClassMethod;
  declare export function objectPattern(properties: Array<BabelNodeRestElement | BabelNodeObjectProperty>): BabelNodeObjectPattern;
  declare export function spreadElement(argument: BabelNodeExpression): BabelNodeSpreadElement;
  declare function _super(): BabelNodeSuper;
  declare export { _super as super }
  declare export function taggedTemplateExpression(tag: BabelNodeExpression, quasi: BabelNodeTemplateLiteral): BabelNodeTaggedTemplateExpression;
  declare export function templateElement(value: { raw: string, cooked?: string }, tail?: boolean): BabelNodeTemplateElement;
  declare export function templateLiteral(quasis: Array<BabelNodeTemplateElement>, expressions: Array<BabelNodeExpression | BabelNodeTSType>): BabelNodeTemplateLiteral;
  declare export function yieldExpression(argument?: BabelNodeExpression, delegate?: boolean): BabelNodeYieldExpression;
  declare export function awaitExpression(argument: BabelNodeExpression): BabelNodeAwaitExpression;
  declare function _import(): BabelNodeImport;
  declare export { _import as import }
  declare export function bigIntLiteral(value: string): BabelNodeBigIntLiteral;
  declare export function exportNamespaceSpecifier(exported: BabelNodeIdentifier): BabelNodeExportNamespaceSpecifier;
  declare export function optionalMemberExpression(object: BabelNodeExpression, property: BabelNodeExpression | BabelNodeIdentifier, computed?: boolean, optional: boolean): BabelNodeOptionalMemberExpression;
  declare export function optionalCallExpression(callee: BabelNodeExpression, _arguments: Array<BabelNodeExpression | BabelNodeSpreadElement | BabelNodeJSXNamespacedName | BabelNodeArgumentPlaceholder>, optional: boolean): BabelNodeOptionalCallExpression;
  declare export function classProperty(key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression, value?: BabelNodeExpression, typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop, decorators?: Array<BabelNodeDecorator>, computed?: boolean, _static?: boolean): BabelNodeClassProperty;
  declare export function classAccessorProperty(key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression | BabelNodePrivateName, value?: BabelNodeExpression, typeAnnotation?: BabelNodeTypeAnnotation | BabelNodeTSTypeAnnotation | BabelNodeNoop, decorators?: Array<BabelNodeDecorator>, computed?: boolean, _static?: boolean): BabelNodeClassAccessorProperty;
  declare export function classPrivateProperty(key: BabelNodePrivateName, value?: BabelNodeExpression, decorators?: Array<BabelNodeDecorator>, _static?: boolean): BabelNodeClassPrivateProperty;
  declare export function classPrivateMethod(kind?: "get" | "set" | "method", key: BabelNodePrivateName, params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement | BabelNodeTSParameterProperty>, body: BabelNodeBlockStatement, _static?: boolean): BabelNodeClassPrivateMethod;
  declare export function privateName(id: BabelNodeIdentifier): BabelNodePrivateName;
  declare export function staticBlock(body: Array<BabelNodeStatement>): BabelNodeStaticBlock;
  declare export function anyTypeAnnotation(): BabelNodeAnyTypeAnnotation;
  declare export function arrayTypeAnnotation(elementType: BabelNodeFlowType): BabelNodeArrayTypeAnnotation;
  declare export function booleanTypeAnnotation(): BabelNodeBooleanTypeAnnotation;
  declare export function booleanLiteralTypeAnnotation(value: boolean): BabelNodeBooleanLiteralTypeAnnotation;
  declare export function nullLiteralTypeAnnotation(): BabelNodeNullLiteralTypeAnnotation;
  declare export function classImplements(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterInstantiation): BabelNodeClassImplements;
  declare export function declareClass(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterDeclaration, _extends?: Array<BabelNodeInterfaceExtends>, body: BabelNodeObjectTypeAnnotation): BabelNodeDeclareClass;
  declare export function declareFunction(id: BabelNodeIdentifier): BabelNodeDeclareFunction;
  declare export function declareInterface(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterDeclaration, _extends?: Array<BabelNodeInterfaceExtends>, body: BabelNodeObjectTypeAnnotation): BabelNodeDeclareInterface;
  declare export function declareModule(id: BabelNodeIdentifier | BabelNodeStringLiteral, body: BabelNodeBlockStatement, kind?: "CommonJS" | "ES"): BabelNodeDeclareModule;
  declare export function declareModuleExports(typeAnnotation: BabelNodeTypeAnnotation): BabelNodeDeclareModuleExports;
  declare export function declareTypeAlias(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterDeclaration, right: BabelNodeFlowType): BabelNodeDeclareTypeAlias;
  declare export function declareOpaqueType(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterDeclaration, supertype?: BabelNodeFlowType): BabelNodeDeclareOpaqueType;
  declare export function declareVariable(id: BabelNodeIdentifier): BabelNodeDeclareVariable;
  declare export function declareExportDeclaration(declaration?: BabelNodeFlow, specifiers?: Array<BabelNodeExportSpecifier | BabelNodeExportNamespaceSpecifier>, source?: BabelNodeStringLiteral): BabelNodeDeclareExportDeclaration;
  declare export function declareExportAllDeclaration(source: BabelNodeStringLiteral): BabelNodeDeclareExportAllDeclaration;
  declare export function declaredPredicate(value: BabelNodeFlow): BabelNodeDeclaredPredicate;
  declare export function existsTypeAnnotation(): BabelNodeExistsTypeAnnotation;
  declare export function functionTypeAnnotation(typeParameters?: BabelNodeTypeParameterDeclaration, params: Array<BabelNodeFunctionTypeParam>, rest?: BabelNodeFunctionTypeParam, returnType: BabelNodeFlowType): BabelNodeFunctionTypeAnnotation;
  declare export function functionTypeParam(name?: BabelNodeIdentifier, typeAnnotation: BabelNodeFlowType): BabelNodeFunctionTypeParam;
  declare export function genericTypeAnnotation(id: BabelNodeIdentifier | BabelNodeQualifiedTypeIdentifier, typeParameters?: BabelNodeTypeParameterInstantiation): BabelNodeGenericTypeAnnotation;
  declare export function inferredPredicate(): BabelNodeInferredPredicate;
  declare export function interfaceExtends(id: BabelNodeIdentifier | BabelNodeQualifiedTypeIdentifier, typeParameters?: BabelNodeTypeParameterInstantiation): BabelNodeInterfaceExtends;
  declare export function interfaceDeclaration(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterDeclaration, _extends?: Array<BabelNodeInterfaceExtends>, body: BabelNodeObjectTypeAnnotation): BabelNodeInterfaceDeclaration;
  declare export function interfaceTypeAnnotation(_extends?: Array<BabelNodeInterfaceExtends>, body: BabelNodeObjectTypeAnnotation): BabelNodeInterfaceTypeAnnotation;
  declare export function intersectionTypeAnnotation(types: Array<BabelNodeFlowType>): BabelNodeIntersectionTypeAnnotation;
  declare export function mixedTypeAnnotation(): BabelNodeMixedTypeAnnotation;
  declare export function emptyTypeAnnotation(): BabelNodeEmptyTypeAnnotation;
  declare export function nullableTypeAnnotation(typeAnnotation: BabelNodeFlowType): BabelNodeNullableTypeAnnotation;
  declare export function numberLiteralTypeAnnotation(value: number): BabelNodeNumberLiteralTypeAnnotation;
  declare export function numberTypeAnnotation(): BabelNodeNumberTypeAnnotation;
  declare export function objectTypeAnnotation(properties: Array<BabelNodeObjectTypeProperty | BabelNodeObjectTypeSpreadProperty>, indexers?: Array<BabelNodeObjectTypeIndexer>, callProperties?: Array<BabelNodeObjectTypeCallProperty>, internalSlots?: Array<BabelNodeObjectTypeInternalSlot>, exact?: boolean): BabelNodeObjectTypeAnnotation;
  declare export function objectTypeInternalSlot(id: BabelNodeIdentifier, value: BabelNodeFlowType, optional: boolean, _static: boolean, method: boolean): BabelNodeObjectTypeInternalSlot;
  declare export function objectTypeCallProperty(value: BabelNodeFlowType): BabelNodeObjectTypeCallProperty;
  declare export function objectTypeIndexer(id?: BabelNodeIdentifier, key: BabelNodeFlowType, value: BabelNodeFlowType, variance?: BabelNodeVariance): BabelNodeObjectTypeIndexer;
  declare export function objectTypeProperty(key: BabelNodeIdentifier | BabelNodeStringLiteral, value: BabelNodeFlowType, variance?: BabelNodeVariance): BabelNodeObjectTypeProperty;
  declare export function objectTypeSpreadProperty(argument: BabelNodeFlowType): BabelNodeObjectTypeSpreadProperty;
  declare export function opaqueType(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterDeclaration, supertype?: BabelNodeFlowType, impltype: BabelNodeFlowType): BabelNodeOpaqueType;
  declare export function qualifiedTypeIdentifier(id: BabelNodeIdentifier, qualification: BabelNodeIdentifier | BabelNodeQualifiedTypeIdentifier): BabelNodeQualifiedTypeIdentifier;
  declare export function stringLiteralTypeAnnotation(value: string): BabelNodeStringLiteralTypeAnnotation;
  declare export function stringTypeAnnotation(): BabelNodeStringTypeAnnotation;
  declare export function symbolTypeAnnotation(): BabelNodeSymbolTypeAnnotation;
  declare export function thisTypeAnnotation(): BabelNodeThisTypeAnnotation;
  declare export function tupleTypeAnnotation(types: Array<BabelNodeFlowType>): BabelNodeTupleTypeAnnotation;
  declare export function typeofTypeAnnotation(argument: BabelNodeFlowType): BabelNodeTypeofTypeAnnotation;
  declare export function typeAlias(id: BabelNodeIdentifier, typeParameters?: BabelNodeTypeParameterDeclaration, right: BabelNodeFlowType): BabelNodeTypeAlias;
  declare export function typeAnnotation(typeAnnotation: BabelNodeFlowType): BabelNodeTypeAnnotation;
  declare export function typeCastExpression(expression: BabelNodeExpression, typeAnnotation: BabelNodeTypeAnnotation): BabelNodeTypeCastExpression;
  declare export function typeParameter(bound?: BabelNodeTypeAnnotation, _default?: BabelNodeFlowType, variance?: BabelNodeVariance): BabelNodeTypeParameter;
  declare export function typeParameterDeclaration(params: Array<BabelNodeTypeParameter>): BabelNodeTypeParameterDeclaration;
  declare export function typeParameterInstantiation(params: Array<BabelNodeFlowType>): BabelNodeTypeParameterInstantiation;
  declare export function unionTypeAnnotation(types: Array<BabelNodeFlowType>): BabelNodeUnionTypeAnnotation;
  declare export function variance(kind: "minus" | "plus"): BabelNodeVariance;
  declare export function voidTypeAnnotation(): BabelNodeVoidTypeAnnotation;
  declare export function enumDeclaration(id: BabelNodeIdentifier, body: BabelNodeEnumBooleanBody | BabelNodeEnumNumberBody | BabelNodeEnumStringBody | BabelNodeEnumSymbolBody): BabelNodeEnumDeclaration;
  declare export function enumBooleanBody(members: Array<BabelNodeEnumBooleanMember>): BabelNodeEnumBooleanBody;
  declare export function enumNumberBody(members: Array<BabelNodeEnumNumberMember>): BabelNodeEnumNumberBody;
  declare export function enumStringBody(members: Array<BabelNodeEnumStringMember | BabelNodeEnumDefaultedMember>): BabelNodeEnumStringBody;
  declare export function enumSymbolBody(members: Array<BabelNodeEnumDefaultedMember>): BabelNodeEnumSymbolBody;
  declare export function enumBooleanMember(id: BabelNodeIdentifier): BabelNodeEnumBooleanMember;
  declare export function enumNumberMember(id: BabelNodeIdentifier, init: BabelNodeNumericLiteral): BabelNodeEnumNumberMember;
  declare export function enumStringMember(id: BabelNodeIdentifier, init: BabelNodeStringLiteral): BabelNodeEnumStringMember;
  declare export function enumDefaultedMember(id: BabelNodeIdentifier): BabelNodeEnumDefaultedMember;
  declare export function indexedAccessType(objectType: BabelNodeFlowType, indexType: BabelNodeFlowType): BabelNodeIndexedAccessType;
  declare export function optionalIndexedAccessType(objectType: BabelNodeFlowType, indexType: BabelNodeFlowType): BabelNodeOptionalIndexedAccessType;
  declare export function jsxAttribute(name: BabelNodeJSXIdentifier | BabelNodeJSXNamespacedName, value?: BabelNodeJSXElement | BabelNodeJSXFragment | BabelNodeStringLiteral | BabelNodeJSXExpressionContainer): BabelNodeJSXAttribute;
  declare export function jsxClosingElement(name: BabelNodeJSXIdentifier | BabelNodeJSXMemberExpression | BabelNodeJSXNamespacedName): BabelNodeJSXClosingElement;
  declare export function jsxElement(openingElement: BabelNodeJSXOpeningElement, closingElement?: BabelNodeJSXClosingElement, children: Array<BabelNodeJSXText | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXElement | BabelNodeJSXFragment>, selfClosing?: boolean): BabelNodeJSXElement;
  declare export function jsxEmptyExpression(): BabelNodeJSXEmptyExpression;
  declare export function jsxExpressionContainer(expression: BabelNodeExpression | BabelNodeJSXEmptyExpression): BabelNodeJSXExpressionContainer;
  declare export function jsxSpreadChild(expression: BabelNodeExpression): BabelNodeJSXSpreadChild;
  declare export function jsxIdentifier(name: string): BabelNodeJSXIdentifier;
  declare export function jsxMemberExpression(object: BabelNodeJSXMemberExpression | BabelNodeJSXIdentifier, property: BabelNodeJSXIdentifier): BabelNodeJSXMemberExpression;
  declare export function jsxNamespacedName(namespace: BabelNodeJSXIdentifier, name: BabelNodeJSXIdentifier): BabelNodeJSXNamespacedName;
  declare export function jsxOpeningElement(name: BabelNodeJSXIdentifier | BabelNodeJSXMemberExpression | BabelNodeJSXNamespacedName, attributes: Array<BabelNodeJSXAttribute | BabelNodeJSXSpreadAttribute>, selfClosing?: boolean): BabelNodeJSXOpeningElement;
  declare export function jsxSpreadAttribute(argument: BabelNodeExpression): BabelNodeJSXSpreadAttribute;
  declare export function jsxText(value: string): BabelNodeJSXText;
  declare export function jsxFragment(openingFragment: BabelNodeJSXOpeningFragment, closingFragment: BabelNodeJSXClosingFragment, children: Array<BabelNodeJSXText | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXElement | BabelNodeJSXFragment>): BabelNodeJSXFragment;
  declare export function jsxOpeningFragment(): BabelNodeJSXOpeningFragment;
  declare export function jsxClosingFragment(): BabelNodeJSXClosingFragment;
  declare export function noop(): BabelNodeNoop;
  declare export function placeholder(expectedNode: "Identifier" | "StringLiteral" | "Expression" | "Statement" | "Declaration" | "BlockStatement" | "ClassBody" | "Pattern", name: BabelNodeIdentifier): BabelNodePlaceholder;
  declare export function v8IntrinsicIdentifier(name: string): BabelNodeV8IntrinsicIdentifier;
  declare export function argumentPlaceholder(): BabelNodeArgumentPlaceholder;
  declare export function bindExpression(object: BabelNodeExpression, callee: BabelNodeExpression): BabelNodeBindExpression;
  declare export function importAttribute(key: BabelNodeIdentifier | BabelNodeStringLiteral, value: BabelNodeStringLiteral): BabelNodeImportAttribute;
  declare export function decorator(expression: BabelNodeExpression): BabelNodeDecorator;
  declare export function doExpression(body: BabelNodeBlockStatement, async?: boolean): BabelNodeDoExpression;
  declare export function exportDefaultSpecifier(exported: BabelNodeIdentifier): BabelNodeExportDefaultSpecifier;
  declare export function recordExpression(properties: Array<BabelNodeObjectProperty | BabelNodeSpreadElement>): BabelNodeRecordExpression;
  declare export function tupleExpression(elements?: Array<BabelNodeExpression | BabelNodeSpreadElement>): BabelNodeTupleExpression;
  declare export function decimalLiteral(value: string): BabelNodeDecimalLiteral;
  declare export function moduleExpression(body: BabelNodeProgram): BabelNodeModuleExpression;
  declare export function topicReference(): BabelNodeTopicReference;
  declare export function pipelineTopicExpression(expression: BabelNodeExpression): BabelNodePipelineTopicExpression;
  declare export function pipelineBareFunction(callee: BabelNodeExpression): BabelNodePipelineBareFunction;
  declare export function pipelinePrimaryTopicReference(): BabelNodePipelinePrimaryTopicReference;
  declare export function tsParameterProperty(parameter: BabelNodeIdentifier | BabelNodeAssignmentPattern): BabelNodeTSParameterProperty;
  declare export function tsDeclareFunction(id?: BabelNodeIdentifier, typeParameters?: BabelNodeTSTypeParameterDeclaration | BabelNodeNoop, params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement>, returnType?: BabelNodeTSTypeAnnotation | BabelNodeNoop): BabelNodeTSDeclareFunction;
  declare export function tsDeclareMethod(decorators?: Array<BabelNodeDecorator>, key: BabelNodeIdentifier | BabelNodeStringLiteral | BabelNodeNumericLiteral | BabelNodeBigIntLiteral | BabelNodeExpression, typeParameters?: BabelNodeTSTypeParameterDeclaration | BabelNodeNoop, params: Array<BabelNodeIdentifier | BabelNodePattern | BabelNodeRestElement | BabelNodeTSParameterProperty>, returnType?: BabelNodeTSTypeAnnotation | BabelNodeNoop): BabelNodeTSDeclareMethod;
  declare export function tsQualifiedName(left: BabelNodeTSEntityName, right: BabelNodeIdentifier): BabelNodeTSQualifiedName;
  declare export function tsCallSignatureDeclaration(typeParameters?: BabelNodeTSTypeParameterDeclaration, parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>, typeAnnotation?: BabelNodeTSTypeAnnotation): BabelNodeTSCallSignatureDeclaration;
  declare export function tsConstructSignatureDeclaration(typeParameters?: BabelNodeTSTypeParameterDeclaration, parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>, typeAnnotation?: BabelNodeTSTypeAnnotation): BabelNodeTSConstructSignatureDeclaration;
  declare export function tsPropertySignature(key: BabelNodeExpression, typeAnnotation?: BabelNodeTSTypeAnnotation): BabelNodeTSPropertySignature;
  declare export function tsMethodSignature(key: BabelNodeExpression, typeParameters?: BabelNodeTSTypeParameterDeclaration, parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>, typeAnnotation?: BabelNodeTSTypeAnnotation): BabelNodeTSMethodSignature;
  declare export function tsIndexSignature(parameters: Array<BabelNodeIdentifier>, typeAnnotation?: BabelNodeTSTypeAnnotation): BabelNodeTSIndexSignature;
  declare export function tsAnyKeyword(): BabelNodeTSAnyKeyword;
  declare export function tsBooleanKeyword(): BabelNodeTSBooleanKeyword;
  declare export function tsBigIntKeyword(): BabelNodeTSBigIntKeyword;
  declare export function tsIntrinsicKeyword(): BabelNodeTSIntrinsicKeyword;
  declare export function tsNeverKeyword(): BabelNodeTSNeverKeyword;
  declare export function tsNullKeyword(): BabelNodeTSNullKeyword;
  declare export function tsNumberKeyword(): BabelNodeTSNumberKeyword;
  declare export function tsObjectKeyword(): BabelNodeTSObjectKeyword;
  declare export function tsStringKeyword(): BabelNodeTSStringKeyword;
  declare export function tsSymbolKeyword(): BabelNodeTSSymbolKeyword;
  declare export function tsUndefinedKeyword(): BabelNodeTSUndefinedKeyword;
  declare export function tsUnknownKeyword(): BabelNodeTSUnknownKeyword;
  declare export function tsVoidKeyword(): BabelNodeTSVoidKeyword;
  declare export function tsThisType(): BabelNodeTSThisType;
  declare export function tsFunctionType(typeParameters?: BabelNodeTSTypeParameterDeclaration, parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>, typeAnnotation?: BabelNodeTSTypeAnnotation): BabelNodeTSFunctionType;
  declare export function tsConstructorType(typeParameters?: BabelNodeTSTypeParameterDeclaration, parameters: Array<BabelNodeArrayPattern | BabelNodeIdentifier | BabelNodeObjectPattern | BabelNodeRestElement>, typeAnnotation?: BabelNodeTSTypeAnnotation): BabelNodeTSConstructorType;
  declare export function tsTypeReference(typeName: BabelNodeTSEntityName, typeParameters?: BabelNodeTSTypeParameterInstantiation): BabelNodeTSTypeReference;
  declare export function tsTypePredicate(parameterName: BabelNodeIdentifier | BabelNodeTSThisType, typeAnnotation?: BabelNodeTSTypeAnnotation, asserts?: boolean): BabelNodeTSTypePredicate;
  declare export function tsTypeQuery(exprName: BabelNodeTSEntityName | BabelNodeTSImportType, typeParameters?: BabelNodeTSTypeParameterInstantiation): BabelNodeTSTypeQuery;
  declare export function tsTypeLiteral(members: Array<BabelNodeTSTypeElement>): BabelNodeTSTypeLiteral;
  declare export function tsArrayType(elementType: BabelNodeTSType): BabelNodeTSArrayType;
  declare export function tsTupleType(elementTypes: Array<BabelNodeTSType | BabelNodeTSNamedTupleMember>): BabelNodeTSTupleType;
  declare export function tsOptionalType(typeAnnotation: BabelNodeTSType): BabelNodeTSOptionalType;
  declare export function tsRestType(typeAnnotation: BabelNodeTSType): BabelNodeTSRestType;
  declare export function tsNamedTupleMember(label: BabelNodeIdentifier, elementType: BabelNodeTSType, optional?: boolean): BabelNodeTSNamedTupleMember;
  declare export function tsUnionType(types: Array<BabelNodeTSType>): BabelNodeTSUnionType;
  declare export function tsIntersectionType(types: Array<BabelNodeTSType>): BabelNodeTSIntersectionType;
  declare export function tsConditionalType(checkType: BabelNodeTSType, extendsType: BabelNodeTSType, trueType: BabelNodeTSType, falseType: BabelNodeTSType): BabelNodeTSConditionalType;
  declare export function tsInferType(typeParameter: BabelNodeTSTypeParameter): BabelNodeTSInferType;
  declare export function tsParenthesizedType(typeAnnotation: BabelNodeTSType): BabelNodeTSParenthesizedType;
  declare export function tsTypeOperator(typeAnnotation: BabelNodeTSType): BabelNodeTSTypeOperator;
  declare export function tsIndexedAccessType(objectType: BabelNodeTSType, indexType: BabelNodeTSType): BabelNodeTSIndexedAccessType;
  declare export function tsMappedType(typeParameter: BabelNodeTSTypeParameter, typeAnnotation?: BabelNodeTSType, nameType?: BabelNodeTSType): BabelNodeTSMappedType;
  declare export function tsLiteralType(literal: BabelNodeNumericLiteral | BabelNodeStringLiteral | BabelNodeBooleanLiteral | BabelNodeBigIntLiteral | BabelNodeTemplateLiteral | BabelNodeUnaryExpression): BabelNodeTSLiteralType;
  declare export function tsExpressionWithTypeArguments(expression: BabelNodeTSEntityName, typeParameters?: BabelNodeTSTypeParameterInstantiation): BabelNodeTSExpressionWithTypeArguments;
  declare export function tsInterfaceDeclaration(id: BabelNodeIdentifier, typeParameters?: BabelNodeTSTypeParameterDeclaration, _extends?: Array<BabelNodeTSExpressionWithTypeArguments>, body: BabelNodeTSInterfaceBody): BabelNodeTSInterfaceDeclaration;
  declare export function tsInterfaceBody(body: Array<BabelNodeTSTypeElement>): BabelNodeTSInterfaceBody;
  declare export function tsTypeAliasDeclaration(id: BabelNodeIdentifier, typeParameters?: BabelNodeTSTypeParameterDeclaration, typeAnnotation: BabelNodeTSType): BabelNodeTSTypeAliasDeclaration;
  declare export function tsInstantiationExpression(expression: BabelNodeExpression, typeParameters?: BabelNodeTSTypeParameterInstantiation): BabelNodeTSInstantiationExpression;
  declare export function tsAsExpression(expression: BabelNodeExpression, typeAnnotation: BabelNodeTSType): BabelNodeTSAsExpression;
  declare export function tsSatisfiesExpression(expression: BabelNodeExpression, typeAnnotation: BabelNodeTSType): BabelNodeTSSatisfiesExpression;
  declare export function tsTypeAssertion(typeAnnotation: BabelNodeTSType, expression: BabelNodeExpression): BabelNodeTSTypeAssertion;
  declare export function tsEnumDeclaration(id: BabelNodeIdentifier, members: Array<BabelNodeTSEnumMember>): BabelNodeTSEnumDeclaration;
  declare export function tsEnumMember(id: BabelNodeIdentifier | BabelNodeStringLiteral, initializer?: BabelNodeExpression): BabelNodeTSEnumMember;
  declare export function tsModuleDeclaration(id: BabelNodeIdentifier | BabelNodeStringLiteral, body: BabelNodeTSModuleBlock | BabelNodeTSModuleDeclaration): BabelNodeTSModuleDeclaration;
  declare export function tsModuleBlock(body: Array<BabelNodeStatement>): BabelNodeTSModuleBlock;
  declare export function tsImportType(argument: BabelNodeStringLiteral, qualifier?: BabelNodeTSEntityName, typeParameters?: BabelNodeTSTypeParameterInstantiation): BabelNodeTSImportType;
  declare export function tsImportEqualsDeclaration(id: BabelNodeIdentifier, moduleReference: BabelNodeTSEntityName | BabelNodeTSExternalModuleReference): BabelNodeTSImportEqualsDeclaration;
  declare export function tsExternalModuleReference(expression: BabelNodeStringLiteral): BabelNodeTSExternalModuleReference;
  declare export function tsNonNullExpression(expression: BabelNodeExpression): BabelNodeTSNonNullExpression;
  declare export function tsExportAssignment(expression: BabelNodeExpression): BabelNodeTSExportAssignment;
  declare export function tsNamespaceExportDeclaration(id: BabelNodeIdentifier): BabelNodeTSNamespaceExportDeclaration;
  declare export function tsTypeAnnotation(typeAnnotation: BabelNodeTSType): BabelNodeTSTypeAnnotation;
  declare export function tsTypeParameterInstantiation(params: Array<BabelNodeTSType>): BabelNodeTSTypeParameterInstantiation;
  declare export function tsTypeParameterDeclaration(params: Array<BabelNodeTSTypeParameter>): BabelNodeTSTypeParameterDeclaration;
  declare export function tsTypeParameter(constraint?: BabelNodeTSType, _default?: BabelNodeTSType, name: string): BabelNodeTSTypeParameter;
  declare export function isArrayExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeArrayExpression)
  declare export function assertArrayExpression(node: ?Object, opts?: ?Object): void
  declare export function isAssignmentExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeAssignmentExpression)
  declare export function assertAssignmentExpression(node: ?Object, opts?: ?Object): void
  declare export function isBinaryExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBinaryExpression)
  declare export function assertBinaryExpression(node: ?Object, opts?: ?Object): void
  declare export function isInterpreterDirective(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeInterpreterDirective)
  declare export function assertInterpreterDirective(node: ?Object, opts?: ?Object): void
  declare export function isDirective(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDirective)
  declare export function assertDirective(node: ?Object, opts?: ?Object): void
  declare export function isDirectiveLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDirectiveLiteral)
  declare export function assertDirectiveLiteral(node: ?Object, opts?: ?Object): void
  declare export function isBlockStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBlockStatement)
  declare export function assertBlockStatement(node: ?Object, opts?: ?Object): void
  declare export function isBreakStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBreakStatement)
  declare export function assertBreakStatement(node: ?Object, opts?: ?Object): void
  declare export function isCallExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeCallExpression)
  declare export function assertCallExpression(node: ?Object, opts?: ?Object): void
  declare export function isCatchClause(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeCatchClause)
  declare export function assertCatchClause(node: ?Object, opts?: ?Object): void
  declare export function isConditionalExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeConditionalExpression)
  declare export function assertConditionalExpression(node: ?Object, opts?: ?Object): void
  declare export function isContinueStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeContinueStatement)
  declare export function assertContinueStatement(node: ?Object, opts?: ?Object): void
  declare export function isDebuggerStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDebuggerStatement)
  declare export function assertDebuggerStatement(node: ?Object, opts?: ?Object): void
  declare export function isDoWhileStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDoWhileStatement)
  declare export function assertDoWhileStatement(node: ?Object, opts?: ?Object): void
  declare export function isEmptyStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEmptyStatement)
  declare export function assertEmptyStatement(node: ?Object, opts?: ?Object): void
  declare export function isExpressionStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExpressionStatement)
  declare export function assertExpressionStatement(node: ?Object, opts?: ?Object): void
  declare export function isFile(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeFile)
  declare export function assertFile(node: ?Object, opts?: ?Object): void
  declare export function isForInStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeForInStatement)
  declare export function assertForInStatement(node: ?Object, opts?: ?Object): void
  declare export function isForStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeForStatement)
  declare export function assertForStatement(node: ?Object, opts?: ?Object): void
  declare export function isFunctionDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeFunctionDeclaration)
  declare export function assertFunctionDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isFunctionExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeFunctionExpression)
  declare export function assertFunctionExpression(node: ?Object, opts?: ?Object): void
  declare export function isIdentifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeIdentifier)
  declare export function assertIdentifier(node: ?Object, opts?: ?Object): void
  declare export function isIfStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeIfStatement)
  declare export function assertIfStatement(node: ?Object, opts?: ?Object): void
  declare export function isLabeledStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeLabeledStatement)
  declare export function assertLabeledStatement(node: ?Object, opts?: ?Object): void
  declare export function isStringLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeStringLiteral)
  declare export function assertStringLiteral(node: ?Object, opts?: ?Object): void
  declare export function isNumericLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNumericLiteral)
  declare export function assertNumericLiteral(node: ?Object, opts?: ?Object): void
  declare export function isNullLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNullLiteral)
  declare export function assertNullLiteral(node: ?Object, opts?: ?Object): void
  declare export function isBooleanLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBooleanLiteral)
  declare export function assertBooleanLiteral(node: ?Object, opts?: ?Object): void
  declare export function isRegExpLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeRegExpLiteral)
  declare export function assertRegExpLiteral(node: ?Object, opts?: ?Object): void
  declare export function isLogicalExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeLogicalExpression)
  declare export function assertLogicalExpression(node: ?Object, opts?: ?Object): void
  declare export function isMemberExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeMemberExpression)
  declare export function assertMemberExpression(node: ?Object, opts?: ?Object): void
  declare export function isNewExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNewExpression)
  declare export function assertNewExpression(node: ?Object, opts?: ?Object): void
  declare export function isProgram(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeProgram)
  declare export function assertProgram(node: ?Object, opts?: ?Object): void
  declare export function isObjectExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectExpression)
  declare export function assertObjectExpression(node: ?Object, opts?: ?Object): void
  declare export function isObjectMethod(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectMethod)
  declare export function assertObjectMethod(node: ?Object, opts?: ?Object): void
  declare export function isObjectProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectProperty)
  declare export function assertObjectProperty(node: ?Object, opts?: ?Object): void
  declare export function isRestElement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeRestElement)
  declare export function assertRestElement(node: ?Object, opts?: ?Object): void
  declare export function isReturnStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeReturnStatement)
  declare export function assertReturnStatement(node: ?Object, opts?: ?Object): void
  declare export function isSequenceExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeSequenceExpression)
  declare export function assertSequenceExpression(node: ?Object, opts?: ?Object): void
  declare export function isParenthesizedExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeParenthesizedExpression)
  declare export function assertParenthesizedExpression(node: ?Object, opts?: ?Object): void
  declare export function isSwitchCase(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeSwitchCase)
  declare export function assertSwitchCase(node: ?Object, opts?: ?Object): void
  declare export function isSwitchStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeSwitchStatement)
  declare export function assertSwitchStatement(node: ?Object, opts?: ?Object): void
  declare export function isThisExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeThisExpression)
  declare export function assertThisExpression(node: ?Object, opts?: ?Object): void
  declare export function isThrowStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeThrowStatement)
  declare export function assertThrowStatement(node: ?Object, opts?: ?Object): void
  declare export function isTryStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTryStatement)
  declare export function assertTryStatement(node: ?Object, opts?: ?Object): void
  declare export function isUnaryExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeUnaryExpression)
  declare export function assertUnaryExpression(node: ?Object, opts?: ?Object): void
  declare export function isUpdateExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeUpdateExpression)
  declare export function assertUpdateExpression(node: ?Object, opts?: ?Object): void
  declare export function isVariableDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeVariableDeclaration)
  declare export function assertVariableDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isVariableDeclarator(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeVariableDeclarator)
  declare export function assertVariableDeclarator(node: ?Object, opts?: ?Object): void
  declare export function isWhileStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeWhileStatement)
  declare export function assertWhileStatement(node: ?Object, opts?: ?Object): void
  declare export function isWithStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeWithStatement)
  declare export function assertWithStatement(node: ?Object, opts?: ?Object): void
  declare export function isAssignmentPattern(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeAssignmentPattern)
  declare export function assertAssignmentPattern(node: ?Object, opts?: ?Object): void
  declare export function isArrayPattern(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeArrayPattern)
  declare export function assertArrayPattern(node: ?Object, opts?: ?Object): void
  declare export function isArrowFunctionExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeArrowFunctionExpression)
  declare export function assertArrowFunctionExpression(node: ?Object, opts?: ?Object): void
  declare export function isClassBody(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassBody)
  declare export function assertClassBody(node: ?Object, opts?: ?Object): void
  declare export function isClassExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassExpression)
  declare export function assertClassExpression(node: ?Object, opts?: ?Object): void
  declare export function isClassDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassDeclaration)
  declare export function assertClassDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isExportAllDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExportAllDeclaration)
  declare export function assertExportAllDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isExportDefaultDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExportDefaultDeclaration)
  declare export function assertExportDefaultDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isExportNamedDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExportNamedDeclaration)
  declare export function assertExportNamedDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isExportSpecifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExportSpecifier)
  declare export function assertExportSpecifier(node: ?Object, opts?: ?Object): void
  declare export function isForOfStatement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeForOfStatement)
  declare export function assertForOfStatement(node: ?Object, opts?: ?Object): void
  declare export function isImportDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeImportDeclaration)
  declare export function assertImportDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isImportDefaultSpecifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeImportDefaultSpecifier)
  declare export function assertImportDefaultSpecifier(node: ?Object, opts?: ?Object): void
  declare export function isImportNamespaceSpecifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeImportNamespaceSpecifier)
  declare export function assertImportNamespaceSpecifier(node: ?Object, opts?: ?Object): void
  declare export function isImportSpecifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeImportSpecifier)
  declare export function assertImportSpecifier(node: ?Object, opts?: ?Object): void
  declare export function isImportExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeImportExpression)
  declare export function assertImportExpression(node: ?Object, opts?: ?Object): void
  declare export function isMetaProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeMetaProperty)
  declare export function assertMetaProperty(node: ?Object, opts?: ?Object): void
  declare export function isClassMethod(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassMethod)
  declare export function assertClassMethod(node: ?Object, opts?: ?Object): void
  declare export function isObjectPattern(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectPattern)
  declare export function assertObjectPattern(node: ?Object, opts?: ?Object): void
  declare export function isSpreadElement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeSpreadElement)
  declare export function assertSpreadElement(node: ?Object, opts?: ?Object): void
  declare export function isSuper(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeSuper)
  declare export function assertSuper(node: ?Object, opts?: ?Object): void
  declare export function isTaggedTemplateExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTaggedTemplateExpression)
  declare export function assertTaggedTemplateExpression(node: ?Object, opts?: ?Object): void
  declare export function isTemplateElement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTemplateElement)
  declare export function assertTemplateElement(node: ?Object, opts?: ?Object): void
  declare export function isTemplateLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTemplateLiteral)
  declare export function assertTemplateLiteral(node: ?Object, opts?: ?Object): void
  declare export function isYieldExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeYieldExpression)
  declare export function assertYieldExpression(node: ?Object, opts?: ?Object): void
  declare export function isAwaitExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeAwaitExpression)
  declare export function assertAwaitExpression(node: ?Object, opts?: ?Object): void
  declare export function isImport(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeImport)
  declare export function assertImport(node: ?Object, opts?: ?Object): void
  declare export function isBigIntLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBigIntLiteral)
  declare export function assertBigIntLiteral(node: ?Object, opts?: ?Object): void
  declare export function isExportNamespaceSpecifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExportNamespaceSpecifier)
  declare export function assertExportNamespaceSpecifier(node: ?Object, opts?: ?Object): void
  declare export function isOptionalMemberExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeOptionalMemberExpression)
  declare export function assertOptionalMemberExpression(node: ?Object, opts?: ?Object): void
  declare export function isOptionalCallExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeOptionalCallExpression)
  declare export function assertOptionalCallExpression(node: ?Object, opts?: ?Object): void
  declare export function isClassProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassProperty)
  declare export function assertClassProperty(node: ?Object, opts?: ?Object): void
  declare export function isClassAccessorProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassAccessorProperty)
  declare export function assertClassAccessorProperty(node: ?Object, opts?: ?Object): void
  declare export function isClassPrivateProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassPrivateProperty)
  declare export function assertClassPrivateProperty(node: ?Object, opts?: ?Object): void
  declare export function isClassPrivateMethod(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassPrivateMethod)
  declare export function assertClassPrivateMethod(node: ?Object, opts?: ?Object): void
  declare export function isPrivateName(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodePrivateName)
  declare export function assertPrivateName(node: ?Object, opts?: ?Object): void
  declare export function isStaticBlock(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeStaticBlock)
  declare export function assertStaticBlock(node: ?Object, opts?: ?Object): void
  declare export function isAnyTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeAnyTypeAnnotation)
  declare export function assertAnyTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isArrayTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeArrayTypeAnnotation)
  declare export function assertArrayTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isBooleanTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBooleanTypeAnnotation)
  declare export function assertBooleanTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isBooleanLiteralTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBooleanLiteralTypeAnnotation)
  declare export function assertBooleanLiteralTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isNullLiteralTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNullLiteralTypeAnnotation)
  declare export function assertNullLiteralTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isClassImplements(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeClassImplements)
  declare export function assertClassImplements(node: ?Object, opts?: ?Object): void
  declare export function isDeclareClass(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareClass)
  declare export function assertDeclareClass(node: ?Object, opts?: ?Object): void
  declare export function isDeclareFunction(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareFunction)
  declare export function assertDeclareFunction(node: ?Object, opts?: ?Object): void
  declare export function isDeclareInterface(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareInterface)
  declare export function assertDeclareInterface(node: ?Object, opts?: ?Object): void
  declare export function isDeclareModule(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareModule)
  declare export function assertDeclareModule(node: ?Object, opts?: ?Object): void
  declare export function isDeclareModuleExports(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareModuleExports)
  declare export function assertDeclareModuleExports(node: ?Object, opts?: ?Object): void
  declare export function isDeclareTypeAlias(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareTypeAlias)
  declare export function assertDeclareTypeAlias(node: ?Object, opts?: ?Object): void
  declare export function isDeclareOpaqueType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareOpaqueType)
  declare export function assertDeclareOpaqueType(node: ?Object, opts?: ?Object): void
  declare export function isDeclareVariable(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareVariable)
  declare export function assertDeclareVariable(node: ?Object, opts?: ?Object): void
  declare export function isDeclareExportDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareExportDeclaration)
  declare export function assertDeclareExportDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isDeclareExportAllDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclareExportAllDeclaration)
  declare export function assertDeclareExportAllDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isDeclaredPredicate(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDeclaredPredicate)
  declare export function assertDeclaredPredicate(node: ?Object, opts?: ?Object): void
  declare export function isExistsTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExistsTypeAnnotation)
  declare export function assertExistsTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isFunctionTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeFunctionTypeAnnotation)
  declare export function assertFunctionTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isFunctionTypeParam(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeFunctionTypeParam)
  declare export function assertFunctionTypeParam(node: ?Object, opts?: ?Object): void
  declare export function isGenericTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeGenericTypeAnnotation)
  declare export function assertGenericTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isInferredPredicate(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeInferredPredicate)
  declare export function assertInferredPredicate(node: ?Object, opts?: ?Object): void
  declare export function isInterfaceExtends(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeInterfaceExtends)
  declare export function assertInterfaceExtends(node: ?Object, opts?: ?Object): void
  declare export function isInterfaceDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeInterfaceDeclaration)
  declare export function assertInterfaceDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isInterfaceTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeInterfaceTypeAnnotation)
  declare export function assertInterfaceTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isIntersectionTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeIntersectionTypeAnnotation)
  declare export function assertIntersectionTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isMixedTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeMixedTypeAnnotation)
  declare export function assertMixedTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isEmptyTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEmptyTypeAnnotation)
  declare export function assertEmptyTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isNullableTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNullableTypeAnnotation)
  declare export function assertNullableTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isNumberLiteralTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNumberLiteralTypeAnnotation)
  declare export function assertNumberLiteralTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isNumberTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNumberTypeAnnotation)
  declare export function assertNumberTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isObjectTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectTypeAnnotation)
  declare export function assertObjectTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isObjectTypeInternalSlot(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectTypeInternalSlot)
  declare export function assertObjectTypeInternalSlot(node: ?Object, opts?: ?Object): void
  declare export function isObjectTypeCallProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectTypeCallProperty)
  declare export function assertObjectTypeCallProperty(node: ?Object, opts?: ?Object): void
  declare export function isObjectTypeIndexer(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectTypeIndexer)
  declare export function assertObjectTypeIndexer(node: ?Object, opts?: ?Object): void
  declare export function isObjectTypeProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectTypeProperty)
  declare export function assertObjectTypeProperty(node: ?Object, opts?: ?Object): void
  declare export function isObjectTypeSpreadProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeObjectTypeSpreadProperty)
  declare export function assertObjectTypeSpreadProperty(node: ?Object, opts?: ?Object): void
  declare export function isOpaqueType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeOpaqueType)
  declare export function assertOpaqueType(node: ?Object, opts?: ?Object): void
  declare export function isQualifiedTypeIdentifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeQualifiedTypeIdentifier)
  declare export function assertQualifiedTypeIdentifier(node: ?Object, opts?: ?Object): void
  declare export function isStringLiteralTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeStringLiteralTypeAnnotation)
  declare export function assertStringLiteralTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isStringTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeStringTypeAnnotation)
  declare export function assertStringTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isSymbolTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeSymbolTypeAnnotation)
  declare export function assertSymbolTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isThisTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeThisTypeAnnotation)
  declare export function assertThisTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isTupleTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTupleTypeAnnotation)
  declare export function assertTupleTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isTypeofTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTypeofTypeAnnotation)
  declare export function assertTypeofTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isTypeAlias(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTypeAlias)
  declare export function assertTypeAlias(node: ?Object, opts?: ?Object): void
  declare export function isTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTypeAnnotation)
  declare export function assertTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isTypeCastExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTypeCastExpression)
  declare export function assertTypeCastExpression(node: ?Object, opts?: ?Object): void
  declare export function isTypeParameter(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTypeParameter)
  declare export function assertTypeParameter(node: ?Object, opts?: ?Object): void
  declare export function isTypeParameterDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTypeParameterDeclaration)
  declare export function assertTypeParameterDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTypeParameterInstantiation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTypeParameterInstantiation)
  declare export function assertTypeParameterInstantiation(node: ?Object, opts?: ?Object): void
  declare export function isUnionTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeUnionTypeAnnotation)
  declare export function assertUnionTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isVariance(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeVariance)
  declare export function assertVariance(node: ?Object, opts?: ?Object): void
  declare export function isVoidTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeVoidTypeAnnotation)
  declare export function assertVoidTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isEnumDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumDeclaration)
  declare export function assertEnumDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isEnumBooleanBody(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumBooleanBody)
  declare export function assertEnumBooleanBody(node: ?Object, opts?: ?Object): void
  declare export function isEnumNumberBody(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumNumberBody)
  declare export function assertEnumNumberBody(node: ?Object, opts?: ?Object): void
  declare export function isEnumStringBody(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumStringBody)
  declare export function assertEnumStringBody(node: ?Object, opts?: ?Object): void
  declare export function isEnumSymbolBody(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumSymbolBody)
  declare export function assertEnumSymbolBody(node: ?Object, opts?: ?Object): void
  declare export function isEnumBooleanMember(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumBooleanMember)
  declare export function assertEnumBooleanMember(node: ?Object, opts?: ?Object): void
  declare export function isEnumNumberMember(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumNumberMember)
  declare export function assertEnumNumberMember(node: ?Object, opts?: ?Object): void
  declare export function isEnumStringMember(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumStringMember)
  declare export function assertEnumStringMember(node: ?Object, opts?: ?Object): void
  declare export function isEnumDefaultedMember(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeEnumDefaultedMember)
  declare export function assertEnumDefaultedMember(node: ?Object, opts?: ?Object): void
  declare export function isIndexedAccessType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeIndexedAccessType)
  declare export function assertIndexedAccessType(node: ?Object, opts?: ?Object): void
  declare export function isOptionalIndexedAccessType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeOptionalIndexedAccessType)
  declare export function assertOptionalIndexedAccessType(node: ?Object, opts?: ?Object): void
  declare export function isJSXAttribute(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXAttribute)
  declare export function assertJSXAttribute(node: ?Object, opts?: ?Object): void
  declare export function isJSXClosingElement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXClosingElement)
  declare export function assertJSXClosingElement(node: ?Object, opts?: ?Object): void
  declare export function isJSXElement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXElement)
  declare export function assertJSXElement(node: ?Object, opts?: ?Object): void
  declare export function isJSXEmptyExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXEmptyExpression)
  declare export function assertJSXEmptyExpression(node: ?Object, opts?: ?Object): void
  declare export function isJSXExpressionContainer(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXExpressionContainer)
  declare export function assertJSXExpressionContainer(node: ?Object, opts?: ?Object): void
  declare export function isJSXSpreadChild(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXSpreadChild)
  declare export function assertJSXSpreadChild(node: ?Object, opts?: ?Object): void
  declare export function isJSXIdentifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXIdentifier)
  declare export function assertJSXIdentifier(node: ?Object, opts?: ?Object): void
  declare export function isJSXMemberExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXMemberExpression)
  declare export function assertJSXMemberExpression(node: ?Object, opts?: ?Object): void
  declare export function isJSXNamespacedName(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXNamespacedName)
  declare export function assertJSXNamespacedName(node: ?Object, opts?: ?Object): void
  declare export function isJSXOpeningElement(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXOpeningElement)
  declare export function assertJSXOpeningElement(node: ?Object, opts?: ?Object): void
  declare export function isJSXSpreadAttribute(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXSpreadAttribute)
  declare export function assertJSXSpreadAttribute(node: ?Object, opts?: ?Object): void
  declare export function isJSXText(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXText)
  declare export function assertJSXText(node: ?Object, opts?: ?Object): void
  declare export function isJSXFragment(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXFragment)
  declare export function assertJSXFragment(node: ?Object, opts?: ?Object): void
  declare export function isJSXOpeningFragment(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXOpeningFragment)
  declare export function assertJSXOpeningFragment(node: ?Object, opts?: ?Object): void
  declare export function isJSXClosingFragment(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeJSXClosingFragment)
  declare export function assertJSXClosingFragment(node: ?Object, opts?: ?Object): void
  declare export function isNoop(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNoop)
  declare export function assertNoop(node: ?Object, opts?: ?Object): void
  declare export function isPlaceholder(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodePlaceholder)
  declare export function assertPlaceholder(node: ?Object, opts?: ?Object): void
  declare export function isV8IntrinsicIdentifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeV8IntrinsicIdentifier)
  declare export function assertV8IntrinsicIdentifier(node: ?Object, opts?: ?Object): void
  declare export function isArgumentPlaceholder(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeArgumentPlaceholder)
  declare export function assertArgumentPlaceholder(node: ?Object, opts?: ?Object): void
  declare export function isBindExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeBindExpression)
  declare export function assertBindExpression(node: ?Object, opts?: ?Object): void
  declare export function isImportAttribute(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeImportAttribute)
  declare export function assertImportAttribute(node: ?Object, opts?: ?Object): void
  declare export function isDecorator(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDecorator)
  declare export function assertDecorator(node: ?Object, opts?: ?Object): void
  declare export function isDoExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDoExpression)
  declare export function assertDoExpression(node: ?Object, opts?: ?Object): void
  declare export function isExportDefaultSpecifier(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeExportDefaultSpecifier)
  declare export function assertExportDefaultSpecifier(node: ?Object, opts?: ?Object): void
  declare export function isRecordExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeRecordExpression)
  declare export function assertRecordExpression(node: ?Object, opts?: ?Object): void
  declare export function isTupleExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTupleExpression)
  declare export function assertTupleExpression(node: ?Object, opts?: ?Object): void
  declare export function isDecimalLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeDecimalLiteral)
  declare export function assertDecimalLiteral(node: ?Object, opts?: ?Object): void
  declare export function isModuleExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeModuleExpression)
  declare export function assertModuleExpression(node: ?Object, opts?: ?Object): void
  declare export function isTopicReference(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTopicReference)
  declare export function assertTopicReference(node: ?Object, opts?: ?Object): void
  declare export function isPipelineTopicExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodePipelineTopicExpression)
  declare export function assertPipelineTopicExpression(node: ?Object, opts?: ?Object): void
  declare export function isPipelineBareFunction(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodePipelineBareFunction)
  declare export function assertPipelineBareFunction(node: ?Object, opts?: ?Object): void
  declare export function isPipelinePrimaryTopicReference(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodePipelinePrimaryTopicReference)
  declare export function assertPipelinePrimaryTopicReference(node: ?Object, opts?: ?Object): void
  declare export function isTSParameterProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSParameterProperty)
  declare export function assertTSParameterProperty(node: ?Object, opts?: ?Object): void
  declare export function isTSDeclareFunction(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSDeclareFunction)
  declare export function assertTSDeclareFunction(node: ?Object, opts?: ?Object): void
  declare export function isTSDeclareMethod(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSDeclareMethod)
  declare export function assertTSDeclareMethod(node: ?Object, opts?: ?Object): void
  declare export function isTSQualifiedName(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSQualifiedName)
  declare export function assertTSQualifiedName(node: ?Object, opts?: ?Object): void
  declare export function isTSCallSignatureDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSCallSignatureDeclaration)
  declare export function assertTSCallSignatureDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSConstructSignatureDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSConstructSignatureDeclaration)
  declare export function assertTSConstructSignatureDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSPropertySignature(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSPropertySignature)
  declare export function assertTSPropertySignature(node: ?Object, opts?: ?Object): void
  declare export function isTSMethodSignature(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSMethodSignature)
  declare export function assertTSMethodSignature(node: ?Object, opts?: ?Object): void
  declare export function isTSIndexSignature(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSIndexSignature)
  declare export function assertTSIndexSignature(node: ?Object, opts?: ?Object): void
  declare export function isTSAnyKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSAnyKeyword)
  declare export function assertTSAnyKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSBooleanKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSBooleanKeyword)
  declare export function assertTSBooleanKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSBigIntKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSBigIntKeyword)
  declare export function assertTSBigIntKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSIntrinsicKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSIntrinsicKeyword)
  declare export function assertTSIntrinsicKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSNeverKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSNeverKeyword)
  declare export function assertTSNeverKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSNullKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSNullKeyword)
  declare export function assertTSNullKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSNumberKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSNumberKeyword)
  declare export function assertTSNumberKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSObjectKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSObjectKeyword)
  declare export function assertTSObjectKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSStringKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSStringKeyword)
  declare export function assertTSStringKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSSymbolKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSSymbolKeyword)
  declare export function assertTSSymbolKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSUndefinedKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSUndefinedKeyword)
  declare export function assertTSUndefinedKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSUnknownKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSUnknownKeyword)
  declare export function assertTSUnknownKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSVoidKeyword(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSVoidKeyword)
  declare export function assertTSVoidKeyword(node: ?Object, opts?: ?Object): void
  declare export function isTSThisType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSThisType)
  declare export function assertTSThisType(node: ?Object, opts?: ?Object): void
  declare export function isTSFunctionType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSFunctionType)
  declare export function assertTSFunctionType(node: ?Object, opts?: ?Object): void
  declare export function isTSConstructorType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSConstructorType)
  declare export function assertTSConstructorType(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeReference(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeReference)
  declare export function assertTSTypeReference(node: ?Object, opts?: ?Object): void
  declare export function isTSTypePredicate(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypePredicate)
  declare export function assertTSTypePredicate(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeQuery(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeQuery)
  declare export function assertTSTypeQuery(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeLiteral)
  declare export function assertTSTypeLiteral(node: ?Object, opts?: ?Object): void
  declare export function isTSArrayType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSArrayType)
  declare export function assertTSArrayType(node: ?Object, opts?: ?Object): void
  declare export function isTSTupleType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTupleType)
  declare export function assertTSTupleType(node: ?Object, opts?: ?Object): void
  declare export function isTSOptionalType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSOptionalType)
  declare export function assertTSOptionalType(node: ?Object, opts?: ?Object): void
  declare export function isTSRestType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSRestType)
  declare export function assertTSRestType(node: ?Object, opts?: ?Object): void
  declare export function isTSNamedTupleMember(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSNamedTupleMember)
  declare export function assertTSNamedTupleMember(node: ?Object, opts?: ?Object): void
  declare export function isTSUnionType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSUnionType)
  declare export function assertTSUnionType(node: ?Object, opts?: ?Object): void
  declare export function isTSIntersectionType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSIntersectionType)
  declare export function assertTSIntersectionType(node: ?Object, opts?: ?Object): void
  declare export function isTSConditionalType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSConditionalType)
  declare export function assertTSConditionalType(node: ?Object, opts?: ?Object): void
  declare export function isTSInferType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSInferType)
  declare export function assertTSInferType(node: ?Object, opts?: ?Object): void
  declare export function isTSParenthesizedType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSParenthesizedType)
  declare export function assertTSParenthesizedType(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeOperator(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeOperator)
  declare export function assertTSTypeOperator(node: ?Object, opts?: ?Object): void
  declare export function isTSIndexedAccessType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSIndexedAccessType)
  declare export function assertTSIndexedAccessType(node: ?Object, opts?: ?Object): void
  declare export function isTSMappedType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSMappedType)
  declare export function assertTSMappedType(node: ?Object, opts?: ?Object): void
  declare export function isTSLiteralType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSLiteralType)
  declare export function assertTSLiteralType(node: ?Object, opts?: ?Object): void
  declare export function isTSExpressionWithTypeArguments(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSExpressionWithTypeArguments)
  declare export function assertTSExpressionWithTypeArguments(node: ?Object, opts?: ?Object): void
  declare export function isTSInterfaceDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSInterfaceDeclaration)
  declare export function assertTSInterfaceDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSInterfaceBody(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSInterfaceBody)
  declare export function assertTSInterfaceBody(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeAliasDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeAliasDeclaration)
  declare export function assertTSTypeAliasDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSInstantiationExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSInstantiationExpression)
  declare export function assertTSInstantiationExpression(node: ?Object, opts?: ?Object): void
  declare export function isTSAsExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSAsExpression)
  declare export function assertTSAsExpression(node: ?Object, opts?: ?Object): void
  declare export function isTSSatisfiesExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSSatisfiesExpression)
  declare export function assertTSSatisfiesExpression(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeAssertion(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeAssertion)
  declare export function assertTSTypeAssertion(node: ?Object, opts?: ?Object): void
  declare export function isTSEnumDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSEnumDeclaration)
  declare export function assertTSEnumDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSEnumMember(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSEnumMember)
  declare export function assertTSEnumMember(node: ?Object, opts?: ?Object): void
  declare export function isTSModuleDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSModuleDeclaration)
  declare export function assertTSModuleDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSModuleBlock(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSModuleBlock)
  declare export function assertTSModuleBlock(node: ?Object, opts?: ?Object): void
  declare export function isTSImportType(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSImportType)
  declare export function assertTSImportType(node: ?Object, opts?: ?Object): void
  declare export function isTSImportEqualsDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSImportEqualsDeclaration)
  declare export function assertTSImportEqualsDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSExternalModuleReference(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSExternalModuleReference)
  declare export function assertTSExternalModuleReference(node: ?Object, opts?: ?Object): void
  declare export function isTSNonNullExpression(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSNonNullExpression)
  declare export function assertTSNonNullExpression(node: ?Object, opts?: ?Object): void
  declare export function isTSExportAssignment(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSExportAssignment)
  declare export function assertTSExportAssignment(node: ?Object, opts?: ?Object): void
  declare export function isTSNamespaceExportDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSNamespaceExportDeclaration)
  declare export function assertTSNamespaceExportDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeAnnotation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeAnnotation)
  declare export function assertTSTypeAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeParameterInstantiation(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeParameterInstantiation)
  declare export function assertTSTypeParameterInstantiation(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeParameterDeclaration(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeParameterDeclaration)
  declare export function assertTSTypeParameterDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeParameter(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeTSTypeParameter)
  declare export function assertTSTypeParameter(node: ?Object, opts?: ?Object): void
  declare export function isStandardized(node: ?Object, opts?: ?Object): boolean
  declare export function assertStandardized(node: ?Object, opts?: ?Object): void
  declare export function isExpression(node: ?Object, opts?: ?Object): boolean
  declare export function assertExpression(node: ?Object, opts?: ?Object): void
  declare export function isBinary(node: ?Object, opts?: ?Object): boolean
  declare export function assertBinary(node: ?Object, opts?: ?Object): void
  declare export function isScopable(node: ?Object, opts?: ?Object): boolean
  declare export function assertScopable(node: ?Object, opts?: ?Object): void
  declare export function isBlockParent(node: ?Object, opts?: ?Object): boolean
  declare export function assertBlockParent(node: ?Object, opts?: ?Object): void
  declare export function isBlock(node: ?Object, opts?: ?Object): boolean
  declare export function assertBlock(node: ?Object, opts?: ?Object): void
  declare export function isStatement(node: ?Object, opts?: ?Object): boolean
  declare export function assertStatement(node: ?Object, opts?: ?Object): void
  declare export function isTerminatorless(node: ?Object, opts?: ?Object): boolean
  declare export function assertTerminatorless(node: ?Object, opts?: ?Object): void
  declare export function isCompletionStatement(node: ?Object, opts?: ?Object): boolean
  declare export function assertCompletionStatement(node: ?Object, opts?: ?Object): void
  declare export function isConditional(node: ?Object, opts?: ?Object): boolean
  declare export function assertConditional(node: ?Object, opts?: ?Object): void
  declare export function isLoop(node: ?Object, opts?: ?Object): boolean
  declare export function assertLoop(node: ?Object, opts?: ?Object): void
  declare export function isWhile(node: ?Object, opts?: ?Object): boolean
  declare export function assertWhile(node: ?Object, opts?: ?Object): void
  declare export function isExpressionWrapper(node: ?Object, opts?: ?Object): boolean
  declare export function assertExpressionWrapper(node: ?Object, opts?: ?Object): void
  declare export function isFor(node: ?Object, opts?: ?Object): boolean
  declare export function assertFor(node: ?Object, opts?: ?Object): void
  declare export function isForXStatement(node: ?Object, opts?: ?Object): boolean
  declare export function assertForXStatement(node: ?Object, opts?: ?Object): void
  declare export function isFunction(node: ?Object, opts?: ?Object): boolean
  declare export function assertFunction(node: ?Object, opts?: ?Object): void
  declare export function isFunctionParent(node: ?Object, opts?: ?Object): boolean
  declare export function assertFunctionParent(node: ?Object, opts?: ?Object): void
  declare export function isPureish(node: ?Object, opts?: ?Object): boolean
  declare export function assertPureish(node: ?Object, opts?: ?Object): void
  declare export function isDeclaration(node: ?Object, opts?: ?Object): boolean
  declare export function assertDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isPatternLike(node: ?Object, opts?: ?Object): boolean
  declare export function assertPatternLike(node: ?Object, opts?: ?Object): void
  declare export function isLVal(node: ?Object, opts?: ?Object): boolean
  declare export function assertLVal(node: ?Object, opts?: ?Object): void
  declare export function isTSEntityName(node: ?Object, opts?: ?Object): boolean
  declare export function assertTSEntityName(node: ?Object, opts?: ?Object): void
  declare export function isLiteral(node: ?Object, opts?: ?Object): boolean
  declare export function assertLiteral(node: ?Object, opts?: ?Object): void
  declare export function isImmutable(node: ?Object, opts?: ?Object): boolean
  declare export function assertImmutable(node: ?Object, opts?: ?Object): void
  declare export function isUserWhitespacable(node: ?Object, opts?: ?Object): boolean
  declare export function assertUserWhitespacable(node: ?Object, opts?: ?Object): void
  declare export function isMethod(node: ?Object, opts?: ?Object): boolean
  declare export function assertMethod(node: ?Object, opts?: ?Object): void
  declare export function isObjectMember(node: ?Object, opts?: ?Object): boolean
  declare export function assertObjectMember(node: ?Object, opts?: ?Object): void
  declare export function isProperty(node: ?Object, opts?: ?Object): boolean
  declare export function assertProperty(node: ?Object, opts?: ?Object): void
  declare export function isUnaryLike(node: ?Object, opts?: ?Object): boolean
  declare export function assertUnaryLike(node: ?Object, opts?: ?Object): void
  declare export function isPattern(node: ?Object, opts?: ?Object): boolean
  declare export function assertPattern(node: ?Object, opts?: ?Object): void
  declare export function isClass(node: ?Object, opts?: ?Object): boolean
  declare export function assertClass(node: ?Object, opts?: ?Object): void
  declare export function isImportOrExportDeclaration(node: ?Object, opts?: ?Object): boolean
  declare export function assertImportOrExportDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isExportDeclaration(node: ?Object, opts?: ?Object): boolean
  declare export function assertExportDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isModuleSpecifier(node: ?Object, opts?: ?Object): boolean
  declare export function assertModuleSpecifier(node: ?Object, opts?: ?Object): void
  declare export function isAccessor(node: ?Object, opts?: ?Object): boolean
  declare export function assertAccessor(node: ?Object, opts?: ?Object): void
  declare export function isPrivate(node: ?Object, opts?: ?Object): boolean
  declare export function assertPrivate(node: ?Object, opts?: ?Object): void
  declare export function isFlow(node: ?Object, opts?: ?Object): boolean
  declare export function assertFlow(node: ?Object, opts?: ?Object): void
  declare export function isFlowType(node: ?Object, opts?: ?Object): boolean
  declare export function assertFlowType(node: ?Object, opts?: ?Object): void
  declare export function isFlowBaseAnnotation(node: ?Object, opts?: ?Object): boolean
  declare export function assertFlowBaseAnnotation(node: ?Object, opts?: ?Object): void
  declare export function isFlowDeclaration(node: ?Object, opts?: ?Object): boolean
  declare export function assertFlowDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isFlowPredicate(node: ?Object, opts?: ?Object): boolean
  declare export function assertFlowPredicate(node: ?Object, opts?: ?Object): void
  declare export function isEnumBody(node: ?Object, opts?: ?Object): boolean
  declare export function assertEnumBody(node: ?Object, opts?: ?Object): void
  declare export function isEnumMember(node: ?Object, opts?: ?Object): boolean
  declare export function assertEnumMember(node: ?Object, opts?: ?Object): void
  declare export function isJSX(node: ?Object, opts?: ?Object): boolean
  declare export function assertJSX(node: ?Object, opts?: ?Object): void
  declare export function isMiscellaneous(node: ?Object, opts?: ?Object): boolean
  declare export function assertMiscellaneous(node: ?Object, opts?: ?Object): void
  declare export function isTypeScript(node: ?Object, opts?: ?Object): boolean
  declare export function assertTypeScript(node: ?Object, opts?: ?Object): void
  declare export function isTSTypeElement(node: ?Object, opts?: ?Object): boolean
  declare export function assertTSTypeElement(node: ?Object, opts?: ?Object): void
  declare export function isTSType(node: ?Object, opts?: ?Object): boolean
  declare export function assertTSType(node: ?Object, opts?: ?Object): void
  declare export function isTSBaseType(node: ?Object, opts?: ?Object): boolean
  declare export function assertTSBaseType(node: ?Object, opts?: ?Object): void
  declare export function isModuleDeclaration(node: ?Object, opts?: ?Object): boolean
  declare export function assertModuleDeclaration(node: ?Object, opts?: ?Object): void
  declare export function isNumberLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeNumericLiteral)
  declare export function assertNumberLiteral(node: ?Object, opts?: ?Object): void
  declare export function isRegexLiteral(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeRegExpLiteral)
  declare export function assertRegexLiteral(node: ?Object, opts?: ?Object): void
  declare export function isRestProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeRestElement)
  declare export function assertRestProperty(node: ?Object, opts?: ?Object): void
  declare export function isSpreadProperty(node: ?Object, opts?: ?Object): boolean %checks (node instanceof BabelNodeSpreadElement)
  declare export function assertSpreadProperty(node: ?Object, opts?: ?Object): void
  declare export var VISITOR_KEYS: { [type: string]: string[] }
  declare export function assertNode(obj: any): void
  declare export function createTypeAnnotationBasedOnTypeof(type: 'string' | 'number' | 'undefined' | 'boolean' | 'function' | 'object' | 'symbol'): BabelNodeTypeAnnotation
  declare export function createUnionTypeAnnotation(types: Array<BabelNodeFlowType>): BabelNodeUnionTypeAnnotation
  declare export function createFlowUnionType(types: Array<BabelNodeFlowType>): BabelNodeUnionTypeAnnotation
  declare export function buildChildren(node: { children: Array<BabelNodeJSXText | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXElement | BabelNodeJSXFragment | BabelNodeJSXEmptyExpression> }): Array<BabelNodeJSXText | BabelNodeJSXExpressionContainer | BabelNodeJSXSpreadChild | BabelNodeJSXElement | BabelNodeJSXFragment>
  declare export function clone<T>(n: T): T;
  declare export function cloneDeep<T>(n: T): T;
  declare export function cloneDeepWithoutLoc<T>(n: T): T;
  declare export function cloneNode<T>(n: T, deep?: boolean, withoutLoc?: boolean): T;
  declare export function cloneWithoutLoc<T>(n: T): T;
  declare type CommentTypeShorthand = 'leading' | 'inner' | 'trailing'
  declare export function addComment<T: BabelNode>(node: T, type: CommentTypeShorthand, content: string, line?: boolean): T
  declare export function addComments<T: BabelNode>(node: T, type: CommentTypeShorthand, comments: Array<Comment>): T
  declare export function inheritInnerComments(node: BabelNode, parent: BabelNode): void
  declare export function inheritLeadingComments(node: BabelNode, parent: BabelNode): void
  declare export function inheritsComments<T: BabelNode>(node: T, parent: BabelNode): void
  declare export function inheritTrailingComments(node: BabelNode, parent: BabelNode): void
  declare export function removeComments<T: BabelNode>(node: T): T
  declare export function ensureBlock(node: BabelNode, key: string): BabelNodeBlockStatement
  declare export function toBindingIdentifierName(name?: ?string): string
  declare export function toBlock(node: BabelNodeStatement | BabelNodeExpression, parent?: BabelNodeFunction | null): BabelNodeBlockStatement
  declare export function toComputedKey(node: BabelNodeMethod | BabelNodeProperty, key?: BabelNodeExpression | BabelNodeIdentifier): BabelNodeExpression
  declare export function toExpression(node: BabelNodeExpressionStatement | BabelNodeExpression | BabelNodeClass | BabelNodeFunction): BabelNodeExpression
  declare export function toIdentifier(name?: ?string): string
  declare export function toKeyAlias(node: BabelNodeMethod | BabelNodeProperty, key?: BabelNode): string
  declare export function toStatement(node: BabelNodeStatement | BabelNodeClass | BabelNodeFunction | BabelNodeAssignmentExpression, ignore?: boolean): BabelNodeStatement | void
  declare export function valueToNode(value: any): BabelNodeExpression
  declare export function removeTypeDuplicates(types: Array<BabelNodeFlowType>): Array<BabelNodeFlowType>
  declare export function appendToMemberExpression(member: BabelNodeMemberExpression, append: BabelNode, computed?: boolean): BabelNodeMemberExpression
  declare export function inherits<T: BabelNode>(child: T, parent: BabelNode | null | void): T
  declare export function prependToMemberExpression(member: BabelNodeMemberExpression, prepend: BabelNodeExpression): BabelNodeMemberExpression
  declare export function removeProperties<T>(n: T, opts: ?{}): void;
  declare export function removePropertiesDeep<T>(n: T, opts: ?{}): T;
  declare export var getBindingIdentifiers: {
      (node: BabelNode, duplicates?: boolean, outerOnly?: boolean): { [key: string]: BabelNodeIdentifier | Array<BabelNodeIdentifier> },
      keys: { [type: string]: string[] }
    }
  declare export function getOuterBindingIdentifiers(node: BabelNode, duplicates?: boolean): { [key: string]: BabelNodeIdentifier | Array<BabelNodeIdentifier> }
  declare type TraversalAncestors = Array<{
    node: BabelNode,
    key: string,
    index?: number,
  }>;
  declare type TraversalHandler<T> = (BabelNode, TraversalAncestors, T) => void;
  declare type TraversalHandlers<T> = {
    enter?: TraversalHandler<T>,
    exit?: TraversalHandler<T>,
  };
  declare export function traverse<T>(n: BabelNode, TraversalHandler<T> | TraversalHandlers<T>, state?: T): void;
  declare export function traverseFast<T>(n: BabelNode, h: TraversalHandler<T>, state?: T): void;
  declare export function shallowEqual(actual: Object, expected: Object): boolean
  declare export function buildMatchMemberExpression(match: string, allowPartial?: boolean): (?BabelNode) => boolean
  declare export function is(type: string, n: BabelNode, opts: Object): boolean;
  declare export function isBinding(node: BabelNode, parent: BabelNode, grandparent?: BabelNode): boolean
  declare export function isBlockScoped(node: BabelNode): boolean
  declare export function isImmutable(node: BabelNode): boolean
  declare export function isLet(node: BabelNode): boolean
  declare export function isNode(node: ?Object): boolean
  declare export function isNodesEquivalent(a: any, b: any): boolean
  declare export function isPlaceholderType(placeholderType: string, targetType: string): boolean
  declare export function isReferenced(node: BabelNode, parent: BabelNode, grandparent?: BabelNode): boolean
  declare export function isScope(node: BabelNode, parent: BabelNode): boolean
  declare export function isSpecifierDefault(specifier: BabelNodeModuleSpecifier): boolean
  declare export function isType(nodetype: ?string, targetType: string): boolean
  declare export function isValidES3Identifier(name: string): boolean
  declare export function isValidES3Identifier(name: string): boolean
  declare export function isValidIdentifier(name: string): boolean
  declare export function isVar(node: BabelNode): boolean
  declare export function matchesPattern(node: ?BabelNode, match: string | Array<string>, allowPartial?: boolean): boolean
  declare export function validate(n: BabelNode, key: string, value: mixed): void;
}
