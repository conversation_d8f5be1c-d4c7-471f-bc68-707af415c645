# Upper Case First

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transforms the string with the first character in upper cased.

## Installation

```
npm install upper-case-first --save
```

## Usage

```js
import { upperCaseFirst } from "upper-case-first";

upperCaseFirst("test"); //=> "Test"
```

## License

MIT

[npm-image]: https://img.shields.io/npm/v/upper-case-first.svg?style=flat
[npm-url]: https://npmjs.org/package/upper-case-first
[downloads-image]: https://img.shields.io/npm/dm/upper-case-first.svg?style=flat
[downloads-url]: https://npmjs.org/package/upper-case-first
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/upper-case-first.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=upper-case-first
