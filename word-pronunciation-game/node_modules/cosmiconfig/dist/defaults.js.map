{"version": 3, "file": "defaults.js", "sourceRoot": "", "sources": ["../src/defaults.ts"], "names": [], "mappings": ";;;AAAA,uCAOmB;AAEnB,SAAgB,sBAAsB,CAAC,UAAkB;IACvD,OAAO;QACL,cAAc;QACd,IAAI,UAAU,IAAI;QAClB,IAAI,UAAU,SAAS;QACvB,IAAI,UAAU,SAAS;QACvB,IAAI,UAAU,QAAQ;QACtB,IAAI,UAAU,OAAO;QACrB,IAAI,UAAU,OAAO;QACrB,IAAI,UAAU,QAAQ;QACtB,IAAI,UAAU,QAAQ;QACtB,WAAW,UAAU,IAAI;QACzB,WAAW,UAAU,SAAS;QAC9B,WAAW,UAAU,SAAS;QAC9B,WAAW,UAAU,QAAQ;QAC7B,WAAW,UAAU,OAAO;QAC5B,WAAW,UAAU,OAAO;QAC5B,WAAW,UAAU,QAAQ;QAC7B,WAAW,UAAU,QAAQ;QAC7B,GAAG,UAAU,YAAY;QACzB,GAAG,UAAU,YAAY;QACzB,GAAG,UAAU,aAAa;QAC1B,GAAG,UAAU,aAAa;KAC3B,CAAC;AACJ,CAAC;AAxBD,wDAwBC;AAED,SAAgB,0BAA0B,CAAC,UAAkB;IAC3D,OAAO;QACL,cAAc;QACd,IAAI,UAAU,IAAI;QAClB,IAAI,UAAU,SAAS;QACvB,IAAI,UAAU,SAAS;QACvB,IAAI,UAAU,QAAQ;QACtB,IAAI,UAAU,OAAO;QACrB,IAAI,UAAU,OAAO;QACrB,IAAI,UAAU,QAAQ;QACtB,WAAW,UAAU,IAAI;QACzB,WAAW,UAAU,SAAS;QAC9B,WAAW,UAAU,SAAS;QAC9B,WAAW,UAAU,QAAQ;QAC7B,WAAW,UAAU,OAAO;QAC5B,WAAW,UAAU,OAAO;QAC5B,WAAW,UAAU,QAAQ;QAC7B,GAAG,UAAU,YAAY;QACzB,GAAG,UAAU,YAAY;QACzB,GAAG,UAAU,aAAa;KAC3B,CAAC;AACJ,CAAC;AArBD,gEAqBC;AAEY,QAAA,wBAAwB,GAAG;IACtC,QAAQ;IACR,aAAa;IACb,aAAa;IACb,YAAY;IACZ,WAAW;IACX,WAAW;IACX,YAAY;IACZ,YAAY;CACb,CAAC;AACW,QAAA,4BAA4B,GAAG;IAC1C,QAAQ;IACR,aAAa;IACb,aAAa;IACb,YAAY;IACZ,WAAW;IACX,WAAW;IACX,YAAY;CACb,CAAC;AAEF,wGAAwG;AAC3F,QAAA,gBAAgB,GAAG;IAC9B,cAAc;IACd,cAAc;IACd,qBAAqB;IACrB,qBAAqB;IACrB,oBAAoB;IACpB,mBAAmB;IACnB,mBAAmB;IACnB,oBAAoB;IACpB,oBAAoB;CACrB,CAAC;AAEF,+EAA+E;AAClE,QAAA,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1C,MAAM,EAAE,gBAAM;IACd,MAAM,EAAE,gBAAM;IACd,KAAK,EAAE,gBAAM;IACb,KAAK,EAAE,gBAAM;IACb,OAAO,EAAE,kBAAQ;IACjB,OAAO,EAAE,kBAAQ;IACjB,MAAM,EAAE,kBAAQ;IAChB,KAAK,EAAE,kBAAQ;CACP,CAAC,CAAC;AAEC,QAAA,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC;IAC9C,MAAM,EAAE,oBAAU;IAClB,KAAK,EAAE,oBAAU;IACjB,KAAK,EAAE,oBAAU;IACjB,OAAO,EAAE,kBAAQ;IACjB,OAAO,EAAE,kBAAQ;IACjB,MAAM,EAAE,kBAAQ;IAChB,KAAK,EAAE,kBAAQ;CACP,CAAC,CAAC"}