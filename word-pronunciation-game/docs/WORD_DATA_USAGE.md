# 单词数据使用指南

本项目提供了灵活的单词数据管理系统，支持本地数据和服务器数据的无缝切换。

## 文件结构

```
src/
├── data/
│   ├── words.json          # JSON格式的单词数据
│   └── words.ts           # 兼容性导出文件
├── services/
│   ├── wordService.ts     # 单词服务类
│   └── api.ts            # API服务类
├── examples/
│   └── WordServiceUsage.tsx # 使用示例组件
└── types/
    └── game.ts           # 类型定义
```

## 数据格式

### 单词对象结构
```typescript
interface Word {
  id: number
  word: string          // 英文单词
  image: string         // 图片URL
  pronunciation: string // 音标
  meaning: string       // 中文意思
}
```

### JSON数据格式
```json
{
  "words": [
    {
      "id": 1,
      "word": "apple",
      "image": "https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=400&fit=crop",
      "pronunciation": "/ˈæpl/",
      "meaning": "苹果"
    }
  ]
}
```

## 使用方法

### 1. 基本使用（本地数据）

```typescript
import { wordService } from '../services/wordService'

// 获取所有单词
const allWords = wordService.getAllWords()

// 获取随机单词
const randomWords = wordService.getRandomWords(5)

// 根据ID获取单词
const word = wordService.getWordById(1)

// 搜索单词
const searchResults = wordService.searchWords('apple')

// 按难度获取单词
const easyWords = wordService.getWordsByDifficulty('easy')
```

### 2. 服务器数据使用

```typescript
import { apiService } from '../services/api'

// 初始化数据（应用启动时调用）
await apiService.initializeData()

// 从服务器获取随机单词
const randomWords = await apiService.fetchRandomWords(10)

// 从服务器搜索单词
const searchResults = await apiService.searchWords('apple')

// 设置自定义服务器地址
apiService.setBaseUrl('https://your-server.com/api')
```

### 3. 在React组件中使用

```typescript
import React, { useState, useEffect } from 'react'
import { wordService } from '../services/wordService'
import { Word } from '../types/game'

const MyComponent: React.FC = () => {
  const [words, setWords] = useState<Word[]>([])

  useEffect(() => {
    // 获取随机单词
    const randomWords = wordService.getRandomWords(5)
    setWords(randomWords)
  }, [])

  return (
    <View>
      {words.map(word => (
        <View key={word.id}>
          <Text>{word.word} - {word.meaning}</Text>
          <Image src={word.image} />
        </View>
      ))}
    </View>
  )
}
```

## 服务器API接口规范

如果要从服务器获取数据，服务器应该提供以下接口：

### 获取所有单词
```
GET /api/words
Response: { "words": Word[] }
```

### 获取随机单词
```
GET /api/words/random?count=5
Response: { "words": Word[] }
```

### 根据ID获取单词
```
GET /api/words/:id
Response: { "word": Word }
```

### 搜索单词
```
GET /api/words/search?q=apple
Response: { "words": Word[] }
```

## 扩展单词数据

### 添加新单词到JSON文件

编辑 `src/data/words.json` 文件，在 `words` 数组中添加新的单词对象：

```json
{
  "id": 26,
  "word": "example",
  "image": "https://example.com/image.jpg",
  "pronunciation": "/ɪɡˈzæmpl/",
  "meaning": "例子"
}
```

### 图片资源建议

1. **Unsplash**: 高质量免费图片
   - 格式: `https://images.unsplash.com/photo-{id}?w=400&h=400&fit=crop`
   
2. **本地图片**: 放在 `src/assets/images/` 目录下
   - 格式: `require('../assets/images/apple.jpg')`

3. **CDN**: 使用稳定的图片CDN服务

## 最佳实践

1. **错误处理**: API服务会自动降级到本地数据
2. **缓存**: WordService使用单例模式，避免重复加载
3. **类型安全**: 使用TypeScript确保类型安全
4. **性能优化**: 大量数据时考虑分页加载
5. **图片优化**: 使用适当尺寸的图片，建议400x400px

## 迁移指南

### 从旧版本迁移

如果你之前使用的是 `src/data/words.ts` 中的数组数据：

```typescript
// 旧方式
import { gameWords, getRandomWords } from '../data/words'

// 新方式（推荐）
import { wordService } from '../services/wordService'
const gameWords = wordService.getAllWords()
const randomWords = wordService.getRandomWords(5)
```

旧的导入方式仍然可以使用，但建议迁移到新的服务类。
