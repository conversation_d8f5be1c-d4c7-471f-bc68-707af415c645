import React, { useState, useEffect } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { wordService } from '../services/wordService'
import { apiService } from '../services/api'
import { Word } from '../types/game'

/**
 * 单词服务使用示例组件
 * 展示如何使用 WordService 和 ApiService
 */
const WordServiceUsage: React.FC = () => {
  const [words, setWords] = useState<Word[]>([])
  const [randomWords, setRandomWords] = useState<Word[]>([])
  const [searchResults, setSearchResults] = useState<Word[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    // 组件挂载时初始化数据
    initializeData()
  }, [])

  /**
   * 初始化数据
   */
  const initializeData = async () => {
    setLoading(true)
    try {
      // 尝试从服务器初始化数据
      await apiService.initializeData()
      
      // 获取所有单词
      const allWords = wordService.getAllWords()
      setWords(allWords)
      
      // 获取随机单词
      const random = wordService.getRandomWords(5)
      setRandomWords(random)
    } catch (error) {
      console.error('Failed to initialize data:', error)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 获取新的随机单词
   */
  const getNewRandomWords = () => {
    const random = wordService.getRandomWords(5)
    setRandomWords(random)
  }

  /**
   * 从服务器获取随机单词
   */
  const getRandomWordsFromServer = async () => {
    setLoading(true)
    try {
      const random = await apiService.fetchRandomWords(5)
      setRandomWords(random)
    } catch (error) {
      console.error('Failed to get random words from server:', error)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 搜索单词
   */
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    if (query.trim()) {
      const results = wordService.searchWords(query)
      setSearchResults(results)
    } else {
      setSearchResults([])
    }
  }

  /**
   * 从服务器搜索单词
   */
  const handleServerSearch = async (query: string) => {
    if (!query.trim()) return
    
    setLoading(true)
    try {
      const results = await apiService.searchWords(query)
      setSearchResults(results)
    } catch (error) {
      console.error('Failed to search words from server:', error)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 按难度获取单词
   */
  const getWordsByDifficulty = (level: 'easy' | 'medium' | 'hard') => {
    const filteredWords = wordService.getWordsByDifficulty(level)
    setWords(filteredWords)
  }

  return (
    <View style={{ padding: '20px' }}>
      <Text style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>
        单词服务使用示例
      </Text>

      {loading && <Text>加载中...</Text>}

      {/* 统计信息 */}
      <View style={{ marginBottom: '20px' }}>
        <Text>总单词数: {wordService.getWordCount()}</Text>
        <Text>当前显示: {words.length} 个单词</Text>
      </View>

      {/* 操作按钮 */}
      <View style={{ marginBottom: '20px' }}>
        <Button onClick={() => setWords(wordService.getAllWords())}>
          显示所有单词
        </Button>
        <Button onClick={getNewRandomWords}>
          获取随机单词(本地)
        </Button>
        <Button onClick={getRandomWordsFromServer}>
          获取随机单词(服务器)
        </Button>
      </View>

      {/* 难度筛选 */}
      <View style={{ marginBottom: '20px' }}>
        <Text>按难度筛选:</Text>
        <Button onClick={() => getWordsByDifficulty('easy')}>简单</Button>
        <Button onClick={() => getWordsByDifficulty('medium')}>中等</Button>
        <Button onClick={() => getWordsByDifficulty('hard')}>困难</Button>
      </View>

      {/* 搜索功能 */}
      <View style={{ marginBottom: '20px' }}>
        <Text>搜索单词:</Text>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          placeholder="输入单词或中文意思"
          style={{ margin: '10px', padding: '5px' }}
        />
        <Button onClick={() => handleServerSearch(searchQuery)}>
          服务器搜索
        </Button>
      </View>

      {/* 搜索结果 */}
      {searchResults.length > 0 && (
        <View style={{ marginBottom: '20px' }}>
          <Text style={{ fontWeight: 'bold' }}>搜索结果:</Text>
          {searchResults.map(word => (
            <View key={word.id} style={{ margin: '5px', padding: '10px', border: '1px solid #ccc' }}>
              <Text>{word.word} - {word.meaning}</Text>
              <Text style={{ fontSize: '12px', color: '#666' }}>{word.pronunciation}</Text>
            </View>
          ))}
        </View>
      )}

      {/* 随机单词展示 */}
      {randomWords.length > 0 && (
        <View style={{ marginBottom: '20px' }}>
          <Text style={{ fontWeight: 'bold' }}>随机单词:</Text>
          {randomWords.map(word => (
            <View key={word.id} style={{ margin: '5px', padding: '10px', border: '1px solid #ccc' }}>
              <Text>{word.word} - {word.meaning}</Text>
              <Text style={{ fontSize: '12px', color: '#666' }}>{word.pronunciation}</Text>
              {word.image && (
                <img 
                  src={word.image} 
                  alt={word.word}
                  style={{ width: '100px', height: '100px', objectFit: 'cover' }}
                />
              )}
            </View>
          ))}
        </View>
      )}
    </View>
  )
}

export default WordServiceUsage
