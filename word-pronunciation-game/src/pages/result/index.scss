.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.celebration {
  text-align: center;
  margin-bottom: 60px;
  
  .trophy-icon {
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
  }
  
  .congratulations {
    font-size: 48px;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
}

.score-display {
  background: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 60px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  
  .score-text {
    font-size: 32px;
    color: #666;
    margin-bottom: 20px;
    display: block;
  }
  
  .score-number {
    font-size: 72px;
    font-weight: bold;
    color: #4A90E2;
    margin-bottom: 10px;
    display: block;
  }
  
  .percentage {
    font-size: 36px;
    color: #28a745;
    font-weight: bold;
    display: block;
  }
}

.actions {
  display: flex;
  gap: 30px;
  margin-bottom: 60px;
  
  .restart-btn, .share-btn {
    width: 160px;
    height: 80px;
    border-radius: 40px;
    font-size: 28px;
    font-weight: bold;
    border: none;
  }
  
  .restart-btn {
    background: #28a745;
    color: white;
  }
  
  .share-btn {
    background: #ffc107;
    color: #333;
  }
}

.promotion {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  
  .promotion-title {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    display: block;
  }
  
  .promotion-text {
    font-size: 28px;
    color: #666;
    margin-bottom: 30px;
    display: block;
  }
  
  .qr-code {
    width: 200px;
    height: 200px;
    border-radius: 15px;
    margin-bottom: 20px;
  }
  
  .qr-text {
    font-size: 24px;
    color: #666;
    display: block;
  }
}
