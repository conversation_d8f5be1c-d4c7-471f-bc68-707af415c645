import { Component } from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

interface ResultProps {
  score?: number
  totalWords?: number
}

export default class Result extends Component<ResultProps> {
  
  render() {
    const { score = 8, totalWords = 10 } = this.props
    const percentage = Math.round((score / totalWords) * 100)
    
    return (
      <View className='result-container'>
        <View className='celebration'>
          <Image 
            className='trophy-icon'
            src='https://via.placeholder.com/120x120'
            mode='aspectFit'
          />
          <Text className='congratulations'>恭喜完成！</Text>
        </View>

        <View className='score-display'>
          <Text className='score-text'>您的得分</Text>
          <Text className='score-number'>{score}/{totalWords}</Text>
          <Text className='percentage'>{percentage}%</Text>
        </View>

        <View className='actions'>
          <Button 
            className='restart-btn'
            onClick={this.restartGame}
          >
            再来一次
          </Button>
          
          <Button 
            className='share-btn'
            onClick={this.shareResult}
          >
            分享成绩
          </Button>
        </View>

        <View className='promotion'>
          <Text className='promotion-title'>想要更多练习？</Text>
          <Text className='promotion-text'>加入我们的英语学习社群</Text>
          <Image 
            className='qr-code'
            src='https://via.placeholder.com/200x200'
            mode='aspectFit'
          />
          <Text className='qr-text'>扫码添加客服微信</Text>
        </View>
      </View>
    )
  }

  restartGame = () => {
    Taro.redirectTo({
      url: '/pages/game/index'
    })
  }

  shareResult = () => {
    Taro.showShareMenu({
      withShareTicket: true
    })
  }
}
