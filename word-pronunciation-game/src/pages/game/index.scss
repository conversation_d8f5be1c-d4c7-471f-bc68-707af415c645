.game-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;

  .progress, .score {
    color: white;
    font-size: 32px;
    font-weight: bold;
  }
}

.word-display {
  background: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 60px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  text-align: center;
  width: 90%;
  max-width: 500px;

  .word-image {
    width: 100%;
    height: 350px;
    border-radius: 15px;
    object-fit: cover;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .word-text {
    font-size: 1px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }

  .word-pronunciation {
    font-size: 1px;
    color: #666;
    font-style: italic;
  }
}

.controls {
  margin-bottom: 40px;

  .record-btn {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: #ff6b6b;
    color: white;
    font-size: 32px;
    font-weight: bold;
    border: none;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    transition: all 0.3s ease;

    &.recording {
      background: #ff4757;
      transform: scale(1.1);
      box-shadow: 0 12px 35px rgba(255, 71, 87, 0.6);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.feedback {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 20px 30px;

  .feedback-text {
    font-size: 28px;
    color: #333;
    text-align: center;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .congrats-modal {
    background: white;
    border-radius: 30px;
    padding: 60px 40px;
    margin: 40px;
    text-align: center;
    max-width: 600px;
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);

    .congrats-title {
      font-size: 48px;
      font-weight: bold;
      color: #ff6b6b;
      margin-bottom: 20px;
    }

    .congrats-score {
      font-size: 32px;
      color: #333;
      margin-bottom: 20px;
      font-weight: 600;
    }

    .congrats-text {
      font-size: 28px;
      color: #666;
      line-height: 1.6;
      margin-bottom: 40px;
    }

    .qr-section {
      background: #f8f9fa;
      border-radius: 20px;
      padding: 40px 30px;
      margin-bottom: 40px;

      .qr-title {
        font-size: 30px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
      }

      .qr-code {
        width: 200px;
        height: 200px;
        margin-bottom: 20px;
        border-radius: 10px;
      }

      .qr-tip {
        font-size: 24px;
        color: #666;
        line-height: 1.4;
      }
    }

    .modal-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;

      .restart-btn, .result-btn {
        border: none;
        border-radius: 30px;
        padding: 25px 40px;
        font-size: 28px;
        font-weight: bold;
        min-width: 120px;
      }

      .restart-btn {
        background: #4CAF50;
        color: white;
      }

      .result-btn {
        background: #2196F3;
        color: white;
      }
    }
  }
}
