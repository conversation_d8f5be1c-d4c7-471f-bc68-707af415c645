import { Component } from 'react'
import { View, Text, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { Word, GameState } from '../../types/game'
import { getRandomWords } from '../../data/words'
import { voiceService } from '../../services/voice'
import './index.scss'

interface GamePageState {
  words: Word[]
  gameState: GameState
  feedback: string
  showCongratsModal: boolean
}

export default class Game extends Component<{}, GamePageState> {
  constructor(props) {
    super(props)
    this.state = {
      words: [],
      gameState: {
        currentWordIndex: 0,
        score: 0,
        totalWords: 0,
        isRecording: false,
        gameStatus: 'ready',
        attempts: 0
      },
      feedback: '',
      showCongratsModal: false
    }
  }

  componentDidMount() {
    this.initGame()
  }

  initGame = () => {
    const gameWords = getRandomWords(5) // 每局5个单词
    this.setState({
      words: gameWords,
      gameState: {
        currentWordIndex: 0,
        score: 0,
        totalWords: gameWords.length,
        isRecording: false,
        gameStatus: 'playing',
        attempts: 0
      },
      feedback: ''
    })
  }

  getCurrentWord = (): Word | null => {
    const { words, gameState } = this.state
    if (words.length === 0 || gameState.currentWordIndex >= words.length) {
      return null
    }
    return words[gameState.currentWordIndex]
  }

  startRecording = async () => {
    try {
      // 检查录音权限
      const hasPermission = await voiceService.checkRecordPermission()
      if (!hasPermission) {
        Taro.showToast({
          title: '需要录音权限才能使用语音识别',
          icon: 'none'
        })
        return
      }

      this.setState(prevState => ({
        gameState: { ...prevState.gameState, isRecording: true },
        feedback: '正在录音...'
      }))

      await voiceService.startRecord()
    } catch (error) {
      console.error('开始录音失败:', error)
      this.setState(prevState => ({
        gameState: { ...prevState.gameState, isRecording: false },
        feedback: '录音失败，请重试'
      }))
    }
  }

  stopRecording = async () => {
    try {
      const audioPath = await voiceService.stopRecord()
      this.setState(prevState => ({
        gameState: { ...prevState.gameState, isRecording: false },
        feedback: '正在识别...'
      }))

      const currentWord = this.getCurrentWord()
      if (!currentWord) return

      // 发送到后端进行ASR识别
      const result = await voiceService.sendToASR(audioPath, currentWord.word)

      if (result.success && result.text.toLowerCase() === currentWord.word.toLowerCase()) {
        // 识别正确
        this.setState({ feedback: '太棒了！发音正确！' })

        setTimeout(() => {
          const { gameState, words } = this.state
          const nextIndex = gameState.currentWordIndex + 1

          if (nextIndex >= words.length) {
            // 游戏完成
            this.setState(prevState => ({
              gameState: {
                ...prevState.gameState,
                currentWordIndex: nextIndex,
                score: prevState.gameState.score + 1,
                gameStatus: 'completed'
              },
              showCongratsModal: true
            }))
          } else {
            // 下一个单词
            this.setState(prevState => ({
              gameState: {
                ...prevState.gameState,
                currentWordIndex: nextIndex,
                score: prevState.gameState.score + 1,
                attempts: 0
              },
              feedback: ''
            }))
          }
        }, 1500)
      } else {
        // 识别错误
        const newAttempts = this.state.gameState.attempts + 1
        this.setState(prevState => ({
          gameState: { ...prevState.gameState, attempts: newAttempts }
        }))

        if (newAttempts >= 3) {
          this.setState({ feedback: `试试读: ${currentWord.pronunciation}` })
        } else {
          this.setState({ feedback: `发音不太对，再试试看！(${newAttempts}/3)` })
        }
      }
    } catch (error) {
      console.error('语音识别失败:', error)
      this.setState(prevState => ({
        gameState: { ...prevState.gameState, isRecording: false },
        feedback: '识别失败，请重试'
      }))
    }
  }

  restartGame = () => {
    this.setState({ showCongratsModal: false })
    this.initGame()
  }

  goToResult = () => {
    Taro.redirectTo({
      url: `/pages/result/index?score=${this.state.gameState.score}&total=${this.state.gameState.totalWords}`
    })
  }

  render() {
    const { gameState, feedback, showCongratsModal } = this.state
    const currentWord = this.getCurrentWord()

    if (!currentWord) {
      return (
        <View className='game-container'>
          <Text>加载中...</Text>
        </View>
      )
    }

    return (
      <View className='game-container'>
        <View className='header'>
          <Text className='progress'>进度: {gameState.currentWordIndex + 1}/{gameState.totalWords}</Text>
          <Text className='score'>得分: {gameState.score}</Text>
        </View>

        <View className='word-display'>
          <Image
            className='word-image'
            src={currentWord.image}
            mode='aspectFit'
          />
          {/* 隐藏中文和音标，只显示图片让用户猜测 */}
        </View>

        <View className='controls'>
          <Button
            className={`record-btn ${gameState.isRecording ? 'recording' : ''}`}
            onTouchStart={this.startRecording}
            onTouchEnd={this.stopRecording}
            disabled={gameState.isRecording}
          >
            {gameState.isRecording ? '录音中...' : '按住说话'}
          </Button>
        </View>

        {feedback && (
          <View className='feedback'>
            <Text className='feedback-text'>{feedback}</Text>
          </View>
        )}

        {/* 祝贺弹窗 */}
        {showCongratsModal && (
          <View className='modal-overlay'>
            <View className='congrats-modal'>
              <Text className='congrats-title'>🎉 恭喜完成!</Text>
              <Text className='congrats-score'>你的得分: {gameState.score} / {gameState.totalWords}</Text>
              <Text className='congrats-text'>
                想要提升英语口语？
                {'\n'}
                加入我们的英语学习社群！
              </Text>

              {/* 客服二维码区域 */}
              <View className='qr-section'>
                <Text className='qr-title'>扫码添加客服</Text>
                <Image
                  className='qr-code'
                  src='https://via.placeholder.com/200x200/4CAF50/white?text=客服二维码'
                  mode='aspectFit'
                />
                <Text className='qr-tip'>获取更多学习资源和专业指导</Text>
              </View>

              <View className='modal-buttons'>
                <Button className='restart-btn' onClick={this.restartGame}>
                  再玩一次
                </Button>
                <Button className='result-btn' onClick={this.goToResult}>
                  查看详情
                </Button>
              </View>
            </View>
          </View>
        )}
      </View>
    )
  }
}
