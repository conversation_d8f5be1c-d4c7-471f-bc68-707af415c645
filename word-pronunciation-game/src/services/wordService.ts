import { Word } from '../types/game'
import wordsData from '../data/words.json'

/**
 * 单词服务类
 * 负责管理单词数据的获取和处理
 */
export class WordService {
  private static instance: WordService
  private words: Word[] = []

  private constructor() {
    // 初始化时加载本地数据
    this.loadLocalWords()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): WordService {
    if (!WordService.instance) {
      WordService.instance = new WordService()
    }
    return WordService.instance
  }

  /**
   * 加载本地单词数据
   */
  private loadLocalWords(): void {
    this.words = wordsData.words as Word[]
  }

  /**
   * 从服务器获取单词数据
   * @param url 服务器API地址
   */
  public async loadWordsFromServer(url: string): Promise<Word[]> {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      
      // 假设服务器返回的数据格式与本地JSON相同
      if (data.words && Array.isArray(data.words)) {
        this.words = data.words
        return this.words
      } else {
        throw new Error('Invalid data format from server')
      }
    } catch (error) {
      console.error('Failed to load words from server:', error)
      // 如果服务器加载失败，使用本地数据
      this.loadLocalWords()
      return this.words
    }
  }

  /**
   * 获取所有单词
   */
  public getAllWords(): Word[] {
    return [...this.words]
  }

  /**
   * 获取随机单词组合
   * @param count 需要的单词数量
   */
  public getRandomWords(count: number = 5): Word[] {
    const shuffled = [...this.words].sort(() => Math.random() - 0.5)
    return shuffled.slice(0, Math.min(count, this.words.length))
  }

  /**
   * 根据ID获取单词
   * @param id 单词ID
   */
  public getWordById(id: number): Word | undefined {
    return this.words.find(word => word.id === id)
  }

  /**
   * 根据单词文本搜索
   * @param searchText 搜索文本
   */
  public searchWords(searchText: string): Word[] {
    const lowerSearchText = searchText.toLowerCase()
    return this.words.filter(word => 
      word.word.toLowerCase().includes(lowerSearchText) ||
      word.meaning.includes(searchText)
    )
  }

  /**
   * 获取单词总数
   */
  public getWordCount(): number {
    return this.words.length
  }

  /**
   * 按难度级别获取单词（基于单词长度）
   * @param level 难度级别：'easy' | 'medium' | 'hard'
   */
  public getWordsByDifficulty(level: 'easy' | 'medium' | 'hard'): Word[] {
    switch (level) {
      case 'easy':
        return this.words.filter(word => word.word.length <= 4)
      case 'medium':
        return this.words.filter(word => word.word.length > 4 && word.word.length <= 7)
      case 'hard':
        return this.words.filter(word => word.word.length > 7)
      default:
        return this.words
    }
  }
}

// 导出单例实例
export const wordService = WordService.getInstance()

// 兼容原有的导出方式
export const gameWords = wordService.getAllWords()
export const getRandomWords = (count: number = 5) => wordService.getRandomWords(count)
