import { wordService } from './wordService'
import { Word } from '../types/game'

/**
 * API 配置
 */
export const API_CONFIG = {
  // 将来可以配置为实际的服务器地址
  BASE_URL: 'https://your-api-server.com/api',
  ENDPOINTS: {
    WORDS: '/words',
    WORD_BY_ID: '/words/:id',
    RANDOM_WORDS: '/words/random',
    SEARCH_WORDS: '/words/search'
  }
}

/**
 * API 服务类
 * 处理与服务器的通信
 */
export class ApiService {
  private static instance: ApiService
  private baseUrl: string

  private constructor() {
    this.baseUrl = API_CONFIG.BASE_URL
  }

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService()
    }
    return ApiService.instance
  }

  /**
   * 设置 API 基础 URL
   * @param url 基础 URL
   */
  public setBaseUrl(url: string): void {
    this.baseUrl = url
  }

  /**
   * 通用请求方法
   * @param endpoint 端点
   * @param options 请求选项
   */
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
      },
    }

    const config = { ...defaultOptions, ...options }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error)
      throw error
    }
  }

  /**
   * 获取所有单词
   */
  public async fetchWords(): Promise<Word[]> {
    try {
      const data = await this.request<{ words: Word[] }>(API_CONFIG.ENDPOINTS.WORDS)
      return data.words
    } catch (error) {
      console.warn('Failed to fetch words from server, using local data')
      return wordService.getAllWords()
    }
  }

  /**
   * 获取随机单词
   * @param count 数量
   */
  public async fetchRandomWords(count: number = 5): Promise<Word[]> {
    try {
      const data = await this.request<{ words: Word[] }>(
        `${API_CONFIG.ENDPOINTS.RANDOM_WORDS}?count=${count}`
      )
      return data.words
    } catch (error) {
      console.warn('Failed to fetch random words from server, using local data')
      return wordService.getRandomWords(count)
    }
  }

  /**
   * 根据ID获取单词
   * @param id 单词ID
   */
  public async fetchWordById(id: number): Promise<Word | null> {
    try {
      const endpoint = API_CONFIG.ENDPOINTS.WORD_BY_ID.replace(':id', id.toString())
      const data = await this.request<{ word: Word }>(endpoint)
      return data.word
    } catch (error) {
      console.warn(`Failed to fetch word ${id} from server, using local data`)
      return wordService.getWordById(id) || null
    }
  }

  /**
   * 搜索单词
   * @param query 搜索关键词
   */
  public async searchWords(query: string): Promise<Word[]> {
    try {
      const data = await this.request<{ words: Word[] }>(
        `${API_CONFIG.ENDPOINTS.SEARCH_WORDS}?q=${encodeURIComponent(query)}`
      )
      return data.words
    } catch (error) {
      console.warn('Failed to search words from server, using local data')
      return wordService.searchWords(query)
    }
  }

  /**
   * 初始化数据
   * 尝试从服务器加载数据，失败则使用本地数据
   */
  public async initializeData(): Promise<void> {
    try {
      const words = await this.fetchWords()
      // 这里可以将服务器数据更新到 wordService 中
      console.log(`Successfully loaded ${words.length} words from server`)
    } catch (error) {
      console.log('Using local word data')
    }
  }
}

// 导出单例实例
export const apiService = ApiService.getInstance()

/**
 * 使用示例：
 * 
 * // 在应用启动时初始化数据
 * await apiService.initializeData()
 * 
 * // 获取随机单词
 * const randomWords = await apiService.fetchRandomWords(10)
 * 
 * // 搜索单词
 * const searchResults = await apiService.searchWords('apple')
 * 
 * // 设置自定义服务器地址
 * apiService.setBaseUrl('https://my-custom-server.com/api')
 */
