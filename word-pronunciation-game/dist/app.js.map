{"version": 3, "file": "app.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://word-pronunciation-game/._src_app.ts", "webpack://word-pronunciation-game/./src/app.ts?7071"], "sourcesContent": ["import { useLaunch } from '@tarojs/taro';\nimport './app.scss';\nfunction App({\n  children\n}) {\n  useLaunch(() => {\n    console.log('App launched.');\n  });\n\n  // children 是将要会渲染的页面\n  return children;\n}\nexport default App;", "import '@tarojs/plugin-platform-weapp/dist/runtime'\n\nimport { window } from '@tarojs/runtime'\nimport { createReactApp } from '@tarojs/plugin-framework-react/dist/runtime'\nimport { initPxTransform } from '@tarojs/taro'\n\nimport component from \"!!../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=app!./app.ts\"\n\nimport * as React from 'react'\nimport ReactDOM from 'react-dom'\n\nvar config = {\"pages\":[\"pages/index/index\",\"pages/game/index\",\"pages/result/index\"],\"window\":{\"backgroundTextStyle\":\"light\",\"navigationBarBackgroundColor\":\"#4A90E2\",\"navigationBarTitleText\":\"英语发音练习\",\"navigationBarTextStyle\":\"white\"},\"permission\":{\"scope.record\":{\"desc\":\"您的录音权限将用于语音识别功能\"}}};\nwindow.__taroAppConfig = config\nvar inst = App(createReactApp(component, React, ReactDOM, config))\n\ninitPxTransform({\n  designWidth: 750,\n  deviceRatio: {\"375\":2,\"640\":1.17,\"750\":1,\"828\":0.905},\n  baseFontSize: 20,\n  unitPrecision: undefined,\n  targetUnit: undefined\n})\n"], "names": [], "sourceRoot": ""}