{"version": 3, "file": "taro.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACvCA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACrCA;AACA;AACA;AACA;;;;;;;;;;;;ACHA;AACA;AACA;AACA", "sources": ["webpack://word-pronunciation-game/.._.._taro-components_mini_index.js", "webpack://word-pronunciation-game/.._src_components-react.ts", "webpack://word-pronunciation-game/._node_modules_@tarojs_webpack5-runner_dist_template_comp.js", "webpack://word-pronunciation-game/._node_modules_@tarojs_webpack5-runner_dist_template_custom-wrapper.js"], "sourcesContent": ["export const View = 'view'\nexport const Icon = 'icon'\nexport const Progress = 'progress'\nexport const RichText = 'rich-text'\nexport const Text = 'text'\nexport const Button = 'button'\nexport const Checkbox = 'checkbox'\nexport const CheckboxGroup = 'checkbox-group'\nexport const Form = 'form'\nexport const Input = 'input'\nexport const Label = 'label'\nexport const Picker = 'picker'\nexport const PickerView = 'picker-view'\nexport const PickerViewColumn = 'picker-view-column'\nexport const Radio = 'radio'\nexport const RadioGroup = 'radio-group'\nexport const Slider = 'slider'\nexport const Switch = 'switch'\nexport const CoverImage = 'cover-image'\nexport const Textarea = 'textarea'\nexport const CoverView = 'cover-view'\nexport const MovableArea = 'movable-area'\nexport const MovableView = 'movable-view'\nexport const ScrollView = 'scroll-view'\nexport const Swiper = 'swiper'\nexport const SwiperItem = 'swiper-item'\nexport const Navigator = 'navigator'\nexport const Audio = 'audio'\nexport const Camera = 'camera'\nexport const Image = 'image'\nexport const LivePlayer = 'live-player'\nexport const Video = 'video'\nexport const Canvas = 'canvas'\nexport const Ad = 'ad'\nexport const WebView = 'web-view'\nexport const Block = 'block'\nexport const Map = 'map'\nexport const Slot = 'slot'\nexport const NativeSlot = 'native-slot'\nexport const CustomWrapper = 'custom-wrapper'\n", null, "/* eslint-disable no-undef */\nimport { createRecursiveComponentConfig } from '@tarojs/runtime'\n// @ts-ignore\nComponent(createRecursiveComponentConfig())\n", "/* eslint-disable no-undef */\nimport { createRecursiveComponentConfig } from '@tarojs/runtime'\n// @ts-ignore\nComponent(createRecursiveComponentConfig('custom-wrapper'))\n"], "names": [], "sourceRoot": ""}