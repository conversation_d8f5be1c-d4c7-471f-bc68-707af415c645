"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["taro"],{

/***/ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Button: function() { return /* binding */ Button; },
/* harmony export */   Image: function() { return /* binding */ Image; },
/* harmony export */   Text: function() { return /* binding */ Text; },
/* harmony export */   View: function() { return /* binding */ View; }
/* harmony export */ });
/* unused harmony exports Ad, AdCustom, Audio, Block, Camera, Canvas, ChannelLive, ChannelVideo, Checkbox, CheckboxGroup, CoverImage, CoverView, CustomWrapper, DoubleTapGestureHandler, DraggableSheet, Editor, ForcePressGestureHandler, Form, FunctionalPageNavigator, GridBuilder, GridView, HorizontalDragGestureHandler, Icon, Input, KeyboardAccessory, Label, ListBuilder, ListView, LivePlayer, LivePusher, LongPressGestureHandler, Map, MatchMedia, MovableArea, MovableView, NativeSlot, NavigationBar, Navigator, NestedScrollBody, NestedScrollHeader, OfficialAccount, OpenContainer, OpenData, PageContainer, PageMeta, PanGestureHandler, Picker, PickerView, PickerViewColumn, Progress, Radio, RadioGroup, RichText, RootPortal, ScaleGestureHandler, ScrollView, ShareElement, Slider, Slot, Snapshot, Span, StickyHeader, StickySection, Swiper, SwiperItem, Switch, TapGestureHandler, Textarea, VerticalDragGestureHandler, Video, VoipRoom, WebView */
const View = 'view';
const Icon = 'icon';
const Progress = 'progress';
const RichText = 'rich-text';
const Text = 'text';
const Button = 'button';
const Checkbox = 'checkbox';
const CheckboxGroup = 'checkbox-group';
const Form = 'form';
const Input = 'input';
const Label = 'label';
const Picker = 'picker';
const PickerView = 'picker-view';
const PickerViewColumn = 'picker-view-column';
const Radio = 'radio';
const RadioGroup = 'radio-group';
const Slider = 'slider';
const Switch = 'switch';
const CoverImage = 'cover-image';
const Textarea = 'textarea';
const CoverView = 'cover-view';
const MovableArea = 'movable-area';
const MovableView = 'movable-view';
const ScrollView = 'scroll-view';
const Swiper = 'swiper';
const SwiperItem = 'swiper-item';
const Navigator = 'navigator';
const Audio = 'audio';
const Camera = 'camera';
const Image = 'image';
const LivePlayer = 'live-player';
const Video = 'video';
const Canvas = 'canvas';
const Ad = 'ad';
const WebView = 'web-view';
const Block = 'block';
const Map = 'map';
const Slot = 'slot';
const NativeSlot = 'native-slot';
const CustomWrapper = 'custom-wrapper';

// For React.createElement's type
const Editor = 'editor';
const MatchMedia = 'match-media';
const FunctionalPageNavigator = 'functional-page-navigator';
const LivePusher = 'live-pusher';
const OfficialAccount = 'official-account';
const OpenData = 'open-data';
const NavigationBar = 'navigation-bar';
const PageMeta = 'page-meta';
const VoipRoom = 'voip-room';
const AdCustom = 'ad-custom';
const PageContainer = 'page-container';
const ShareElement = 'share-element';
const KeyboardAccessory = 'keyboard-accessory';
const RootPortal = 'root-portal';
const ChannelLive = 'channel-live';
const ChannelVideo = 'channel-video';
const ListView = 'list-view';
const ListBuilder = 'list-builder';
const GridView = 'grid-view';
const GridBuilder = 'grid-builder';
const StickyHeader = 'sticky-header';
const StickySection = 'sticky-section';
const Snapshot = 'snapshot';
const Span = 'span';
const OpenContainer = 'open-container';
const DraggableSheet = 'draggable-sheet';
const NestedScrollHeader = 'nested-scroll-header';
const NestedScrollBody = 'nested-scroll-body';
const DoubleTapGestureHandler = 'double-tap-gesture-handler';
const ForcePressGestureHandler = 'force-press-gesture-handler';
const HorizontalDragGestureHandler = 'horizontal-drag-gesture-handler';
const LongPressGestureHandler = 'long-press-gesture-handler';
const PanGestureHandler = 'pan-gesture-handler';
const ScaleGestureHandler = 'scale-gesture-handler';
const TapGestureHandler = 'tap-gesture-handler';
const VerticalDragGestureHandler = 'vertical-drag-gesture-handler';


/***/ }),

/***/ "./node_modules/@tarojs/webpack5-runner/dist/template/comp.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tarojs/webpack5-runner/dist/template/comp.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "webpack/container/remote/@tarojs/runtime");
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* eslint-disable no-undef */

// @ts-ignore
Component((0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__.createRecursiveComponentConfig)());

/***/ }),

/***/ "./node_modules/@tarojs/webpack5-runner/dist/template/custom-wrapper.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@tarojs/webpack5-runner/dist/template/custom-wrapper.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "webpack/container/remote/@tarojs/runtime");
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* eslint-disable no-undef */

// @ts-ignore
Component((0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__.createRecursiveComponentConfig)('custom-wrapper'));

/***/ })

}]);
//# sourceMappingURL=taro.js.map