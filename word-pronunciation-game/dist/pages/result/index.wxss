/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[2]!./node_modules/resolve-url-loader/index.js!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[4]!./src/pages/result/index.scss ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.celebration {
  text-align: center;
  margin-bottom: 60rpx;
}

.celebration .trophy-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.celebration .congratulations {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.score-display {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.score-display .score-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.score-display .score-number {
  font-size: 72rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 10rpx;
  display: block;
}

.score-display .percentage {
  font-size: 36rpx;
  color: #28a745;
  font-weight: bold;
  display: block;
}

.actions {
  display: flex;
  gap: 30rpx;
  margin-bottom: 60rpx;
}

.actions .restart-btn, .actions .share-btn {
  width: 160rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.actions .restart-btn {
  background: #28a745;
  color: white;
}

.actions .share-btn {
  background: #ffc107;
  color: #333;
}

.promotion {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.promotion .promotion-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.promotion .promotion-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.promotion .qr-code {
  width: 200rpx;
  height: 200rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.promotion .qr-text {
  font-size: 24rpx;
  color: #666;
  display: block;
}
