{"version": 3, "file": "pages/result/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AChFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://word-pronunciation-game/./src/pages/result/index.tsx?ae42", "webpack://word-pronunciation-game/._src_pages_result_index.tsx"], "sourcesContent": ["import _defineProperty from \"/Users/<USER>/work/mampod/word_mp/word-pronunciation-game/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { Component } from 'react';\nimport { View, Text, Button, Image } from '@tarojs/components';\nimport Taro from '@tarojs/taro';\nimport './index.scss';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default class Result extends Component {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"restartGame\", () => {\n      Taro.redirectTo({\n        url: '/pages/game/index'\n      });\n    });\n    _defineProperty(this, \"shareResult\", () => {\n      Taro.showShareMenu({\n        withShareTicket: true\n      });\n    });\n  }\n  render() {\n    const {\n      score = 8,\n      totalWords = 10\n    } = this.props;\n    const percentage = Math.round(score / totalWords * 100);\n    return /*#__PURE__*/_jsxs(View, {\n      className: \"result-container\",\n      children: [/*#__PURE__*/_jsxs(View, {\n        className: \"celebration\",\n        children: [/*#__PURE__*/_jsx(Image, {\n          className: \"trophy-icon\",\n          src: \"https://via.placeholder.com/120x120\",\n          mode: \"aspectFit\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"congratulations\",\n          children: \"\\u606D\\u559C\\u5B8C\\u6210\\uFF01\"\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"score-display\",\n        children: [/*#__PURE__*/_jsx(Text, {\n          className: \"score-text\",\n          children: \"\\u60A8\\u7684\\u5F97\\u5206\"\n        }), /*#__PURE__*/_jsxs(Text, {\n          className: \"score-number\",\n          children: [score, \"/\", totalWords]\n        }), /*#__PURE__*/_jsxs(Text, {\n          className: \"percentage\",\n          children: [percentage, \"%\"]\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"actions\",\n        children: [/*#__PURE__*/_jsx(Button, {\n          className: \"restart-btn\",\n          onClick: this.restartGame,\n          children: \"\\u518D\\u6765\\u4E00\\u6B21\"\n        }), /*#__PURE__*/_jsx(Button, {\n          className: \"share-btn\",\n          onClick: this.shareResult,\n          children: \"\\u5206\\u4EAB\\u6210\\u7EE9\"\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"promotion\",\n        children: [/*#__PURE__*/_jsx(Text, {\n          className: \"promotion-title\",\n          children: \"\\u60F3\\u8981\\u66F4\\u591A\\u7EC3\\u4E60\\uFF1F\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"promotion-text\",\n          children: \"\\u52A0\\u5165\\u6211\\u4EEC\\u7684\\u82F1\\u8BED\\u5B66\\u4E60\\u793E\\u7FA4\"\n        }), /*#__PURE__*/_jsx(Image, {\n          className: \"qr-code\",\n          src: \"https://via.placeholder.com/200x200\",\n          mode: \"aspectFit\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"qr-text\",\n          children: \"\\u626B\\u7801\\u6DFB\\u52A0\\u5BA2\\u670D\\u5FAE\\u4FE1\"\n        })]\n      })]\n    });\n  }\n}", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/result/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"练习结果\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/result/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}