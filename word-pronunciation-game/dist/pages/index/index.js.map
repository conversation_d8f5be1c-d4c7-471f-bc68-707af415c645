{"version": 3, "file": "pages/index/index.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACnFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://word-pronunciation-game/./src/pages/index/index.tsx?63ff", "webpack://word-pronunciation-game/._src_pages_index_index.tsx"], "sourcesContent": ["import { View, Text, Image, Button } from '@tarojs/components';\nimport { useLoad } from '@tarojs/taro';\nimport Taro from '@tarojs/taro';\nimport './index.scss';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Index() {\n  useLoad(() => {\n    console.log('Page loaded.');\n  });\n  const startGame = () => {\n    Taro.navigateTo({\n      url: '/pages/game/index'\n    });\n  };\n  return /*#__PURE__*/_jsx(View, {\n    className: \"index\",\n    children: /*#__PURE__*/_jsxs(View, {\n      className: \"welcome-container\",\n      children: [/*#__PURE__*/_jsxs(View, {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsx(Image, {\n          className: \"logo\",\n          src: \"https://via.placeholder.com/120x120/4A90E2/white?text=\\uD83C\\uDFAF\",\n          mode: \"aspectFit\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"title\",\n          children: \"\\u82F1\\u8BED\\u53D1\\u97F3\\u7EC3\\u4E60\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"subtitle\",\n          children: \"\\u901A\\u8FC7\\u8BED\\u97F3\\u8BC6\\u522B\\u63D0\\u5347\\u4F60\\u7684\\u82F1\\u8BED\\u53E3\\u8BED\"\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"features\",\n        children: [/*#__PURE__*/_jsxs(View, {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsx(Text, {\n            className: \"feature-icon\",\n            children: \"\\uD83C\\uDFA4\"\n          }), /*#__PURE__*/_jsx(Text, {\n            className: \"feature-text\",\n            children: \"\\u8BED\\u97F3\\u8BC6\\u522B\"\n          })]\n        }), /*#__PURE__*/_jsxs(View, {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsx(Text, {\n            className: \"feature-icon\",\n            children: \"\\uD83D\\uDCDA\"\n          }), /*#__PURE__*/_jsx(Text, {\n            className: \"feature-text\",\n            children: \"\\u5355\\u8BCD\\u7EC3\\u4E60\"\n          })]\n        }), /*#__PURE__*/_jsxs(View, {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsx(Text, {\n            className: \"feature-icon\",\n            children: \"\\uD83C\\uDFC6\"\n          }), /*#__PURE__*/_jsx(Text, {\n            className: \"feature-text\",\n            children: \"\\u6210\\u7EE9\\u7EDF\\u8BA1\"\n          })]\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"actions\",\n        children: [/*#__PURE__*/_jsx(Button, {\n          className: \"start-btn\",\n          onClick: startGame,\n          children: \"\\u5F00\\u59CB\\u7EC3\\u4E60\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"tip\",\n          children: \"\\u70B9\\u51FB\\u5F00\\u59CB\\uFF0C\\u4F53\\u9A8C\\u667A\\u80FD\\u8BED\\u97F3\\u8BC6\\u522B\\u7EC3\\u4E60\"\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"promotion\",\n        children: [/*#__PURE__*/_jsx(Text, {\n          className: \"promotion-text\",\n          children: \"\\u60F3\\u8981\\u66F4\\u7CFB\\u7EDF\\u7684\\u82F1\\u8BED\\u5B66\\u4E60\\uFF1F\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"promotion-highlight\",\n          children: \"\\u52A0\\u5165\\u6211\\u4EEC\\u7684\\u82F1\\u8BED\\u5B66\\u4E60\\u793E\\u7FA4\"\n        })]\n      })]\n    })\n  });\n}", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"首页\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/index/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}