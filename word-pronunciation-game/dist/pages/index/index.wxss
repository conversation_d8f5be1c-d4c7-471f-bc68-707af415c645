/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[2]!./node_modules/resolve-url-loader/index.js!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[4]!./src/pages/index/index.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
.index {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}
.index .welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 90vh;
}
.index .welcome-container .header {
  text-align: center;
  margin-bottom: 80rpx;
}
.index .welcome-container .header .logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  border-radius: 60rpx;
}
.index .welcome-container .header .title {
  font-size: 56rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}
.index .welcome-container .header .subtitle {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  line-height: 1.6;
}
.index .welcome-container .features {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 80rpx;
}
.index .welcome-container .features .feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.index .welcome-container .features .feature-item .feature-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}
.index .welcome-container .features .feature-item .feature-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}
.index .welcome-container .actions {
  text-align: center;
  margin-bottom: 60rpx;
}
.index .welcome-container .actions .start-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 35rpx 80rpx;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 15rpx 40rpx rgba(255, 107, 107, 0.4);
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}
.index .welcome-container .actions .start-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 10rpx 30rpx rgba(255, 107, 107, 0.6);
}
.index .welcome-container .actions .tip {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}
.index .welcome-container .promotion {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.index .welcome-container .promotion .promotion-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 10rpx;
}
.index .welcome-container .promotion .promotion-highlight {
  font-size: 30rpx;
  color: #FFD700;
  font-weight: bold;
}
