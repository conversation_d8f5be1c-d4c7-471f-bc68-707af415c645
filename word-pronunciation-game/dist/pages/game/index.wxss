/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[2]!./node_modules/resolve-url-loader/index.js!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[4]!./src/pages/game/index.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
.game-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.header .progress, .header .score {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.word-display {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  text-align: center;
}

.word-display .word-image {
  width: 300rpx;
  height: 200rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.word-display .word-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.word-display .word-pronunciation {
  font-size: 28rpx;
  color: #666;
  font-style: italic;
}

.controls {
  margin-bottom: 40rpx;
}

.controls .record-btn {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: #ff6b6b;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
}

.controls .record-btn.recording {
  background: #ff4757;
  transform: scale(1.1);
  box-shadow: 0 12rpx 35rpx rgba(255, 71, 87, 0.6);
}

.controls .record-btn:active {
  transform: scale(0.95);
}

.feedback {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 20rpx 30rpx;
}

.feedback .feedback-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-overlay .congrats-modal {
  background: white;
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx;
  text-align: center;
  max-width: 600rpx;
  box-shadow: 0 30rpx 80rpx rgba(0, 0, 0, 0.2);
}

.modal-overlay .congrats-modal .congrats-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 20rpx;
}

.modal-overlay .congrats-modal .congrats-score {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 600;
}

.modal-overlay .congrats-modal .congrats-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40rpx;
}

.modal-overlay .congrats-modal .qr-section {
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
}

.modal-overlay .congrats-modal .qr-section .qr-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.modal-overlay .congrats-modal .qr-section .qr-code {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.modal-overlay .congrats-modal .qr-section .qr-tip {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.modal-overlay .congrats-modal .modal-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.modal-overlay .congrats-modal .modal-buttons .restart-btn, .modal-overlay .congrats-modal .modal-buttons .result-btn {
  border: none;
  border-radius: 30rpx;
  padding: 25rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  min-width: 120rpx;
}

.modal-overlay .congrats-modal .modal-buttons .restart-btn {
  background: #4CAF50;
  color: white;
}

.modal-overlay .congrats-modal .modal-buttons .result-btn {
  background: #2196F3;
  color: white;
}
