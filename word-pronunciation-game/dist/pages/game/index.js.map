{"version": 3, "file": "pages/game/index.js", "mappings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dA;AAUA;AAIA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA", "sources": ["webpack://word-pronunciation-game/./src/pages/game/index.tsx?3592", "webpack://word-pronunciation-game/._src_data_words.ts", "webpack://word-pronunciation-game/._src_pages_game_index.tsx", "webpack://word-pronunciation-game/._src_services_voice.ts"], "sourcesContent": ["import _defineProperty from \"/Users/<USER>/work/mampod/word_mp/word-pronunciation-game/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { Component } from 'react';\nimport { View, Text, Image, Button } from '@tarojs/components';\nimport Taro from '@tarojs/taro';\nimport { getRandomWords } from '../../data/words';\nimport { voiceService } from '../../services/voice';\nimport './index.scss';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default class Game extends Component {\n  constructor(props) {\n    super(props);\n    _defineProperty(this, \"initGame\", () => {\n      const gameWords = getRandomWords(5); // 每局5个单词\n      this.setState({\n        words: gameWords,\n        gameState: {\n          currentWordIndex: 0,\n          score: 0,\n          totalWords: gameWords.length,\n          isRecording: false,\n          gameStatus: 'playing',\n          attempts: 0\n        },\n        feedback: ''\n      });\n    });\n    _defineProperty(this, \"getCurrentWord\", () => {\n      const {\n        words,\n        gameState\n      } = this.state;\n      if (words.length === 0 || gameState.currentWordIndex >= words.length) {\n        return null;\n      }\n      return words[gameState.currentWordIndex];\n    });\n    _defineProperty(this, \"startRecording\", async () => {\n      try {\n        // 检查录音权限\n        const hasPermission = await voiceService.checkRecordPermission();\n        if (!hasPermission) {\n          Taro.showToast({\n            title: '需要录音权限才能使用语音识别',\n            icon: 'none'\n          });\n          return;\n        }\n        this.setState(prevState => ({\n          gameState: {\n            ...prevState.gameState,\n            isRecording: true\n          },\n          feedback: '正在录音...'\n        }));\n        await voiceService.startRecord();\n      } catch (error) {\n        console.error('开始录音失败:', error);\n        this.setState(prevState => ({\n          gameState: {\n            ...prevState.gameState,\n            isRecording: false\n          },\n          feedback: '录音失败，请重试'\n        }));\n      }\n    });\n    _defineProperty(this, \"stopRecording\", async () => {\n      try {\n        const audioPath = await voiceService.stopRecord();\n        this.setState(prevState => ({\n          gameState: {\n            ...prevState.gameState,\n            isRecording: false\n          },\n          feedback: '正在识别...'\n        }));\n        const currentWord = this.getCurrentWord();\n        if (!currentWord) return;\n\n        // 发送到后端进行ASR识别\n        const result = await voiceService.sendToASR(audioPath, currentWord.word);\n        if (result.success && result.text.toLowerCase() === currentWord.word.toLowerCase()) {\n          // 识别正确\n          this.setState({\n            feedback: '太棒了！发音正确！'\n          });\n          setTimeout(() => {\n            const {\n              gameState,\n              words\n            } = this.state;\n            const nextIndex = gameState.currentWordIndex + 1;\n            if (nextIndex >= words.length) {\n              // 游戏完成\n              this.setState(prevState => ({\n                gameState: {\n                  ...prevState.gameState,\n                  currentWordIndex: nextIndex,\n                  score: prevState.gameState.score + 1,\n                  gameStatus: 'completed'\n                },\n                showCongratsModal: true\n              }));\n            } else {\n              // 下一个单词\n              this.setState(prevState => ({\n                gameState: {\n                  ...prevState.gameState,\n                  currentWordIndex: nextIndex,\n                  score: prevState.gameState.score + 1,\n                  attempts: 0\n                },\n                feedback: ''\n              }));\n            }\n          }, 1500);\n        } else {\n          // 识别错误\n          const newAttempts = this.state.gameState.attempts + 1;\n          this.setState(prevState => ({\n            gameState: {\n              ...prevState.gameState,\n              attempts: newAttempts\n            }\n          }));\n          if (newAttempts >= 3) {\n            this.setState({\n              feedback: `试试读: ${currentWord.pronunciation}`\n            });\n          } else {\n            this.setState({\n              feedback: `发音不太对，再试试看！(${newAttempts}/3)`\n            });\n          }\n        }\n      } catch (error) {\n        console.error('语音识别失败:', error);\n        this.setState(prevState => ({\n          gameState: {\n            ...prevState.gameState,\n            isRecording: false\n          },\n          feedback: '识别失败，请重试'\n        }));\n      }\n    });\n    _defineProperty(this, \"restartGame\", () => {\n      this.setState({\n        showCongratsModal: false\n      });\n      this.initGame();\n    });\n    _defineProperty(this, \"goToResult\", () => {\n      Taro.redirectTo({\n        url: `/pages/result/index?score=${this.state.gameState.score}&total=${this.state.gameState.totalWords}`\n      });\n    });\n    this.state = {\n      words: [],\n      gameState: {\n        currentWordIndex: 0,\n        score: 0,\n        totalWords: 0,\n        isRecording: false,\n        gameStatus: 'ready',\n        attempts: 0\n      },\n      feedback: '',\n      showCongratsModal: false\n    };\n  }\n  componentDidMount() {\n    this.initGame();\n  }\n  render() {\n    const {\n      gameState,\n      feedback,\n      showCongratsModal\n    } = this.state;\n    const currentWord = this.getCurrentWord();\n    if (!currentWord) {\n      return /*#__PURE__*/_jsx(View, {\n        className: \"game-container\",\n        children: /*#__PURE__*/_jsx(Text, {\n          children: \"\\u52A0\\u8F7D\\u4E2D...\"\n        })\n      });\n    }\n    return /*#__PURE__*/_jsxs(View, {\n      className: \"game-container\",\n      children: [/*#__PURE__*/_jsxs(View, {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsxs(Text, {\n          className: \"progress\",\n          children: [\"\\u8FDB\\u5EA6: \", gameState.currentWordIndex + 1, \"/\", gameState.totalWords]\n        }), /*#__PURE__*/_jsxs(Text, {\n          className: \"score\",\n          children: [\"\\u5F97\\u5206: \", gameState.score]\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"word-display\",\n        children: [/*#__PURE__*/_jsx(Image, {\n          className: \"word-image\",\n          src: currentWord.image,\n          mode: \"aspectFit\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"word-text\",\n          children: currentWord.meaning\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"word-pronunciation\",\n          children: currentWord.pronunciation\n        })]\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"controls\",\n        children: /*#__PURE__*/_jsx(Button, {\n          className: `record-btn ${gameState.isRecording ? 'recording' : ''}`,\n          onTouchStart: this.startRecording,\n          onTouchEnd: this.stopRecording,\n          disabled: gameState.isRecording,\n          children: gameState.isRecording ? '录音中...' : '按住说话'\n        })\n      }), feedback && /*#__PURE__*/_jsx(View, {\n        className: \"feedback\",\n        children: /*#__PURE__*/_jsx(Text, {\n          className: \"feedback-text\",\n          children: feedback\n        })\n      }), showCongratsModal && /*#__PURE__*/_jsx(View, {\n        className: \"modal-overlay\",\n        children: /*#__PURE__*/_jsxs(View, {\n          className: \"congrats-modal\",\n          children: [/*#__PURE__*/_jsx(Text, {\n            className: \"congrats-title\",\n            children: \"\\uD83C\\uDF89 \\u606D\\u559C\\u5B8C\\u6210!\"\n          }), /*#__PURE__*/_jsxs(Text, {\n            className: \"congrats-score\",\n            children: [\"\\u4F60\\u7684\\u5F97\\u5206: \", gameState.score, \" / \", gameState.totalWords]\n          }), /*#__PURE__*/_jsxs(Text, {\n            className: \"congrats-text\",\n            children: [\"\\u60F3\\u8981\\u63D0\\u5347\\u82F1\\u8BED\\u53E3\\u8BED\\uFF1F\", '\\n', \"\\u52A0\\u5165\\u6211\\u4EEC\\u7684\\u82F1\\u8BED\\u5B66\\u4E60\\u793E\\u7FA4\\uFF01\"]\n          }), /*#__PURE__*/_jsxs(View, {\n            className: \"qr-section\",\n            children: [/*#__PURE__*/_jsx(Text, {\n              className: \"qr-title\",\n              children: \"\\u626B\\u7801\\u6DFB\\u52A0\\u5BA2\\u670D\"\n            }), /*#__PURE__*/_jsx(Image, {\n              className: \"qr-code\",\n              src: \"https://via.placeholder.com/200x200/4CAF50/white?text=\\u5BA2\\u670D\\u4E8C\\u7EF4\\u7801\",\n              mode: \"aspectFit\"\n            }), /*#__PURE__*/_jsx(Text, {\n              className: \"qr-tip\",\n              children: \"\\u83B7\\u53D6\\u66F4\\u591A\\u5B66\\u4E60\\u8D44\\u6E90\\u548C\\u4E13\\u4E1A\\u6307\\u5BFC\"\n            })]\n          }), /*#__PURE__*/_jsxs(View, {\n            className: \"modal-buttons\",\n            children: [/*#__PURE__*/_jsx(Button, {\n              className: \"restart-btn\",\n              onClick: this.restartGame,\n              children: \"\\u518D\\u73A9\\u4E00\\u6B21\"\n            }), /*#__PURE__*/_jsx(Button, {\n              className: \"result-btn\",\n              onClick: this.goToResult,\n              children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n            })]\n          })]\n        })\n      })]\n    });\n  }\n}", "import { Word } from '../types/game'\n\n// 预设单词数据（你可以根据需要调整这些单词）\nexport const gameWords: Word[] = [\n  {\n    id: 1,\n    word: 'apple',\n    image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=400&fit=crop',\n    pronunciation: '/ˈæpl/',\n    meaning: '苹果'\n  },\n  {\n    id: 2,\n    word: 'banana',\n    image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=400&fit=crop',\n    pronunciation: '/bəˈnænə/',\n    meaning: '香蕉'\n  },\n  {\n    id: 3,\n    word: 'orange',\n    image: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400&h=400&fit=crop',\n    pronunciation: '/ˈɔːrɪndʒ/',\n    meaning: '橙子'\n  },\n  {\n    id: 4,\n    word: 'grape',\n    image: 'https://images.unsplash.com/photo-1599819177406-6dea7eadbf81?w=400&h=400&fit=crop',\n    pronunciation: '/ɡreɪp/',\n    meaning: '葡萄'\n  },\n  {\n    id: 5,\n    word: 'strawberry',\n    image: 'https://images.unsplash.com/photo-1601004890684-d8cbf643f5f2?w=400&h=400&fit=crop',\n    pronunciation: '/ˈstrɔːberi/',\n    meaning: '草莓'\n  },\n  {\n    id: 6,\n    word: 'watermelon',\n    image: 'https://images.unsplash.com/photo-1587049016823-ba97f8330c4b?w=400&h=400&fit=crop',\n    pronunciation: '/ˈwɔːtərmelən/',\n    meaning: '西瓜'\n  },\n  {\n    id: 7,\n    word: 'pineapple',\n    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop',\n    pronunciation: '/ˈpaɪnæpl/',\n    meaning: '菠萝'\n  },\n  {\n    id: 8,\n    word: 'cherry',\n    image: 'https://images.unsplash.com/photo-1528821128474-27f963b062bf?w=400&h=400&fit=crop',\n    pronunciation: '/ˈtʃeri/',\n    meaning: '樱桃'\n  },\n  {\n    id: 9,\n    word: 'cat',\n    image: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=400&fit=crop',\n    pronunciation: '/kæt/',\n    meaning: '猫'\n  },\n  {\n    id: 10,\n    word: 'dog',\n    image: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=400&fit=crop',\n    pronunciation: '/dɔːɡ/',\n    meaning: '狗'\n  },\n  {\n    id: 11,\n    word: 'bird',\n    image: 'https://images.unsplash.com/photo-1444464666168-49d633b86797?w=400&h=400&fit=crop',\n    pronunciation: '/bɜːrd/',\n    meaning: '鸟'\n  },\n  {\n    id: 12,\n    word: 'fish',\n    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=400&fit=crop',\n    pronunciation: '/fɪʃ/',\n    meaning: '鱼'\n  },\n  {\n    id: 13,\n    word: 'car',\n    image: 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=400&h=400&fit=crop',\n    pronunciation: '/kɑːr/',\n    meaning: '汽车'\n  },\n  {\n    id: 14,\n    word: 'house',\n    image: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=400&fit=crop',\n    pronunciation: '/haʊs/',\n    meaning: '房子'\n  },\n  {\n    id: 15,\n    word: 'tree',\n    image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop',\n    pronunciation: '/triː/',\n    meaning: '树'\n  },\n  {\n    id: 16,\n    word: 'flower',\n    image: 'https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400&h=400&fit=crop',\n    pronunciation: '/ˈflaʊər/',\n    meaning: '花'\n  }\n]\n\n// 获取随机单词组合\nexport const getRandomWords = (count: number = 5): Word[] => {\n  const shuffled = [...gameWords].sort(() => Math.random() - 0.5)\n  return shuffled.slice(0, Math.min(count, gameWords.length))\n}\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/game/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"发音练习\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/game/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n", "import Taro from '@tarojs/taro'\nimport { ASRResponse } from '../types/game'\n\ndeclare const process: {\n  env: {\n    TARO_ENV?: string\n    NODE_ENV?: string\n  }\n}\n\nclass VoiceService {\n  private recorderManager: any\n  private isInitialized = false\n\n  constructor() {\n    this.initRecorder()\n  }\n\n  private initRecorder() {\n    if (typeof Taro !== 'undefined' && Taro.getRecorderManager) {\n      this.recorderManager = Taro.getRecorderManager()\n      this.isInitialized = true\n    }\n  }\n\n  // 开始录音\n  startRecord(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (!this.isInitialized) {\n        reject(new Error('录音功能未初始化'))\n        return\n      }\n\n      // 小程序录音配置\n      const options = {\n        duration: 10000, // 最长录音时间 10秒\n        sampleRate: 16000, // 采样率\n        numberOfChannels: 1, // 录音通道数\n        encodeBitRate: 48000, // 编码码率\n        format: 'mp3', // 音频格式\n        frameSize: 50 // 指定帧大小，单位 KB\n      }\n\n      this.recorderManager.onStart(() => {\n        console.log('开始录音')\n        resolve()\n      })\n\n      this.recorderManager.onError((err: any) => {\n        console.error('录音错误:', err)\n        reject(err)\n      })\n\n      this.recorderManager.start(options)\n    })\n  }\n\n  // 停止录音并获取结果\n  stopRecord(): Promise<string> {\n    return new Promise((resolve, reject) => {\n      if (!this.isInitialized) {\n        reject(new Error('录音功能未初始化'))\n        return\n      }\n\n      this.recorderManager.onStop((res: any) => {\n        console.log('录音结束:', res)\n        resolve(res.tempFilePath)\n      })\n\n      this.recorderManager.stop()\n    })\n  }\n\n  // 发送音频到后端进行ASR识别\n  async sendToASR(audioPath: string, targetWord: string): Promise<ASRResponse> {\n    try {\n      // 这里需要替换为你的实际后端ASR接口\n      const uploadResult = await Taro.uploadFile({\n        url: 'https://your-backend-api.com/asr', // 替换为你的后端接口\n        filePath: audioPath,\n        name: 'audio',\n        formData: {\n          target_word: targetWord\n        }\n      })\n\n      const response = JSON.parse(uploadResult.data)\n\n      return {\n        success: response.success,\n        text: response.text || '',\n        confidence: response.confidence || 0\n      }\n    } catch (error) {\n      console.error('ASR识别失败:', error)\n\n      // 开发阶段的模拟响应\n      if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {\n        return this.mockASRResponse(targetWord)\n      }\n\n      throw error\n    }\n  }\n\n  // 开发阶段的模拟ASR响应\n  private mockASRResponse(targetWord: string): ASRResponse {\n    // 模拟50%的成功率\n    const isCorrect = Math.random() > 0.5\n\n    return {\n      success: true,\n      text: isCorrect ? targetWord : 'wrong_word',\n      confidence: isCorrect ? 0.9 : 0.3\n    }\n  }\n\n  // 检查录音权限\n  async checkRecordPermission(): Promise<boolean> {\n    try {\n      const { authSetting } = await Taro.getSetting()\n\n      if (authSetting['scope.record'] === false) {\n        // 如果用户拒绝了权限，引导用户去设置页面\n        const { confirm } = await Taro.showModal({\n          title: '需要录音权限',\n          content: '语音识别功能需要录音权限，请在设置中开启',\n          confirmText: '去设置',\n          cancelText: '取消'\n        })\n\n        if (confirm) {\n          await Taro.openSetting()\n        }\n        return false\n      }\n\n      if (!authSetting['scope.record']) {\n        // 请求录音权限\n        await Taro.authorize({ scope: 'scope.record' })\n      }\n\n      return true\n    } catch (error) {\n      console.error('权限检查失败:', error)\n      return false\n    }\n  }\n}\n\nexport const voiceService = new VoiceService()\n"], "names": [], "sourceRoot": ""}