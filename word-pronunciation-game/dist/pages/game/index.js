"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/game/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/game/index!./src/pages/game/index.tsx":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/game/index!./src/pages/game/index.tsx ***!
  \**************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Game; }
/* harmony export */ });
/* harmony import */ var _Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/defineProperty.js */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "webpack/container/remote/react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _data_words__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../data/words */ "./src/data/words.ts");
/* harmony import */ var _services_voice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/voice */ "./src/services/voice.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "webpack/container/remote/react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);








class Game extends react__WEBPACK_IMPORTED_MODULE_0__.Component {
  constructor(props) {
    super(props);
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, "initGame", () => {
      const gameWords = (0,_data_words__WEBPACK_IMPORTED_MODULE_2__.getRandomWords)(5); // 每局5个单词
      this.setState({
        words: gameWords,
        gameState: {
          currentWordIndex: 0,
          score: 0,
          totalWords: gameWords.length,
          isRecording: false,
          gameStatus: 'playing',
          attempts: 0
        },
        feedback: ''
      });
    });
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, "getCurrentWord", () => {
      const {
        words,
        gameState
      } = this.state;
      if (words.length === 0 || gameState.currentWordIndex >= words.length) {
        return null;
      }
      return words[gameState.currentWordIndex];
    });
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, "startRecording", async () => {
      try {
        // 检查录音权限
        const hasPermission = await _services_voice__WEBPACK_IMPORTED_MODULE_3__.voiceService.checkRecordPermission();
        if (!hasPermission) {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showToast({
            title: '需要录音权限才能使用语音识别',
            icon: 'none'
          });
          return;
        }
        this.setState(prevState => ({
          gameState: {
            ...prevState.gameState,
            isRecording: true
          },
          feedback: '正在录音...'
        }));
        await _services_voice__WEBPACK_IMPORTED_MODULE_3__.voiceService.startRecord();
      } catch (error) {
        console.error('开始录音失败:', error);
        this.setState(prevState => ({
          gameState: {
            ...prevState.gameState,
            isRecording: false
          },
          feedback: '录音失败，请重试'
        }));
      }
    });
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, "stopRecording", async () => {
      try {
        const audioPath = await _services_voice__WEBPACK_IMPORTED_MODULE_3__.voiceService.stopRecord();
        this.setState(prevState => ({
          gameState: {
            ...prevState.gameState,
            isRecording: false
          },
          feedback: '正在识别...'
        }));
        const currentWord = this.getCurrentWord();
        if (!currentWord) return;

        // 发送到后端进行ASR识别
        const result = await _services_voice__WEBPACK_IMPORTED_MODULE_3__.voiceService.sendToASR(audioPath, currentWord.word);
        if (result.success && result.text.toLowerCase() === currentWord.word.toLowerCase()) {
          // 识别正确
          this.setState({
            feedback: '太棒了！发音正确！'
          });
          setTimeout(() => {
            const {
              gameState,
              words
            } = this.state;
            const nextIndex = gameState.currentWordIndex + 1;
            if (nextIndex >= words.length) {
              // 游戏完成
              this.setState(prevState => ({
                gameState: {
                  ...prevState.gameState,
                  currentWordIndex: nextIndex,
                  score: prevState.gameState.score + 1,
                  gameStatus: 'completed'
                },
                showCongratsModal: true
              }));
            } else {
              // 下一个单词
              this.setState(prevState => ({
                gameState: {
                  ...prevState.gameState,
                  currentWordIndex: nextIndex,
                  score: prevState.gameState.score + 1,
                  attempts: 0
                },
                feedback: ''
              }));
            }
          }, 1500);
        } else {
          // 识别错误
          const newAttempts = this.state.gameState.attempts + 1;
          this.setState(prevState => ({
            gameState: {
              ...prevState.gameState,
              attempts: newAttempts
            }
          }));
          if (newAttempts >= 3) {
            this.setState({
              feedback: `试试读: ${currentWord.pronunciation}`
            });
          } else {
            this.setState({
              feedback: `发音不太对，再试试看！(${newAttempts}/3)`
            });
          }
        }
      } catch (error) {
        console.error('语音识别失败:', error);
        this.setState(prevState => ({
          gameState: {
            ...prevState.gameState,
            isRecording: false
          },
          feedback: '识别失败，请重试'
        }));
      }
    });
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, "restartGame", () => {
      this.setState({
        showCongratsModal: false
      });
      this.initGame();
    });
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, "goToResult", () => {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().redirectTo({
        url: `/pages/result/index?score=${this.state.gameState.score}&total=${this.state.gameState.totalWords}`
      });
    });
    this.state = {
      words: [],
      gameState: {
        currentWordIndex: 0,
        score: 0,
        totalWords: 0,
        isRecording: false,
        gameStatus: 'ready',
        attempts: 0
      },
      feedback: '',
      showCongratsModal: false
    };
  }
  componentDidMount() {
    this.initGame();
  }
  render() {
    const {
      gameState,
      feedback,
      showCongratsModal
    } = this.state;
    const currentWord = this.getCurrentWord();
    if (!currentWord) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
        className: "game-container",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
          children: "\u52A0\u8F7D\u4E2D..."
        })
      });
    }
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
      className: "game-container",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
        className: "header",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
          className: "progress",
          children: ["\u8FDB\u5EA6: ", gameState.currentWordIndex + 1, "/", gameState.totalWords]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
          className: "score",
          children: ["\u5F97\u5206: ", gameState.score]
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
        className: "word-display",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Image, {
          className: "word-image",
          src: currentWord.image,
          mode: "aspectFit"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
          className: "word-text",
          children: currentWord.meaning
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
          className: "word-pronunciation",
          children: currentWord.pronunciation
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
        className: "controls",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Button, {
          className: `record-btn ${gameState.isRecording ? 'recording' : ''}`,
          onTouchStart: this.startRecording,
          onTouchEnd: this.stopRecording,
          disabled: gameState.isRecording,
          children: gameState.isRecording ? '录音中...' : '按住说话'
        })
      }), feedback && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
        className: "feedback",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
          className: "feedback-text",
          children: feedback
        })
      }), showCongratsModal && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
        className: "modal-overlay",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
          className: "congrats-modal",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
            className: "congrats-title",
            children: "\uD83C\uDF89 \u606D\u559C\u5B8C\u6210!"
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
            className: "congrats-score",
            children: ["\u4F60\u7684\u5F97\u5206: ", gameState.score, " / ", gameState.totalWords]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
            className: "congrats-text",
            children: ["\u60F3\u8981\u63D0\u5347\u82F1\u8BED\u53E3\u8BED\uFF1F", '\n', "\u52A0\u5165\u6211\u4EEC\u7684\u82F1\u8BED\u5B66\u4E60\u793E\u7FA4\uFF01"]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
            className: "qr-section",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
              className: "qr-title",
              children: "\u626B\u7801\u6DFB\u52A0\u5BA2\u670D"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Image, {
              className: "qr-code",
              src: "https://via.placeholder.com/200x200/4CAF50/white?text=\u5BA2\u670D\u4E8C\u7EF4\u7801",
              mode: "aspectFit"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Text, {
              className: "qr-tip",
              children: "\u83B7\u53D6\u66F4\u591A\u5B66\u4E60\u8D44\u6E90\u548C\u4E13\u4E1A\u6307\u5BFC"
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
            className: "modal-buttons",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Button, {
              className: "restart-btn",
              onClick: this.restartGame,
              children: "\u518D\u73A9\u4E00\u6B21"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.Button, {
              className: "result-btn",
              onClick: this.goToResult,
              children: "\u67E5\u770B\u8BE6\u60C5"
            })]
          })]
        })
      })]
    });
  }
}

/***/ }),

/***/ "./src/data/words.ts":
/*!***************************!*\
  !*** ./src/data/words.ts ***!
  \***************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getRandomWords: function() { return /* binding */ getRandomWords; }
/* harmony export */ });
/* unused harmony export gameWords */
// 预设单词数据（你可以根据需要调整这些单词）
const gameWords = [{
  id: 1,
  word: 'apple',
  image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=400&fit=crop',
  pronunciation: '/ˈæpl/',
  meaning: '苹果'
}, {
  id: 2,
  word: 'banana',
  image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=400&fit=crop',
  pronunciation: '/bəˈnænə/',
  meaning: '香蕉'
}, {
  id: 3,
  word: 'orange',
  image: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400&h=400&fit=crop',
  pronunciation: '/ˈɔːrɪndʒ/',
  meaning: '橙子'
}, {
  id: 4,
  word: 'grape',
  image: 'https://images.unsplash.com/photo-1599819177406-6dea7eadbf81?w=400&h=400&fit=crop',
  pronunciation: '/ɡreɪp/',
  meaning: '葡萄'
}, {
  id: 5,
  word: 'strawberry',
  image: 'https://images.unsplash.com/photo-1601004890684-d8cbf643f5f2?w=400&h=400&fit=crop',
  pronunciation: '/ˈstrɔːberi/',
  meaning: '草莓'
}, {
  id: 6,
  word: 'watermelon',
  image: 'https://images.unsplash.com/photo-1587049016823-ba97f8330c4b?w=400&h=400&fit=crop',
  pronunciation: '/ˈwɔːtərmelən/',
  meaning: '西瓜'
}, {
  id: 7,
  word: 'pineapple',
  image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop',
  pronunciation: '/ˈpaɪnæpl/',
  meaning: '菠萝'
}, {
  id: 8,
  word: 'cherry',
  image: 'https://images.unsplash.com/photo-1528821128474-27f963b062bf?w=400&h=400&fit=crop',
  pronunciation: '/ˈtʃeri/',
  meaning: '樱桃'
}, {
  id: 9,
  word: 'cat',
  image: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=400&fit=crop',
  pronunciation: '/kæt/',
  meaning: '猫'
}, {
  id: 10,
  word: 'dog',
  image: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=400&fit=crop',
  pronunciation: '/dɔːɡ/',
  meaning: '狗'
}, {
  id: 11,
  word: 'bird',
  image: 'https://images.unsplash.com/photo-1444464666168-49d633b86797?w=400&h=400&fit=crop',
  pronunciation: '/bɜːrd/',
  meaning: '鸟'
}, {
  id: 12,
  word: 'fish',
  image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=400&fit=crop',
  pronunciation: '/fɪʃ/',
  meaning: '鱼'
}, {
  id: 13,
  word: 'car',
  image: 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=400&h=400&fit=crop',
  pronunciation: '/kɑːr/',
  meaning: '汽车'
}, {
  id: 14,
  word: 'house',
  image: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=400&fit=crop',
  pronunciation: '/haʊs/',
  meaning: '房子'
}, {
  id: 15,
  word: 'tree',
  image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop',
  pronunciation: '/triː/',
  meaning: '树'
}, {
  id: 16,
  word: 'flower',
  image: 'https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400&h=400&fit=crop',
  pronunciation: '/ˈflaʊər/',
  meaning: '花'
}];

// 获取随机单词组合
const getRandomWords = (count = 5) => {
  const shuffled = [...gameWords].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, Math.min(count, gameWords.length));
};

/***/ }),

/***/ "./src/pages/game/index.tsx":
/*!**********************************!*\
  !*** ./src/pages/game/index.tsx ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "webpack/container/remote/@tarojs/runtime");
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_game_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/game/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/game/index!./src/pages/game/index.tsx");


var config = {"navigationBarTitleText":"发音练习"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_game_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"], 'pages/game/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_game_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_game_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_game_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_game_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"]);


/***/ }),

/***/ "./src/services/voice.ts":
/*!*******************************!*\
  !*** ./src/services/voice.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   voiceService: function() { return /* binding */ voiceService; }
/* harmony export */ });
/* harmony import */ var _Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/defineProperty.js */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);


class VoiceService {
  constructor() {
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])(this, "recorderManager", void 0);
    (0,_Users_dongyi_work_mampod_word_mp_word_pronunciation_game_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__["default"])(this, "isInitialized", false);
    this.initRecorder();
  }
  initRecorder() {
    if (typeof (_tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default()) !== 'undefined' && (_tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().getRecorderManager)) {
      this.recorderManager = _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().getRecorderManager();
      this.isInitialized = true;
    }
  }

  // 开始录音
  startRecord() {
    return new Promise((resolve, reject) => {
      if (!this.isInitialized) {
        reject(new Error('录音功能未初始化'));
        return;
      }

      // 小程序录音配置
      const options = {
        duration: 10000,
        // 最长录音时间 10秒
        sampleRate: 16000,
        // 采样率
        numberOfChannels: 1,
        // 录音通道数
        encodeBitRate: 48000,
        // 编码码率
        format: 'mp3',
        // 音频格式
        frameSize: 50 // 指定帧大小，单位 KB
      };
      this.recorderManager.onStart(() => {
        console.log('开始录音');
        resolve();
      });
      this.recorderManager.onError(err => {
        console.error('录音错误:', err);
        reject(err);
      });
      this.recorderManager.start(options);
    });
  }

  // 停止录音并获取结果
  stopRecord() {
    return new Promise((resolve, reject) => {
      if (!this.isInitialized) {
        reject(new Error('录音功能未初始化'));
        return;
      }
      this.recorderManager.onStop(res => {
        console.log('录音结束:', res);
        resolve(res.tempFilePath);
      });
      this.recorderManager.stop();
    });
  }

  // 发送音频到后端进行ASR识别
  async sendToASR(audioPath, targetWord) {
    try {
      // 这里需要替换为你的实际后端ASR接口
      const uploadResult = await _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().uploadFile({
        url: 'https://your-backend-api.com/asr',
        // 替换为你的后端接口
        filePath: audioPath,
        name: 'audio',
        formData: {
          target_word: targetWord
        }
      });
      const response = JSON.parse(uploadResult.data);
      return {
        success: response.success,
        text: response.text || '',
        confidence: response.confidence || 0
      };
    } catch (error) {
      console.error('ASR识别失败:', error);

      // 开发阶段的模拟响应
      if (typeof process !== 'undefined' && process.env && "development" === 'development') {
        return this.mockASRResponse(targetWord);
      }
      throw error;
    }
  }

  // 开发阶段的模拟ASR响应
  mockASRResponse(targetWord) {
    // 模拟50%的成功率
    const isCorrect = Math.random() > 0.5;
    return {
      success: true,
      text: isCorrect ? targetWord : 'wrong_word',
      confidence: isCorrect ? 0.9 : 0.3
    };
  }

  // 检查录音权限
  async checkRecordPermission() {
    try {
      const {
        authSetting
      } = await _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().getSetting();
      if (authSetting['scope.record'] === false) {
        // 如果用户拒绝了权限，引导用户去设置页面
        const {
          confirm
        } = await _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showModal({
          title: '需要录音权限',
          content: '语音识别功能需要录音权限，请在设置中开启',
          confirmText: '去设置',
          cancelText: '取消'
        });
        if (confirm) {
          await _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().openSetting();
        }
        return false;
      }
      if (!authSetting['scope.record']) {
        // 请求录音权限
        await _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().authorize({
          scope: 'scope.record'
        });
      }
      return true;
    } catch (error) {
      console.error('权限检查失败:', error);
      return false;
    }
  }
}
const voiceService = new VoiceService();

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/game/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map